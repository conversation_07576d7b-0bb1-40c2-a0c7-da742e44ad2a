import 'package:dartz/dartz.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:quycky/app/features/terms/data/datasources/terms_datasource.dart';
import 'package:quycky/app/features/terms/data/repositories/terms_repository_implementation.dart';
import 'package:quycky/core/usecase/errors/exceptions.dart';
import 'package:quycky/core/usecase/errors/failure.dart';
import 'package:quycky/core/usecase/usecase.dart';

import 'package:quycky/app/features/terms/data/models/current_terms_model.dart';

class MockTermsDatasource extends Mock implements ITermsDatasource {}

void main() {
  late ITermsDatasource datasource;
  late TermsRepositoryImplementation repository;

  setUp(() {
    datasource = MockTermsDatasource();
    repository = TermsRepositoryImplementation(datasource);
  });

  final tNoParams = NoParams();

  final tCurrentTermsModel = CurrentTermsModel(
      id: '2',
      version: '1.00',
      content: '',
      url: 'terms_ecc990f3ab.html',
      createdAt: DateTime.parse("2022-10-20T03:24:23.000Z"),
      updatedAt: DateTime.parse("2022-10-20T03:24:23.000Z"),
      pageUrl:
          'https://staging-api.quycky.co:2053/terms/terms_ecc990f3ab.html');
  test('Should get the current terms and condictions of use from server',
      () async {
    // Arrange
    when(() => datasource.getCurrentTerms())
        .thenAnswer((_) async => tCurrentTermsModel);
    // Act
    final result = await repository.getCurrentTerms();
    // Assert
    expect(result, Right(tCurrentTermsModel));
    verify(() => datasource.getCurrentTerms()).called(1);
  });
  test('Should return a Failure when call the datasource is unsuccessful',
      () async {
    // Arrange
    when(() => datasource.getCurrentTerms()).thenThrow(ServerException());
    // Act
    final result = await repository.getCurrentTerms();
    //Assert
    expect(result, Left(ServerFailure()));
    verify(() => datasource.getCurrentTerms()).called(1);
  });
}
