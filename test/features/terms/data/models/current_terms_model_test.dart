import 'package:flutter_test/flutter_test.dart';
import 'package:quycky/app/features/terms/data/models/current_terms_model.dart';
import 'package:quycky/app/features/terms/domain/entities/current_terms_entity.dart';

import '../../../../mocks/current_terms_mock.dart';

void main() {
  final tCurrentTermsModel = CurrentTermsModel(
      id: '2',
      version: '1.00',
      content: '',
      url: 'terms_ecc990f3ab.html',
      createdAt: DateTime.parse("2022-10-20T03:24:23.000Z"),
      updatedAt: DateTime.parse("2022-10-20T03:24:23.000Z"),
      pageUrl:
          'https://staging-api.quycky.co:2053/terms/terms_ecc990f3ab.html');
  test('Should be a subclass of CurrentTermsModel', () {
    expect(tCurrentTermsModel, isA<CurrentTermsEntity>());
  });
  test('Should return a valid model', () {
    // Arrange
    //final Map<String, dynamic> jsonMap = json.decode(currentTermsMock);
    // Act
    final result = CurrentTermsModel.fromJson(currentTermsMapMock);
    // Assert
    expect(result, tCurrentTermsModel);
  });

  test('Should return a json map containing the proper data', () {
    // Arrange
    // Act
    final result = tCurrentTermsModel.toJson();
    // Assert
    expect(result, currentTermsMapMock);
  });
}
