import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:quycky/app/features/terms/data/datasources/endpoints/terms_endpoints.dart';
import 'package:quycky/app/features/terms/data/datasources/terms_datasource.dart';
import 'package:quycky/app/features/terms/data/datasources/terms_datasource_implementation.dart';
import 'package:quycky/app/features/terms/data/models/current_terms_model.dart';
import 'package:quycky/core/http_client/http_client.dart';
import 'package:quycky/core/http_client/http_response.dart';
import 'package:quycky/core/usecase/errors/exceptions.dart';

import '../../../../mocks/current_terms_mock.dart';

class HttpClientMock extends Mock implements HttpClient {}

void main() {
  late ITermsDatasource datasource;
  late HttpClient client;

  setUp(() {
    client = HttpClientMock();
    datasource = TermsDatasourceImplementation(client);
  });
  String urlExcepted = TermsEndpoints.currentTerms();
  // "terms/newestTerm";
  void successMock() {
    when(() => client.get(any())).thenAnswer(
        (_) async => HttpResponse(data: currentTermsMapMock, statusCode: 200));
  }

  test('Should call the get method with correct url', () async {
    // Arrange
    successMock();
    // Act
    await datasource.getCurrentTerms();
    // Assert
    verify(() => client.get(urlExcepted)).called(1);
  });
  test('Should return a CurrentTermsModel when is successful', () async {
    // Arrange
    successMock();
    final tCurrentTermsModekExcepted = CurrentTermsModel(
        id: '2',
        version: '1.00',
        content: '',
        url: 'terms_ecc990f3ab.html',
        createdAt: DateTime.parse("2022-10-20T03:24:23.000Z"),
        updatedAt: DateTime.parse("2022-10-20T03:24:23.000Z"),
        pageUrl:
            'https://staging-api.quycky.co:2053/terms/terms_ecc990f3ab.html');
    // Act
    final result = await datasource.getCurrentTerms();
    // Assert
    expect(result, tCurrentTermsModekExcepted);
  });
  test('Should throw a ServerException when the call is unsuccessful',
      () async {
    // Arrange
    when(() => client.get(any())).thenAnswer((_) async =>
        HttpResponse(data: 'something went wrong', statusCode: 400));
    final tCurrentTermsModekExcepted = CurrentTermsModel(
        id: '2',
        version: '1.00',
        content: '',
        url: 'terms_ecc990f3ab.html',
        createdAt: DateTime.parse("2022-10-20T03:24:23.000Z"),
        updatedAt: DateTime.parse("2022-10-20T03:24:23.000Z"),
        pageUrl:
            'https://staging-api.quycky.co:2053/terms/terms_ecc990f3ab.html');
    // Act
    final result = datasource.getCurrentTerms();
    // Assert
    expect(() => result, throwsA(ServerException()));
  });
}
