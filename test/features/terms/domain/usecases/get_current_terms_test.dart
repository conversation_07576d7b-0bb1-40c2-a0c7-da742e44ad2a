import 'package:dartz/dartz.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:quycky/app/features/terms/domain/entities/current_terms_entity.dart';
import 'package:quycky/app/features/terms/domain/repositories/terms_repository.dart';
import 'package:quycky/app/features/terms/domain/usecases/get_current_terms.dart';
import 'package:quycky/core/usecase/errors/failure.dart';
import 'package:quycky/core/usecase/usecase.dart';

class MockTermsRepository extends Mock implements ITermsRepository {}

void main() {
  late ITermsRepository termsRepository;
  late GetCurrentTermsUseCase usecase;

  setUp(() {
    termsRepository = MockTermsRepository();
    usecase = GetCurrentTermsUseCase(termsRepository);
  });

  final tCurrentTerms = CurrentTermsEntity(
      id: '2',
      version: '1.00',
      content: '',
      url: 'terms_ecc990f3ab.html',
      createdAt: DateTime.parse("2022-10-20T03:24:23.000Z"),
      updatedAt: DateTime.parse("2022-10-20T03:24:23.000Z"),
      pageUrl:
          'https://staging-api.quycky.co:2053/terms/terms_ecc990f3ab.html');
  final tNoParams = NoParams();
  test('Should get the current terms and condictions of use from server',
      () async {
    // Arrange
    when(() => termsRepository.getCurrentTerms()).thenAnswer(
        (_) async => Right<Failure, CurrentTermsEntity>(tCurrentTerms));
    // Act
    final result = await usecase(tNoParams);
    //Assert
    expect(result, Right(tCurrentTerms));
    verify(() => termsRepository.getCurrentTerms()).called(1);
  });
  Failure tServerFailure = ServerFailure();
  test('Should return a Failure when don\'t succeed', () async {
    // Arrange
    when(() => termsRepository.getCurrentTerms()).thenAnswer(
        (_) async => Left<Failure, CurrentTermsEntity>(tServerFailure));
    // Act
    final result = await usecase(tNoParams);
    //Assert
    expect(result, Left(tServerFailure));
    verify(termsRepository.getCurrentTerms).called(1);
  });
}
