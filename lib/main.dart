import 'dart:io';

import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:get_storage/get_storage.dart';
import 'package:quycky/core/utils/app_env.dart';
import 'package:quycky/firebase_options.dart';

import 'app/modules/app_module.dart';
import 'app/presenter/app_widget.dart';
import './analytics.dart';

class MyHttpOverrides extends HttpOverrides {
  @override
  HttpClient createHttpClient(SecurityContext? context) {
    return super.createHttpClient(context)
      ..badCertificateCallback =
          (X509Certificate cert, String host, int port) => true;
  }
}
// static final facebookAppEvents = FacebookAppEvents();

void main() async {
  try {
    WidgetsFlutterBinding.ensureInitialized();
    await AppEnv.initialize();
    await Firebase.initializeApp(
        options: DefaultFirebaseOptions.currentPlatform);

    await GetStorage.init();
    // await Firebase.initializeApp(
    //   options: DefaultFirebaseOptions.currentPlatform,
    // );
    Analytics.initialize();
  } catch (e) {
    debugPrint("err->: $e");
  }
  runApp(ModularApp(module: AppModule(), child: const AppWidget())
      // ChangeNotifierProvider<ThemeStore>(create: (context) => ThemeStore(),
      //     child: ModularApp(module: AppModule(), child: AppWidget())),
      );
  // HttpOverrides.global = MyHttpOverrides();
}
