import 'package:equatable/equatable.dart';

class CurrentTermsEntity extends Equatable {
  final String id;
  final String version;
  final String content;
  final String url;
  DateTime createdAt;
  DateTime updatedAt;
  final String pageUrl;

  CurrentTermsEntity(
      {required this.id,
      required this.version,
      required this.content,
      required this.url,
      required this.createdAt,
      required this.updatedAt,
      required this.pageUrl});
  @override
  List<Object> get props =>
      [id, version, content, url, createdAt, updatedAt, pageUrl];
}
