// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:dartz/dartz.dart';

import 'package:quycky/app/features/terms/domain/repositories/terms_repository.dart';
import 'package:quycky/core/usecase/errors/failure.dart';
import 'package:quycky/core/usecase/usecase.dart';

class GetTermsHtmlUseCase implements UseCase<String, String> {
  final ITermsRepository repository;

  GetTermsHtmlUseCase(
    this.repository,
  );

  @override
  Future<Either<Failure, String>> call(String url) async {
    return await repository.getTermsHtml(url);
  }
}
