import 'package:flutter_triple/flutter_triple.dart';
import 'package:quycky/app/features/terms/domain/entities/current_terms_entity.dart';
import 'package:quycky/app/features/terms/domain/usecases/get_current_terms.dart';
import 'package:quycky/app/features/terms/domain/usecases/get_terms_html.dart';

class TermsStore extends Store<CurrentTermsEntity> {
  final GetCurrentTermsUseCase getCurrentTermsUseCase;
  final GetTermsHtmlUseCase getTermsHtmlUseCase;

  TermsStore(this.getCurrentTermsUseCase, this.getTermsHtmlUseCase)
      : super(CurrentTermsEntity(
            id: '',
            version: '',
            content: '',
            url: '',
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
            pageUrl: ''));

  getTermsHtml(CurrentTermsEntity data) async {
    setLoading(true);
    final result = await getTermsHtmlUseCase(data.pageUrl);
    result.fold((left) => null, (right) {
      update(CurrentTermsEntity(
          id: data.id,
          version: data.version,
          content: right,
          url: data.url,
          createdAt: data.createdAt,
          updatedAt: data.updatedAt,
          pageUrl: data.pageUrl));
    });
    setLoading(false);
  }

  getCurrentTerms() async {
    setLoading(true);
    final result = await getCurrentTermsUseCase(null);
    result.fold(
        (left) => {
              update(CurrentTermsEntity(
                  id: '',
                  version: '',
                  content: '',
                  url: '',
                  createdAt: DateTime.now(),
                  updatedAt: DateTime.now(),
                  pageUrl: ''))
            }, //setError(left)},
        (right) => getTermsHtml(right));
    setLoading(false);
  }
}
