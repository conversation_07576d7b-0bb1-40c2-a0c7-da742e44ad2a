import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_svg/svg.dart';
import 'package:flutter_triple/flutter_triple.dart';
import 'package:quycky/app/features/home/<USER>/controllers/question_temperature_slider_store.dart';
import 'package:quycky/app/theme/colors.dart';
import 'package:sleek_circular_slider/sleek_circular_slider.dart';
import 'dart:math' as math;

class QuestionTemperatureSlider extends StatefulWidget {
  final double width;
  final void Function(double)? onChange;
  final double initialValue;

  const QuestionTemperatureSlider(
      {super.key, required this.initialValue, this.width = 240, this.onChange});

  @override
  State<QuestionTemperatureSlider> createState() =>
      _QuestionTemperatureSlider();
}

class _QuestionTemperatureSlider extends State<QuestionTemperatureSlider> {
  double currentValue = 50;
  final QuestionTemperatureSliderStore _store =
      Modular.get<QuestionTemperatureSliderStore>();

  Widget getNullChild(double value) {
    return Container();
  }

  Widget getInnerWidget(double value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(50),
              color: CustomColors.salmon,
              // borderRadius: BorderRadius.all(
              //   Radius.circular(value / 6),
              // ),
            ),
            child: const Align(
                alignment: Alignment.center,
                child: Text("0°",
                    style: TextStyle(
                        fontFamily: 'Roboto',
                        fontSize: 18,
                        color: Colors.white)))),
        Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(50),
              color: CustomColors.orangeSoda,
              // borderRadius: BorderRadius.all(
              //   Radius.circular(value / 6),
              // ),
            ),
            child: const Align(
                alignment: Alignment.center,
                child: Text("100°",
                    style: TextStyle(
                        fontFamily: 'Roboto',
                        fontSize: 18,
                        color: Colors.white)))),
      ],
    );
  }

  Widget getRotatedQuyckyPin(double v) {
    v = v / 100;
    double angle = 189 - ((1 - v) * 1.08);
    return Transform.rotate(
      angle: -angle,
      origin: const Offset(0, -15),
      child: Padding(
        padding: const EdgeInsets.all(4.0),
        child: SizedBox(
            height: 214,
            child: SvgPicture.asset(
              'assets/img/svg/quycky_pin.svg',
            )),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 370,
      width: widget.width + 25,
      child: Stack(
        alignment: AlignmentDirectional.center,
        children: [
          Padding(
            padding: const EdgeInsets.only(top: 25),
            child: getInnerWidget(50),
          ),
          Positioned(
            top: 150,
            child: Container(
                width: widget.width,
                transform: Matrix4.translationValues(0, -75, 0),
                child: Transform.rotate(
                    angle: -math.pi,
                    child: SleekCircularSlider(
                        innerWidget: getNullChild,
                        initialValue: widget.initialValue,
                        appearance: CircularSliderAppearance(
                            customWidths: CustomSliderWidths(
                                progressBarWidth: 7,
                                trackWidth: 3,
                                handlerSize: 14),
                            customColors: CustomSliderColors(
                                shadowColor: CustomColors.orangeSoda,
                                dotColor: CustomColors.orangeSoda,
                                progressBarColor: CustomColors.orangeSoda,
                                trackColor: CustomColors.orangeSoda),
                            counterClockwise: true,
                            startAngle: 330,
                            angleRange: 120,
                            spinnerMode: false,
                            size: widget.width),
                        onChange: (double value) {
                          _store.setValue(value);
                        }))),
          ),
          Positioned(
            top: -15,
            child: Stack(
              children: [
                TripleBuilder(
                  store: _store,
                  builder: ((context, store) => getRotatedQuyckyPin(
                      double.parse(store.state.toString()))),
                ),
                Positioned(
                  top: -10,
                  left: 44,
                  child: SizedBox(
                      height: 214,
                      child: SvgPicture.asset(
                        'assets/img/svg/fire_2.svg',
                      )),
                )
              ],
            ),
          ),
        ],
      ),
    );
  }
}
