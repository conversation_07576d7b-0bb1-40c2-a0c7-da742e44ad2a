import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:quycky/app/features/home/<USER>/pages/widgets/promo_slide.dart';
import 'package:quycky/app/services/remote_config/remote_config_entity.dart';
import 'package:quycky/app/services/remote_config/remote_config_store.dart';
import 'package:sizer/sizer.dart';

class BottomIconData {
  final IconData iconData;
  final double size;

  const BottomIconData({required this.iconData, required this.size});
}

class PromotionSlides extends StatefulWidget {
  final Function() onClose;
  final String title;
  const PromotionSlides(
      {super.key, this.title = "PromotionSlides", required this.onClose});

  @override
  _PromotionSlidesState createState() => _PromotionSlidesState();
}

class _PromotionSlidesState extends State<PromotionSlides> {
  final PageController _pageController = PageController(viewportFraction: 0.8);
  final _remoteConfigStore = Modular.get<RemoteConfigStore>();

  bool isLoading = false;
  int loadingProgressIndex = 0;
  bool isSelectingQuiz = true;

  double getWidth() {
    double defaultValue = 200;
    double res = MediaQuery.of(context).size.width * 0.85;
    return res > defaultValue ? defaultValue : res;
  }

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  void handleGoTo(String route, {bool replaceRoute = false, dynamic data}) {
    if (replaceRoute) {
      Modular.to.pushReplacementNamed(route, arguments: data);
      return;
    }
    Modular.to.pushNamed(route, arguments: data);
  }

  Widget _buildPromoImage(BannerPromoRuleAndData prize) {
    return PromoSlide(
      promo: prize,
      onOpenPromo: widget.onClose,
    );
  }

  Widget _buildPromoSectionItem(BannerPromoRuleAndData prize) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [_buildPromoImage(prize)],
    );
  }

  Widget _buildPromoSection() {
    return _buildGlassOverlay(
      Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Expanded(
            child: PageView.builder(
              controller: _pageController,
              itemCount:
                  _remoteConfigStore.state.bannersPromoRulesAndData.length,
              itemBuilder: (context, index) {
                return AnimatedBuilder(
                  animation: _pageController,
                  builder: (context, child) {
                    double value = 1.0;

                    if (_pageController.position.haveDimensions) {
                      value = _pageController.page! - index;
                      value = (1 - (value.abs() * 0.3)).clamp(0.85, 1.0);
                    }

                    return Center(
                      child: Transform.scale(
                        scale: value,
                        child: _buildPromoSectionItem(_remoteConfigStore
                            .state.bannersPromoRulesAndData[index]),
                      ),
                    );
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGlassOverlay(Widget child) {
    return GestureDetector(
      onTap: () {
        widget.onClose();
      },
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 3.0, sigmaY: 3.0),
        child: SizedBox(width: 100.w, height: 100.h, child: child),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: SizedBox(
          height: 100.h,
          child: Stack(
            children: [
              _buildPromoSection(),
            ],
          )),
    );
  }
}
