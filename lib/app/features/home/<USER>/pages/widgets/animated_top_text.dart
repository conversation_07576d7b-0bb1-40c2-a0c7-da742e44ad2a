import 'dart:async';
import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

class AnimatedTopText extends StatefulWidget {
  final String playerName;
  const AnimatedTopText({super.key, this.playerName = 'PLAYER'});

  @override
  State<AnimatedTopText> createState() => _AnimatedTopTextState();
}

class _AnimatedTopTextState extends State<AnimatedTopText> {
  late final List<String> _texts;
  int _currentIndex = 0;

  @override
  void initState() {
    super.initState();

    _texts = [
      'WELCOME\n${widget.playerName.toUpperCase()}',
      'ARE YOU A ROMANTIC PLAYER?',
      'RED FLAGS?\nYOU SPOT THEM EARLY',
      'FIND YOUR DIMENSION!',
      'PLAY TO UNLOCK!'
    ];
    Timer.periodic(const Duration(seconds: 3), (Timer timer) {
      setState(() {
        _currentIndex = (_currentIndex + 1) % _texts.length;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 4.73.h,
      child: AnimatedSwitcher(
        duration: const Duration(seconds: 1),
        child: Text(
          _texts[_currentIndex],
          key: ValueKey<int>(_currentIndex),
          textAlign: TextAlign.center,
          style: const TextStyle(
              letterSpacing: 2.7,
              fontFamily: "Roboto",
              fontWeight: FontWeight.bold,
              color: Colors.white,
              fontSize: 12),
        ),
      ),
    );
  }
}
