import 'dart:async';
import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:quycky/analytics.dart';
import 'package:quycky/app/features/home/<USER>/store/promotion_store.dart';
import 'package:quycky/app/services/remote_config/remote_config_entity.dart';
import 'package:quycky/app/theme/colors.dart';
import 'package:quycky/app/widgets/button.dart';
import 'package:quycky/app/widgets/custom_image.dart';
import 'package:quycky/core/entities/promotion_entity.dart';
import 'package:quycky/core/utils/app_routes.dart';
import 'package:quycky/core/utils/assets_strings.dart';
import 'package:quycky/core/utils/quycky_icons_icons.dart';
import 'package:sizer/sizer.dart';
import 'package:url_launcher/url_launcher.dart';

class PromoSlide extends StatefulWidget {
  final BannerPromoRuleAndData promo;
  final Function() onOpenPromo;

  const PromoSlide({super.key, required this.promo, required this.onOpenPromo});

  @override
  State<PromoSlide> createState() => _PromoSlideState();
}

class _PromoSlideState extends State<PromoSlide> {
  final PromotionStore _promotionStore = Modular.get<PromotionStore>();
  PromotionEntity? _promotion;
  Timer? countdownTimer;
  Duration _remaingTime = Duration();

  @override
  void initState() {
    super.initState();
    _promotion = _promotionStore
        .getPromotionById(widget.promo.whenUserHasPlayed.toString());
    // PromotionEntity(
    //     identifier: '',
    //     name: '',
    //     url: '',
    //     status: 'claimed',
    //     startTime: DateTime.now(),
    //     minutesToClose: 28);
    startTimer();
  }

  Future<void> _logOpenPromoEvent() async {
    Analytics.instance.logEvent(name: 'claim_prize_click');
  }

  void _openPromo() {
    _logOpenPromoEvent();
    if (_promotion == null ||
        _promotion!.url.isEmpty ||
        _promotion!.status != 'waiting') {
      return;
    }
    Uri promoUri = Uri.parse(_promotion!.url);
    if (promoUri.queryParameters.isNotEmpty) {
      promoUri = promoUri.replace(
        queryParameters: {
          ...promoUri.queryParameters,
          'promoId': _promotion!.identifier,
        },
      );
    } else {
      promoUri = promoUri.replace(
        queryParameters: {
          'promoId': _promotion!.identifier,
        },
      );
    }
    launchUrl(promoUri, mode: LaunchMode.externalApplication);
    widget.onOpenPromo();
  }

  void closePromotion() {
    _promotion = _promotion!.copyWith(
      status: 'expired',
    );
    _promotionStore.changePromotionData(_promotion!);
    setState(() {});
    // _promotionStore.removePromotion(widget.promotion.identifier);
  }

  void stopTimer() {
    countdownTimer!.cancel();
  }

  void resetTimer() {
    stopTimer();
    _remaingTime = Duration();
  }

  void setCountDown() {
    final seconds = _remaingTime.inSeconds - 1;
    if (seconds <= 0) {
      _remaingTime = Duration(seconds: 0);
      countdownTimer!.cancel();
      closePromotion();
    }
    _remaingTime = Duration(seconds: seconds);

    setState(() {});
  }

  String formattedDigits(int n) => n.toString().padLeft(2, '0');

  String getCurrentTime() {
    final minutes = formattedDigits(_remaingTime.inMinutes.remainder(60));
    final seconds = formattedDigits(_remaingTime.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }

  void startTimer() {
    if (_promotion == null ||
        _promotion!.minutesToClose == 0 ||
        _promotion!.status != 'waiting') {
      return;
    }

    final timeDifference = DateTime.now().difference(_promotion!.startTime);
    if (timeDifference.inMinutes >= _promotion!.minutesToClose) {
      closePromotion();
      return;
    }
    int remainderSeconds =
        (_promotion!.minutesToClose * 60) - timeDifference.inSeconds;
    _remaingTime = Duration(seconds: remainderSeconds);
    countdownTimer =
        Timer.periodic(const Duration(seconds: 1), (_) => setCountDown());
  }

  Widget _buildTimer() {
    if (_promotion == null ||
        _promotion!.minutesToClose == 0 ||
        _promotion!.status != 'waiting') {
      return Container();
    }
    return Center(
      child: Padding(
        padding: EdgeInsets.only(top: 3.45.h),
        child: Text(
          '${getCurrentTime()} MIN',
          style: TextStyle(
            color: Colors.white,
            decoration: TextDecoration.none,
            fontFamily: 'Roboto',
            fontSize: 14,
            letterSpacing: 1,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  void _handleOpenRulesPage() {
    if (_promotion == null ||
        _promotion!.url.isEmpty ||
        _promotion!.status != 'waiting') {
      return;
    }
    Uri uriToOpen = Uri.parse('https://www.quycky.app/prize-rules');

    launchUrl(uriToOpen, mode: LaunchMode.externalApplication);
  }

  Widget _buildClaimPrizeOverlay() {
    if (_promotion == null) {
      // || _promotion!.status != 'waiting') {
      return Container();
    }

    return SizedBox(
      width: 76.92.w,
      height: 50.35.h,
      child: Column(mainAxisAlignment: MainAxisAlignment.end, children: [
        Container(
            margin: EdgeInsets.only(bottom: 1.5.h),
            height: 5.67.h,
            width: 58.79.w,
            child: Button(
                autoSized: true, onPressed: _openPromo, text: 'CLAIM PRIZE')),
        Button(
          onPressed: _handleOpenRulesPage,
          circleButton: true,
          circleButtonColor: Colors.transparent,
          shadowColor: Colors.transparent,
          child: Icon(
            QuyckyIcons.question_circle,
            color: Colors.white,
            size: 3.h,
          ),
        ),
        SizedBox(
          height: 2.h,
        ),
      ]),
    );
  }

  Widget _buildPlayMoreToUnlockOverlay() {
    if (_promotion != null) {
      return Container();
    }

    return GestureDetector(
      onTap: () => Modular.to.pushNamed(AppRoutes.gameLobby()),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(18.64),
        child: Stack(
          children: [
            _buildGlassOverlay(),
            SizedBox(
              width: 76.92.w,
              height: 50.35.h,
              child: CustomImage(Assets.svg.overlay.playMoreToUnlock),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPrizeClaimedOverlay() {
    if (_promotion == null || _promotion!.status != 'claimed') {
      return Container();
    }

    return ClipRRect(
      borderRadius: BorderRadius.circular(18.64),
      child: Stack(
        children: [
          _buildGlassOverlay(),
          SizedBox(
            width: 76.92.w,
            height: 50.35.h,
            child: CustomImage(Assets.svg.overlay.prizeClaimed),
          ),
        ],
      ),
    );
  }

  Widget _buildPrizeExpiredOverlay() {
    if (_promotion == null || _promotion!.status != 'expired') {
      return Container();
    }

    return ClipRRect(
      borderRadius: BorderRadius.circular(18.64),
      child: Stack(
        children: [
          _buildGlassOverlay(),
          SizedBox(
            width: 76.92.w,
            height: 50.35.h,
            child: CustomImage(Assets.svg.overlay.expired),
          ),
        ],
      ),
    );
  }

  Widget _buildGlassOverlay() {
    return ClipRRect(
        borderRadius: BorderRadius.circular(18.64),
        clipBehavior: Clip.antiAlias,
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 3.0, sigmaY: 3.0),
          child: Container(
            width: 76.92.w,
            height: 50.35.h,
            decoration: BoxDecoration(color: Colors.white.withAlpha(85)),
          ),
        ));
  }

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(18.64),
      child: Stack(
        alignment: Alignment.center,
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(18.64),
            child: Container(
              width: 76.92.w,
              height: 50.35.h,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    CustomColors.orangeSoda,
                    Colors.white.withAlpha(235),
                  ],
                ),
              ),
              child: CustomImage(
                widget.promo.imageUrl,
              ),
            ),
          ),
          // _build
          Positioned(top: 0, left: 0, right: 0, child: _buildTimer()),
          _buildClaimPrizeOverlay(),
          _buildPlayMoreToUnlockOverlay(),
          _buildPrizeExpiredOverlay(),
          _buildPrizeClaimedOverlay(),
        ],
      ),
    );
  }
}
