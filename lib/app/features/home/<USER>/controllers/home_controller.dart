// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:quycky/analytics.dart';

import 'package:quycky/app/features/home/<USER>/store/promotion_store.dart';
import 'package:quycky/core/entities/promotion_entity.dart';

class HomeController {
  final PromotionStore _promotionStore;
  HomeController(
    this._promotionStore,
  );
  void logPromotionShowEvent(PromotionEntity promotion) async {
    await Analytics.instance
        .logEvent(name: 'prize_icon_click'); //'promo_endgame0_show');
    // _promotionStore.changePromotionData(promotion.copyWith(status: 'claimed'));
  }

  void logPromotionOpenedEvent() async {
    await Analytics.instance.logEvent(name: 'promo_endgame0_open');
  }

  void logPromotionLauchedEvent() async {
    await Analytics.instance.logEvent(name: 'promo_endgame0_launch');
  }
}
