import 'dart:convert';

import 'package:quycky/app/features/game/domain/entities/game_entity.dart';
import 'package:quycky/core/services/storage/storage_client.dart';
import 'package:quycky/core/utils/app_storage_keys.dart';
import 'package:quycky/core/utils/type_converters.dart';

class GameStorage {
  final StorageClient _storage;
  GameStorage(this._storage);

  Future<bool> setGameRoomWaiting({String roomName = ''}) async {
    return await _storage.write(AppStorageKeys.gameRoomWaiting, roomName);
  }

  Future<String> getGameRoomWaiting() async {
    return (await _storage.read(AppStorageKeys.gameRoomWaiting)) ?? '';
  }

  Future<bool> setNumberOfGamesPlayed({int played = 0}) async {
    return await _storage.write(
        AppStorageKeys.numberOfGamesPlayed, played.toString());
  }

  Future<int> getNumberOfGamesPlayed() async {
    return dynamicToInt(
        await _storage.read(AppStorageKeys.numberOfGamesPlayed));
  }

  Future<bool> setLastGameParams(
      {Map<String, dynamic> params = const {
        "game_mode": null,
        "quiz_id": null
      }}) async {
    try {
      return await _storage.write(
          AppStorageKeys.lastGameParams,
          jsonEncode({
            "game_mode": params["game_mode"]?.label,
            "quiz_id": params["quiz_id"]
          }));
    } catch (e) {
      print("==>$e");
    }
    return false;
  }

  Future<Map<String, dynamic>> getLastGameParams() async {
    final res = (await _storage.read(AppStorageKeys.lastGameParams));
    if (res != null && res != '') {
      Map<String, dynamic> rValue = jsonDecode(res);
      if (rValue['game_mode'] != null) {
        rValue['game_mode'] = EGameMode.fromString(rValue['game_mode']);
      }
      return rValue;
    }
    return {"quiz_id": null, "game_mode": null};
  }
}
