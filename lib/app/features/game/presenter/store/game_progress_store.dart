import 'dart:async';

import 'package:flutter_triple/flutter_triple.dart';
import 'package:quycky/core/usecase/errors/failure.dart';

class GameProgressStore extends Store<double> {
  Timer _timer = Timer(const Duration(seconds: 0), () => {});
  // late Function? _changeState;
  int _totalSeconds = 8;
  double _timeFraction = 0;

  void _currentTimeUpdate(Timer t) {
    update(state + _timeFraction);
    if (state >= 1) {
      t.cancel();
    }
  }

  void setFractionOfTime(double fraction) {
    _timeFraction = fraction;
  }

  void stopTimer() => _timer.cancel();

  void setProgressTo(double value, {autoIncrement = false}) {
    update(value > 1 ? 1 : value);
    if (autoIncrement && !_timer.isActive) {
      //  print('==>Timer: $_timeFraction');
      startCurrentTimeCounting();
    }
  }

  void restartCurrentTime({int? totalSeconds}) {
    update(0);
    if (totalSeconds != null) {
      _totalSeconds = totalSeconds;
    }
    _timer.cancel();
    startCurrentTimeCounting();
  }

  void startCurrentTimeCounting() {
    _timer =
        Timer.periodic(const Duration(milliseconds: 100), _currentTimeUpdate);
  }

  // void setCurrentTimerTo() {}

  clear() {
    _timer.cancel();
    setProgressTo(0.0);
  }

  GameProgressStore() : super(0.0) {
    _timer.cancel();
  }
}
