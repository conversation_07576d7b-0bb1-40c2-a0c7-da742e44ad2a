import 'package:flutter_triple/flutter_triple.dart';
import 'package:quycky/app/features/game/domain/entities/game_friend_invite_entity.dart';
import 'package:quycky/core/usecase/errors/failure.dart';

class GameFriendInviteStore extends Store<GameFriendInviteEntity> {
  GameFriendInviteStore() : super(const GameFriendInviteEntity());

  clear() {
    update(const GameFriendInviteEntity(), force: true);
  }

  setData(GameFriendInviteEntity data) {
    update(data, force: true);
  }
}
