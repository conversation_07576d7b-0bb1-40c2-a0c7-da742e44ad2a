import 'package:flutter/material.dart';
import 'package:quycky/app/features/game/domain/entities/game_entity.dart';
import 'package:quycky/app/features/game/domain/entities/socket_user_entity.dart';
import 'package:quycky/app/features/game/presenter/pages/widgets/invite_friend_button.dart';
import 'package:quycky/app/features/game/presenter/pages/widgets/lobby_backdrop.dart';
import 'package:quycky/app/features/game/presenter/pages/widgets/player_avatar_animated.dart';
import 'package:quycky/app/features/user/domain/entities/user_entity.dart';
import 'dart:math';

import 'package:quycky/app/widgets/avatar.dart';
import 'package:quycky/core/utils/animated_opacity_widget.dart';
import 'package:quycky/core/utils/interval_utils.dart';
import 'package:quycky/core/utils/show_generic_dialog.dart';
import 'package:sizer/sizer.dart';

Iterable<Rect> getBounds(Rect rect, int length) sync* {
  final s = Size.square(rect.shortestSide / 6.5);
  final radius = (rect.shortestSide - s.shortestSide) * 0.40;
  for (var i = 0; i < length; i++) {
    /// distance +
    final angle = i * pi / 6 + pi * .01;
    final center = rect.center + Offset(cos(angle), sin(angle)) * radius;
    yield Rect.fromCenter(center: center, width: s.width, height: s.height);
  }
}

class PlayersOrbit extends StatefulWidget {
  final List<SocketUserEntity> players;
  final UserEntity localPlayer;
  final Map<int, SocketUserEntity> invitedPlayers;
  final double height;
  final double width;
  final EGameMode gameMode;
  final bool showButtonAddFriends;
  final bool showAllInviteSlots;
  final void Function({int pos})? onInviteFriendsPressed;
  final Function(bool value) setShowAllInviteSlots;
  final bool hasLoggedUserEverHadAnyMatches;
  final bool fromVibeCheckPage;

  const PlayersOrbit(
      {super.key,
      this.hasLoggedUserEverHadAnyMatches = false,
      this.invitedPlayers = const {},
      required this.setShowAllInviteSlots,
      this.height = double.infinity,
      this.showAllInviteSlots = false,
      this.players = const [],
      required this.localPlayer,
      this.showButtonAddFriends = false,
      this.onInviteFriendsPressed,
      required this.gameMode,
      this.width = double.infinity,
      this.fromVibeCheckPage = false});

  @override
  _PlayersOrbit createState() => _PlayersOrbit();
}

class _PlayersOrbit extends State<PlayersOrbit>
    with SingleTickerProviderStateMixin {
  final invitedSlot001 = GlobalKey();
  final invitedSlot002 = GlobalKey();
  final invitedSlot003 = GlobalKey();
  final List<Alignment> _avatarPositions = [
    Alignment.topCenter,
    Alignment.centerRight,
    Alignment.bottomCenter
  ];
  late AnimationController controller;
  Map<int, SocketUserEntity> localInvitedPlayers = {};

  double size = 400;
  @override
  void initState() {
    super.initState();
    controller = AnimationController(vsync: this);
    controller.repeat(min: 0.0, max: 1.0, period: const Duration(seconds: 7));
  }

  Point rotatePoint(Point xy, double radians, Point origin) {
    num adjustedX = xy.x - origin.x;
    num adjustedY = (xy.y - origin.y);
    num cosRad = cos(radians);
    num sinRad = sin(radians);
    num qx = origin.x + cosRad * adjustedX + sinRad * adjustedY;
    num qy = origin.y + -sinRad * adjustedX + cosRad * adjustedY;

    return Point(qx, qy);
  }

  getSize() {
    Size screenSize = MediaQuery.of(context).size;
    if (screenSize.width <= 400) {
      size = screenSize.width - 40;
    }
  }

  PlayerAvatarAnimated getPlayerAvatarAnimated(
      int alignementIndex, UserEntity? playerData) {
    return PlayerAvatarAnimated(
      alignment: _avatarPositions[alignementIndex],
      imagePath: playerData?.avatarUrl,
      name: playerData?.name ?? '',
      visible: playerData != null,
    );
  }

  void handleTapInviteFriends(int pos) {
    widget.onInviteFriendsPressed!(pos: pos);
  }

  // Widget getInviteFriends() {
  //   if (widget.showButtonAddFriends) {
  //     return getInviteFriendsWidget();
  //   }
  //   if (widget.gameMode == EGameMode.oneOnInvitedPlayers &&
  //       widget.players.isEmpty) {
  //     return getPlayerAvatarAnimated(0, const UserEntity(name: 'WAITING'));
  //   }
  //   return Container();
  // }

  List<PlayerAvatarAnimated> getPlayersAvatarAnimated() {
    List<PlayerAvatarAnimated> res = [];
    for (int i = 0; i < 3; i++) {
      res.add(getPlayerAvatarAnimated(
          i,
          widget.players.asMap().containsKey(i)
              ? widget.players[i].userData
              : null));
    }
    return res;
  }

  Widget getPlayer(UserEntity user) {
    return PlayerAvatarAnimated(
      // key: key,
      alignment: Alignment.center,
      imagePath: user.avatarUrl,
      name: user.uuid == 'WAITING' ? 'WAITING' : user.name,
      visible: true,
    );
  }

  showFriendAcceptedToPlay(UserEntity userData, GlobalKey? key) {
    if (key == null) {
      return;
    }
    RenderBox? box = key.currentContext?.findRenderObject() as RenderBox?;
    Offset? position = box?.localToGlobal(Offset.zero);
    ShowGenericDialog(
        widget: LobbyBackdrop(
            offset: position,
            friendData: userData,
            duration: const Duration(seconds: 3),
            child: Container()));
  }

  void testAndUpdateLocalInvitedPlayers() {
    if (localInvitedPlayers.isNotEmpty) {
      if ((widget.invitedPlayers.containsKey(0)) &&
          widget.invitedPlayers[0]!.userData.uuid != 'WAITING' &&
          widget.invitedPlayers[0]!.userData.uuid !=
              localInvitedPlayers[0]!.userData.uuid) {
        setTimeout(
            callback: () => showFriendAcceptedToPlay(
                widget.invitedPlayers[0]!.userData, invitedSlot001),
            duration: const Duration(microseconds: 100));
      }
      if ((widget.invitedPlayers.containsKey(1)) &&
          widget.invitedPlayers[1]!.userData.uuid != 'WAITING' &&
          widget.invitedPlayers[1]!.userData.uuid !=
              localInvitedPlayers[1]!.userData.uuid) {
        setTimeout(
            callback: () => showFriendAcceptedToPlay(
                widget.invitedPlayers[1]!.userData, invitedSlot002),
            duration: const Duration(microseconds: 100));
      }
      if ((widget.invitedPlayers.containsKey(2)) &&
          widget.invitedPlayers[2]!.userData.uuid != 'WAITING' &&
          widget.invitedPlayers[2]!.userData.uuid !=
              localInvitedPlayers[2]!.userData.uuid) {
        setTimeout(
            callback: () => showFriendAcceptedToPlay(
                widget.invitedPlayers[2]!.userData, invitedSlot003),
            duration: const Duration(microseconds: 100));
      }
    }
    localInvitedPlayers = widget.invitedPlayers;
    // if (widget.invitedPlayers.isNotEmpty) {
    //   print(
    //       '==>${widget.invitedPlayers[1]!.userData.name}--${localInvitedPlayers[1]!.userData.name}');
    // }
  }

  Widget getButtonAddFriends(int index) {
    return InviteFriendButton(
      onTap: () => handleTapInviteFriends(index),
    );
  }

  List<Widget> getInvitedPlayersAvatar() {
    //getPlayersAvatarAnimated
    List<Widget> res = [
      Positioned(
          bottom: -3,
          child: widget.invitedPlayers.containsKey(1)
              ? AnimatedOpacityWidget(
                  key: invitedSlot002,
                  showAnimation:
                      widget.invitedPlayers[1]!.userData.uuid == 'WAITING',
                  child: getPlayer(
                    widget.invitedPlayers[1]!.userData,
                  ))
              : getButtonAddFriends(1)),
    ];
    if (!widget.fromVibeCheckPage && widget.showAllInviteSlots == true) {
      res.add(Positioned(
          left: 0,
          bottom: MediaQuery.of(context).size.height * 0.12,
          child: widget.invitedPlayers.containsKey(0)
              ? AnimatedOpacityWidget(
                  key: invitedSlot001,
                  showAnimation:
                      widget.invitedPlayers[0]!.userData.uuid == 'WAITING',
                  child: getPlayer(
                    widget.invitedPlayers[0]!.userData,
                  ))
              : getButtonAddFriends(0)));
      res.add(Positioned(
          right: 0,
          bottom: MediaQuery.of(context).size.height * 0.12,
          child: widget.invitedPlayers.containsKey(2)
              ? AnimatedOpacityWidget(
                  key: invitedSlot003,
                  showAnimation:
                      widget.invitedPlayers[2]!.userData.uuid == 'WAITING',
                  child: getPlayer(
                    widget.invitedPlayers[2]!.userData,
                  ))
              : getButtonAddFriends(2)));
    }

    testAndUpdateLocalInvitedPlayers();

    return res;
  }

  List<Widget> getPlayersAvatar() {
    return (widget.fromVibeCheckPage ||
            widget.showButtonAddFriends &&
                widget.hasLoggedUserEverHadAnyMatches)
        ? getInvitedPlayersAvatar()
        : getPlayersAvatarAnimated();
  }

  @override
  Widget build(BuildContext context) {
    getSize();

    return SizedBox(
      height: size + 100,
      width: size,
      child: Stack(
        alignment: Alignment.center,
        children: <Widget>[
          Center(
            child: Avatar(
              imagePath: widget.localPlayer.avatarUrl,
              size: 11.14.h,
              addPhotoButton: false,
            ),
          ),
          ...getPlayersAvatar(),
          // getInviteFriends(),
        ],
      ),
    );
  }
}
