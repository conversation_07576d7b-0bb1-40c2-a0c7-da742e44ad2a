import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:quycky/app/services/remote_config/remote_config_store.dart';

class LobbyCountdown extends StatefulWidget {
  final Function goToNextPage;

  const LobbyCountdown({super.key, required this.goToNextPage});

  @override
  _LobbyCountdown createState() => _LobbyCountdown();
}

class _LobbyCountdown extends State<LobbyCountdown> {
  // final _store = Modular.get<GameCountdownStore>();
  int _count = 5;
  @override
  void initState() {
    super.initState();
    startCountdown();
  }

  void _updateCount(Timer timer) {
    if (_count > 0 && mounted) {
      setState(() {
        _count--;
      });
      return;
    }
    timer.cancel();
  }

  void startCountdown() {
    final remoteConfig = Modular.get<RemoteConfigStore>();
    _count = remoteConfig.state.gameTimeWaitingStartGameMultiplayer;
    Timer.periodic(const Duration(seconds: 1), _updateCount);
  }

  String get countText => _count == 0 ? 'GO!' : _count.toString();

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Text(
        countText,
        style: TextStyle(
            fontFamily: "SFProText",
            color: Colors.white,
            fontSize: _count == 0 ? 45 : 100,
            fontWeight: FontWeight.bold),
      ),
    );
  }
}
