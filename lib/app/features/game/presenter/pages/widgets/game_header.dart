import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_svg/svg.dart';
import 'package:flutter_triple/flutter_triple.dart';
import 'package:quycky/app/features/game/domain/entities/game_entity.dart';
import 'package:quycky/app/features/game/presenter/pages/widgets/game_progress_bar.dart';
import 'package:quycky/app/features/game/presenter/store/game_progress_store.dart';
import 'package:quycky/app/features/game/presenter/store/game_round_info_store.dart';
import 'package:quycky/app/features/game/presenter/store/game_store.dart';
import 'package:quycky/app/widgets/app_header.dart';

TextStyle buttonDefaultTextStyle(Color color, {double fontSize = 15}) =>
    TextStyle(
        letterSpacing: 3,
        fontFamily: "Roboto",
        fontWeight: FontWeight.bold,
        color: color,
        fontSize: fontSize);

class GameHeader extends StatefulWidget {
  final String title;

  const GameHeader({
    super.key,
    this.title = '',
  });

  @override
  State<GameHeader> createState() => _GameHeader();
}

class _GameHeader extends State<GameHeader> {
  final _roundInfoStore = Modular.get<GameRoundInfoStore>();
  final _gameStore = Modular.get<GameStore>();
  final _progressStore = Modular.get<GameProgressStore>();

  Widget getLogoSection() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.max,
      children: [
        SizedBox(
            height: 45,
            child: SvgPicture.asset(
              'assets/img/svg/logo.svg',
            )),
      ],
    );
  }

  // Widget getWidgetTitleAndSubtitle(String title) => Padding(
  //     padding: const EdgeInsets.only(top: 8),
  //     child: ScopedBuilder(
  //         store: _gameStore,
  //         onState: (context, GameEntity state) => ClipRRect(
  //               borderRadius: const BorderRadius.all(Radius.circular(23)),
  //               child: Text(
  //                 title.isEmpty
  //                     ? 'ROUND ${state.room.flagCurrentRound}/6'
  //                     : title,
  //                 style: const TextStyle(
  //                     letterSpacing: 3,
  //                     fontFamily: "SFProText",
  //                     fontWeight: FontWeight.bold,
  //                     color: Colors.white,
  //                     fontSize: 14),
  //               ),
  //             )));

  Widget getWidgetTitleAndSubtitle(String title) => ScopedBuilder(
      store: _gameStore,
      onState: (context, GameEntity state) => AppHeader(
            title: title.isEmpty
                ? 'ROUND ${state.room.flagCurrentRound}/${state.room.numRounds}'
                : title,
          ));

  Widget getWidget() {
    return Column(
      children: [getWidgetTitleAndSubtitle(widget.title)],
    );
  }

  @override
  Widget build(BuildContext context) {
    return getWidget();
  }
}
