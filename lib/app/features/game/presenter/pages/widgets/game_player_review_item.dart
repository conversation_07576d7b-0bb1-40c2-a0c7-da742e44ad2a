import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:quycky/app/features/user/domain/entities/user_entity.dart';
import 'package:quycky/app/theme/colors.dart';
import 'package:quycky/app/widgets/avatar.dart';
import 'package:quycky/core/utils/image_assets.dart';
import 'package:quycky/app/widgets/button.dart';
import 'package:quycky/core/utils/show_dialog_friend_menu.dart';
import 'package:sizer/sizer.dart';

class GamePlayerReviewItem extends StatefulWidget {
  final UserEntity player;
  final int points;
  final String avatarUrl;
  final double avatarSize;
  final bool alreadyGavePoints;
  final bool isLocalPlayer;
  final bool showAddButton;
  final void Function() onAvatarPressed;
  final String answer;
  final Uint8List? playerImageMemoryData;

  const GamePlayerReviewItem(
      {super.key,
      this.answer = '',
      required this.player,
      this.isLocalPlayer = false,
      this.points = 0,
      this.avatarUrl = '',
      this.avatarSize = 40,
      this.showAddButton = true,
      this.alreadyGavePoints = false,
      this.playerImageMemoryData,
      required this.onAvatarPressed});

  @override
  State<GamePlayerReviewItem> createState() => _GamePlayerReviewItem();
}

class _GamePlayerReviewItem extends State<GamePlayerReviewItem> {
  Widget getButton(String assetImageUrl, Color shadowColor,
          {void Function()? onPressed}) =>
      Visibility(
        visible: (!widget.alreadyGavePoints && !widget.isLocalPlayer),
        child: Button(
          circleButton: true,
          assetImageUrl: assetImageUrl,
          shadowColor: shadowColor,
          onPressed: onPressed,
          autoSized: true,
        ),
      );
  onAvatarPressed() {
    if (widget.showAddButton) {
      ShowDialogFriendMenu(
          user: widget.player,
          playerImageMemoryData: widget.playerImageMemoryData);
    }
  }

  Widget getWidget() {
    return Container(
        constraints: BoxConstraints(maxWidth: 90.w),
        child: Column(children: [
          SizedBox(
            child: Column(children: [
              Container(
                height: 49,
                decoration: const BoxDecoration(
                  color: CustomColors.orangeSoda30,
                  borderRadius: BorderRadius.all(Radius.circular(100)),
                ),
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 4.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 4),
                            child: Avatar(
                              imageMemory: widget.playerImageMemoryData,
                              size: widget.avatarSize,
                              addPhotoButton: widget.showAddButton,
                              onPressed: onAvatarPressed,
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsets.only(left: 8.0),
                            child: Text(
                              widget.player.name,
                              style: const TextStyle(
                                  fontFamily: "SFProText",
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                  fontSize: 12),
                            ),
                          ),
                        ],
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(
                            vertical: 12.0, horizontal: 10),
                        child: Row(
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(top: 4),
                              child: Padding(
                                padding: const EdgeInsets.only(right: 9.0),
                                child: Text(
                                  widget.points > 0
                                      ? widget.points.toString()
                                      : '0',
                                  style: const TextStyle(
                                      fontFamily: "SFProText",
                                      fontWeight: FontWeight.bold,
                                      color: Colors.white,
                                      fontSize: 12),
                                ),
                              ),
                            ),
                            Image.asset(
                              ImageAssets.fire,
                              width: 25,
                            ),
                          ],
                        ),
                      )
                    ],
                  ),
                ),
              ),
            ]),
          )
        ]));
  }

  @override
  Widget build(BuildContext context) {
    return getWidget();
  }
}
