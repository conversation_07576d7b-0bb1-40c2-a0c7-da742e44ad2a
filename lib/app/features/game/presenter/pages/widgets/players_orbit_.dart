import 'package:flutter/material.dart';
import 'dart:math' as math;

import 'package:quycky/app/widgets/avatar.dart';

void main() => runApp(const MyApp());

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return const MaterialApp(
      home: Scaffold(
        body: Center(
          child: SizedBox(width: 300.0, height: 300.0, child: OrbitingButton()),
        ),
      ),
    );
  }
}

class OrbitingButton extends StatefulWidget {
  const OrbitingButton({super.key});

  @override
  _OrbitingButtonState createState() => _OrbitingButtonState();
}

class _OrbitingButtonState extends State<OrbitingButton>
    with TickerProviderStateMixin {
  late final AnimationController _controller =
      AnimationController(vsync: this, duration: const Duration(seconds: 15))
        ..repeat();

  late AnimationController controller;

  @override
  void initState() {
    super.initState();
    controller = AnimationController(vsync: this);

    controller.repeat(min: 0.0, max: 1.0, period: const Duration(seconds: 15));
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.center,
      children: <Widget>[
        RotationTransition(
          turns: controller,
          child: Align(
            alignment: Alignment.topCenter,
            child: AnimatedBuilder(
              animation: _controller,
              builder: (_, child) {
                return Transform.rotate(
                  angle: -_controller.value * 2 * math.pi,
                  child: child,
                );
              },
              child: Avatar(),
            ),
          ),
        ),
        TextButton(
          onPressed: () => {print('==>')},
          child: const Text('Button'),
        )
      ],
    );
  }
}
