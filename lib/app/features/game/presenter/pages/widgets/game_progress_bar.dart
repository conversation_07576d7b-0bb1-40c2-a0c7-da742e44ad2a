import 'dart:async';

import 'package:flutter/material.dart';
import 'package:quycky/app/theme/colors.dart';
import 'package:quycky/core/utils/app_env.dart';
import 'package:sizer/sizer.dart';

class GameProgressBar extends StatefulWidget {
  final double value;
  final int timeInSeconds;

  const GameProgressBar({super.key, this.value = 0, this.timeInSeconds = 8});

  @override
  State<GameProgressBar> createState() => _GameProgressBarState();
}

class _GameProgressBarState extends State<GameProgressBar>
    with TickerProviderStateMixin {
  late AnimationController controller;
  double _count = 0;
  bool isLessOrEqualsToMinimumLayoutHeight = false;

  _GameProgressBarState() {
    // startProgressCounter();
  }

  @override
  void initState() {
    controller = AnimationController(
      /// [AnimationController]s can be created with `vsync: this` because of
      /// [TickerProviderStateMixin].
      vsync: this,
      duration: Duration(seconds: widget.timeInSeconds),
    )..addListener(() {
        setState(() {});
      });
    controller.forward();
    super.initState();
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  updateProgressVal() {
    _count = (1 - (_count / widget.timeInSeconds));
  }

  void _updateProgressCounter(Timer timer) {
    if (_count < 1 && mounted) {
      setState(() {
        _count += 1 / (10 * widget.timeInSeconds);
      });
      return;
    }
    timer.cancel();
  }

  void startProgressCounter() {
    Timer.periodic(const Duration(milliseconds: 100), _updateProgressCounter);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 5.33.h,
      constraints: BoxConstraints(maxWidth: 90.w),
      decoration: const BoxDecoration(
          color: CustomColors.salmon_2,
          borderRadius: BorderRadius.all(Radius.circular(23))),
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 5.9.w, vertical: 2.01.h),
        child: ClipRRect(
          borderRadius: const BorderRadius.all(Radius.circular(23)),
          child: LinearProgressIndicator(
              minHeight: 0.7.h,
              backgroundColor: Colors.white,
              value: controller.value,
              color: CustomColors.button),
        ),
      ),
    );
  }
}
