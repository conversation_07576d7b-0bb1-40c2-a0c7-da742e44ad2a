import 'package:flutter/material.dart';

class InvitedFriendEntered extends StatelessWidget {
  final bool show;
  final String username;
  final String avatarUrl;

  const InvitedFriendEntered({
    super.key,
    required this.show,
    required this.username,
    required this.avatarUrl,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        if (show)
          Container(
            color: Colors.amber,
            width: double.infinity,
            height: double.infinity,
            padding: const EdgeInsets.only(top: 144),
            alignment: Alignment.center,
            child: ConstrainedBox(
              constraints: const BoxConstraints(maxWidth: 390),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    "Olá, $username acabou de entrar",
                    style: const TextStyle(
                      fontFamily: "Roboto",
                      fontWeight: FontWeight.bold,
                      fontSize: 15,
                      letterSpacing: 0.3,
                      height: 2,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 20),
                  const CircleAvatar(
                    radius: 30,
                    backgroundImage: NetworkImage(
                        'https://www.gannett-cdn.com/media/2022/03/16/USATODAY/usatsports/imageForEntry5-ODq.jpg?width=1200&disable=upscale&format=pjpg&auto=webp'),
                  ),
                ],
              ),
            ),
          ),
      ],
    );
  }
}
