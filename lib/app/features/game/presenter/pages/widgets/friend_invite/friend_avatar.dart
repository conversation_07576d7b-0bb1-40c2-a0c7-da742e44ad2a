import 'package:flutter/material.dart';
import 'package:quycky/analytics.dart';
import 'package:quycky/app/features/user/domain/entities/user_entity.dart';
import 'package:quycky/app/theme/colors.dart';
import 'package:quycky/app/widgets/avatar.dart';
import 'package:sizer/sizer.dart';

class FriendAvatar extends StatelessWidget {
  final UserEntity friend;
  final void Function({UserEntity friendData})? onSelectFriend;

  const FriendAvatar({super.key, required this.friend, this.onSelectFriend});

  void handleOnSelectFriend() {
    if (onSelectFriend != null) {
      Analytics.instance.logEvent(name: 'lobby_send_invite_direct');
      onSelectFriend!(friendData: friend);
    }
  }

  String getLimitedName(String name) {
    return name.length > 9 ? '${name.substring(0, 6)}...' : name;
  }

  Decoration? getAvatarDecoration() {
    return friend.isConnected
        ? const BoxDecoration(
            gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  CustomColors.seaGreen90,
                  CustomColors.uclaGold,
                ],
                stops: [
                  0.4,
                  1,
                ]),
          )
        : null;
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
        width: 21.8.w,
        child: SizedBox(
          height: 11.84.h,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ClipOval(
                child: Container(
                  width: 9.48.h,
                  height: 9.48.h,
                  decoration: getAvatarDecoration(),
                  padding: const EdgeInsets.all(5),
                  child: Avatar(
                      imagePath: friend.avatarUrl,
                      addPhotoButton: false,
                      size: 9.48.h,
                      onPressed: handleOnSelectFriend),
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(top: 3.0),
                child: Text(
                  getLimitedName(friend.name),
                  style: const TextStyle(
                      letterSpacing: 2,
                      fontFamily: "Roboto",
                      fontWeight: FontWeight.w700,
                      color: CustomColors.americanPurple2,
                      fontSize: 10),
                ),
              )
            ],
          ),
        ));
  }
}
