import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:quycky/analytics.dart';
import 'package:quycky/app/features/game/presenter/pages/widgets/friend_invite/friend_avatar.dart';
import 'package:quycky/app/features/user/domain/entities/user_entity.dart';
import 'package:quycky/app/theme/colors.dart';
import 'package:quycky/core/utils/quycky_icons_icons.dart';
import 'package:sizer/sizer.dart';

class FriendsInviteBottomSheet extends StatelessWidget {
  late BuildContext currentContext;
  final List<UserEntity> friends;
  final UserEntity currentUser;
  final int? playerPosition;
  final void Function(UserEntity? friendData, {int pos, bool? shareLink})?
      onClose;
  FriendsInviteBottomSheet(
      {super.key,
      required this.currentUser,
      required this.friends,
      this.playerPosition,
      this.onClose}) {
    Analytics.instance.logEvent(name: 'lobby_open_invite');
  }

  Widget getAvatarFriend(UserEntity friend) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 2.05.w),
      child: FriendAvatar(friend: friend, onSelectFriend: handleClose),
    );
  }

  void handleClose({UserEntity? friendData, shareLink = false}) {
    if (shareLink) {
      Analytics.instance.logEvent(name: 'lobby_send_invite_link');
    }
    if (onClose != null) {
      onClose!(friendData, shareLink: shareLink, pos: playerPosition ?? 0);
    }
    Navigator.pop(currentContext);
  }

  List<Widget> getFriendsAvatarList() {
    List<Widget> res = [
      Padding(
        padding: const EdgeInsets.only(left: 30.0),
        child: InkWell(
            onTap: () =>
                handleClose(shareLink: true), // () => handleTapInviteFriends(),
            child: Column(
              children: [
                ClipOval(
                  child: Material(
                    color: CustomColors.orangeSoda,
                    child: InkWell(
                        highlightColor: CustomColors.orangeSoda,
                        splashColor: CustomColors.orangeSoda,
                        child: SizedBox(
                            width: 8.77.h,
                            height: 8.77.h,
                            child: Container(
                                height: 1.78.h,
                                padding: EdgeInsets.all(2.72.h),
                                child: SvgPicture.asset(
                                    'assets/img/svg/upload_file.svg'))) //Icon(Icons.file_upload_outlined, color: Colors.white)),
                        ),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(top: 1.h),
                  child: Text(
                    'SHARE ROOM',
                    style: TextStyle(
                        letterSpacing: 2,
                        fontFamily: "Roboto",
                        fontWeight: FontWeight.bold,
                        color: CustomColors.americanPurple2,
                        fontSize: 10),
                  ),
                )
              ],
            )),
      )
    ];
    for (var friend in friends) {
      res.add(getAvatarFriend(friend));
    }
    return res;
  }

  @override
  Widget build(BuildContext context) {
    currentContext = context;
    return WillPopScope(
      onWillPop: () async => false,
      child: Container(
        height: 270,
        width: 390,
        decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topRight: Radius.circular(24.0),
              topLeft: Radius.circular(24.0),
            )),
        child: Center(
          child: Column(
            children: <Widget>[
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Padding(
                    padding: const EdgeInsets.only(top: 2, right: 2.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        IconButton(
                            iconSize: 29,
                            onPressed: () => handleClose(shareLink: false),
                            color: CustomColors.americanPurple2,
                            icon: const Icon(QuyckyIcons.close_circle))
                      ],
                    ),
                  ),
                ],
              ),
              const Padding(
                padding: EdgeInsets.only(top: 0, bottom: 45.0),
                child: Center(
                  child: Text(
                    'INVITE A FRIEND',
                    style: TextStyle(
                        fontFamily: 'Roboto',
                        fontSize: 20,
                        color: CustomColors.americanPurple2,
                        letterSpacing: 1,
                        fontWeight: FontWeight.w700,
                        decoration: TextDecoration.none),
                  ),
                ),
              ),
              Container(
                width: 100.w,
                height: 11.6.h,
                padding: const EdgeInsets.only(left: 0),
                child: SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Row(
                      mainAxisSize: MainAxisSize.max,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: getFriendsAvatarList(),
                    )),
              ),
            ],
          ),
        ),
      ),
    );
  }
}


/**showModalBottomSheet<void>(
            context: context,
            builder: (BuildContext context) {
              return Container(
                height: 200,
                color: Colors.amber,
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    mainAxisSize: MainAxisSize.min,
                    children: <Widget>[
                      const Text('Modal BottomSheet'),
                      ElevatedButton(
                        child: const Text('Close BottomSheet'),
                        onPressed: () => Navigator.pop(context),
                      ),
                    ],
                  ),
                ),
              );
            },
          );*/