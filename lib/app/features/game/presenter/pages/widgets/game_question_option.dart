import 'package:flutter/material.dart';
import 'package:quycky/app/features/game/domain/entities/question_option_entity.dart';
import 'package:quycky/app/theme/colors.dart';
import 'package:quycky/core/utils/app_env.dart';
import 'package:sizer/sizer.dart';

class GameQuestionOption extends StatefulWidget {
  final QuestionOptionEntity optionData;
  final bool isSelected;
  final String label;
  final Function() onTap;

  const GameQuestionOption(
      {super.key,
      required this.optionData,
      required this.onTap,
      this.label = '',
      this.isSelected = false});

  @override
  State<GameQuestionOption> createState() => _GameQuestionOption();
}

class _GameQuestionOption extends State<GameQuestionOption> {
  bool isLessOrEqualsToMinimumHeight = false;

  Widget getWidget() {
    Color labelColor = CustomColors.primary;
    Color color = Colors.white;
    Color questionColor = CustomColors.americanPurple;

    if (widget.isSelected) {
      color = CustomColors.orangeSoda40;
      questionColor = labelColor = Colors.white;
    }

    return Stack(alignment: AlignmentDirectional.bottomCenter, children: [
      Container(
        constraints: BoxConstraints(maxWidth: 90.w),
        height: 8.88.h,
        decoration: BoxDecoration(
          color: color,
          borderRadius: const BorderRadius.all(Radius.circular(100)),
          border: Border.all(width: 1, color: Colors.white),
        ),
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 1.02.w),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              SizedBox(
                width: 11.28.w,
                child: Text(
                  textAlign: TextAlign.center,
                  widget.label,
                  style: TextStyle(
                      fontFamily: "SFProText",
                      fontWeight: FontWeight.w500,
                      color: labelColor,
                      fontSize: 15.sp),
                ),
              ),
              Flexible(
                child: Text(
                  widget.optionData.value,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                      fontFamily: "SFProText",
                      fontWeight: FontWeight.w500,
                      color: questionColor,
                      fontSize: 15.sp),
                ),
              ),
              const SizedBox(
                width: 44,
              )
            ],
          ),
        ),
      ),
    ]);
  }

  void handleOnTap() {
    widget.onTap();
  }

  @override
  Widget build(BuildContext context) {
    isLessOrEqualsToMinimumHeight =
        MediaQuery.of(context).size.height <= AppEnv.minimumLayoutHeight;
    return InkWell(onTap: handleOnTap, child: getWidget());
  }
}
