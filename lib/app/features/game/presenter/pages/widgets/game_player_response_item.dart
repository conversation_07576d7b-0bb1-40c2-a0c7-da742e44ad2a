import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:quycky/app/features/game/domain/entities/game_entity.dart';
import 'package:quycky/app/features/user/domain/entities/user_entity.dart';
import 'package:quycky/app/theme/colors.dart';
import 'package:quycky/app/widgets/avatar.dart';
import 'package:quycky/core/utils/assets_strings.dart';
import 'package:quycky/core/utils/image_assets.dart';
import 'package:quycky/app/widgets/button.dart';
import 'package:quycky/core/utils/show_dialog_friend_menu.dart';
import 'package:sizer/sizer.dart';

class GamePlayerResponseItem extends StatefulWidget {
  final UserEntity player;
  final int points;
  final String avatarUrl;
  final double avatarSize;
  final bool alreadyGavePoints;
  final bool isLocalPlayer;
  final bool showAddButton;
  final String answer;
  final int receivedPontuations;
  final void Function(String identifier, EGamePontuationType pontuationType)?
      givePointToPlayer;
  final void Function() hidePointButtons;
  final String userWhoReceivedPoint;
  final EGamePontuationType receivedPointType;
  final bool isReviewingMode;
  final Uint8List? playerImageMemoryData;
  final bool showPointButtons;

  const GamePlayerResponseItem(
      {super.key,
      this.answer = '',
      this.playerImageMemoryData,
      required this.player,
      this.isLocalPlayer = false,
      this.points = 0,
      this.isReviewingMode = false,
      this.givePointToPlayer,
      required this.showPointButtons,
      required this.hidePointButtons,
      this.avatarUrl = '',
      this.avatarSize = 40,
      this.userWhoReceivedPoint = '',
      this.receivedPointType = EGamePontuationType.cold,
      this.showAddButton = true,
      this.receivedPontuations = 0,
      this.alreadyGavePoints = false});

  @override
  State<GamePlayerResponseItem> createState() => _GamePlayerResponseItem();
}

class _GamePlayerResponseItem extends State<GamePlayerResponseItem> {
  bool isLessOrEqualsToMinimumHeight = false;
  Widget getButton(String assetImageUrl, Color shadowColor,
      {void Function()? onPressed}) {
    final Function()? executeBeforeAnimation =
        widget.alreadyGavePoints ? null : onPressed;
    return Visibility(
      visible: (widget.showPointButtons &&
          !widget.isLocalPlayer &&
          widget.answer.isNotEmpty),
      child: SizedBox(
        height: 5.33.h,
        child: Button(
          circleButton: true,
          assetImageUrl: assetImageUrl,
          shadowColor: shadowColor,
          onPressed: widget.hidePointButtons,
          executeBeforeAnimation: executeBeforeAnimation,
          autoSized: true,
        ),
      ),
    );
  }

  onAvatarPressed() {
    if (widget.showAddButton) {
      ShowDialogFriendMenu(
          user: widget.player,
          playerImageMemoryData: widget.playerImageMemoryData);
    }
  }

  void givePointToPlayer(EGamePontuationType pontuationType) {
    widget.givePointToPlayer!(widget.player.identifier ?? '', pontuationType);
  }

  Widget getFireButton() => getButton(Assets.png.fire, CustomColors.orangeSoda,
      onPressed: () => givePointToPlayer(EGamePontuationType.fire));
  Widget getIceButton() => getButton(Assets.png.ice, CustomColors.paleCerulean,
      onPressed: () => givePointToPlayer(EGamePontuationType.cold));

  List<Widget> getFirePoints() {
    List<Widget> res = [];
    if (widget.receivedPontuations > 0) {
      final points =
          widget.receivedPontuations > 3 ? 3 : widget.receivedPontuations;
      for (int i = 0; i < points; i++) {
        res.add(Padding(
          padding: EdgeInsets.symmetric(horizontal: 2.05.w),
          child: Image.asset(
            'assets/img/png/fire.png',
            width: 6.41.w,
            height: 3.08.h,
          ),
        ));
      }
    }
    return res;
  }

  Widget getWidget() {
    Color color = Colors.white30;
    if (widget.userWhoReceivedPoint == widget.player.identifier) {
      color = widget.receivedPointType == EGamePontuationType.fire
          ? CustomColors.orangeSoda30
          : CustomColors.paleCerulean50;
    }
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 0),
      child: Container(
          constraints: BoxConstraints(maxWidth: 90.w),
          height: 9.35.h + widget.avatarSize * 0.7, //54
          child: Stack(alignment: AlignmentDirectional.topCenter, children: [
            SizedBox(
              height: 8.7.h + widget.avatarSize * 0.7,
              child: Stack(
                  alignment: AlignmentDirectional.bottomCenter,
                  children: [
                    Container(
                      height: 8.7.h,
                      width: 90.w,
                      decoration: BoxDecoration(
                        color: color,
                        borderRadius:
                            const BorderRadius.all(Radius.circular(100)),
                      ),
                      child: Padding(
                        padding: EdgeInsets.symmetric(horizontal: 1.5.w),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            getIceButton(),
                            Flexible(
                              child: Container(
                                margin: EdgeInsets.symmetric(
                                    horizontal: widget.showPointButtons &&
                                            !widget.isLocalPlayer
                                        ? 0
                                        : 5.6.w),
                                child: Text(
                                  widget.answer,
                                  textAlign: TextAlign.center,
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                  style: TextStyle(
                                      fontFamily: "SFProText",
                                      fontWeight: FontWeight.bold,
                                      color: Colors.white,
                                      fontSize: 12),
                                ),
                              ),
                            ),
                            getFireButton(),
                          ],
                        ),
                      ),
                    ),
                    Positioned(
                        bottom: 8.88.h,
                        left: 7.7.w,
                        child: Text(
                          widget.player.name,
                          style: const TextStyle(
                              fontFamily: "SFProText",
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                              fontSize: 7),
                        )),
                    Positioned(
                        bottom: 8.88.h,
                        right: 3.59.w,
                        child: Row(
                          children: [
                            Padding(
                              padding: EdgeInsets.only(right: 0.77.w),
                              child: Image.asset(
                                ImageAssets.fire,
                                width: 13,
                              ),
                            ),
                            Text(
                              widget.points > 0 ? widget.points.toString() : '',
                              style: TextStyle(
                                  fontFamily: "SFProText",
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                  fontSize: 7),
                            )
                          ],
                        )),
                    Positioned(
                      top: 0, //-widget.avatarSize * 0.00,
                      child: Avatar(
                        // imagePath: widget.player.avatarUrl ?? '',
                        imageMemory: widget.playerImageMemoryData,
                        size: widget.avatarSize,
                        onPressed: onAvatarPressed,
                        addPhotoButton: widget.showAddButton,
                      ),
                    ),
                  ]),
            ),
            Positioned(
              bottom: 0,
              child: Row(
                children: getFirePoints(),
              ),
            ),
          ])),
    );
  }

  @override
  Widget build(BuildContext context) {
    return getWidget();
  }
}
