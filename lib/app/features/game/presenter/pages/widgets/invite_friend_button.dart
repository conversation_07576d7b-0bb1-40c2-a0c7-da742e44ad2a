import 'package:flutter/material.dart';
import 'package:quycky/app/widgets/custom_image.dart';
import 'package:quycky/core/utils/assets_strings.dart';

class InviteFriendButton extends StatelessWidget {
  final void Function() onTap;

  const InviteFriendButton({Key? key, required this.onTap}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 88,
      child: InkWell(
        onTap: onTap,
        highlightColor: Colors.red,
        child: Column(mainAxisSize: MainAxisSize.min, children: [
          CustomImage(
            Assets.svg.addFriendButton,
            height: 58,
          ),
          const SizedBox(height: 8),
          const Text(
            'INVITE FRIENDS',
            style: TextStyle(
                letterSpacing: 1.2,
                fontFamily: "Roboto",
                fontWeight: FontWeight.w500,
                color: Colors.white,
                fontSize: 12,
                decoration: TextDecoration.none),
          ),
        ]),
      ),
    );
  }
}
