import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:qr_code_scanner_plus/qr_code_scanner_plus.dart';
import 'package:quycky/analytics.dart';
import 'package:quycky/app/features/friendship/domain/entities/profile_temp_code_entity.dart';
import 'package:quycky/app/features/friendship/presenter/controllers/friendship_controller.dart';
import 'package:quycky/app/theme/colors.dart';
import 'package:quycky/app/widgets/app_header.dart';
import 'package:quycky/app/widgets/gradient_container.dart';
import 'package:quycky/app/widgets/quycky_modal_progress.dart';
import 'package:quycky/core/utils/app_routes.dart';
import 'package:quycky/core/utils/quycky_icons_icons.dart';
import 'package:quycky/core/utils/show_dialog_confirmation.dart';
import 'package:quycky/core/utils/show_message.dart';
import 'package:sizer/sizer.dart';

class ReadGameQrcodePage extends StatefulWidget {
  const ReadGameQrcodePage({super.key});

  @override
  State<ReadGameQrcodePage> createState() => _ReadGameQrcodePageState();
}

class _ReadGameQrcodePageState extends State<ReadGameQrcodePage>
    with SingleTickerProviderStateMixin {
  bool _isLoading = false;
  bool _isVerifyingCode = false;
  final bool _isTryingToAddFriend = false;
  final _controller = Modular.get<FriendshipController>();
  final GlobalKey qrKey = GlobalKey(debugLabel: 'QR');
  final _textEditingCodeFocusNode = FocusNode();
  final _textEditingCodeController = TextEditingController();
  QRViewController? _qrViewController;
  final ProfileTempCodeEntity _tempCodeData = ProfileTempCodeEntity(
      code: '1234',
      createdAt: DateTime.now(),
      friendId: '',
      id: '',
      message: '',
      status: '',
      userId: '');

  void _onQRViewCreated(QRViewController controller) {
    setState(() {
      _qrViewController = controller;
    });
    _qrViewController!.scannedDataStream.listen((scanData) {
      Analytics.instance.logEvent(name: 'qr_code_readed');
      _qrViewController!.pauseCamera();
      if (scanData.code != null && scanData.code!.isNotEmpty) {
        _handleValidateAndOpenGameByQrCode(scanData.code!);
      }
    });
  }

  @override
  void dispose() {
    _textEditingCodeController.dispose();
    _textEditingCodeFocusNode.dispose();
    super.dispose();
  }

  FutureOr<Null> _handleRequisitionError(dynamic errorResult) {
    final errMessage = errorResult.toString().isEmpty
        ? 'An error occurred, please, verify your internet connection and try again later'
        : errorResult.toString().replaceAll('Exception: ', '');

    ShowMessage(
        message: errMessage,
        callback: () {
          _textEditingCodeFocusNode.unfocus();
          _resumeCamera();
        },
        duration: Duration(seconds: 5));
  }

  set isLoading(bool val) {
    setState(() {
      _isLoading = val;
    });
  }

  @override
  void initState() {
    super.initState();
  }

  void handleBack() {
    Navigator.pop(context);
  }

  void _pauseCamera() {
    _qrViewController?.pauseCamera();
    setState(() => true);
  }

  void _resumeCamera() {
    _isVerifyingCode = false;
    _qrViewController?.resumeCamera();
    setState(() => true);
  }

  bool isValidCode(String code) {
    if (code.contains('/game/local/')) {
      final parts = code.split('/game/local/');
      if (parts.length >= 2) {
        final locationCode = parts[1].split('/')[0].split('?')[0];
        return locationCode.isNotEmpty &&
            RegExp(r'^[a-zA-Z0-9]+$').hasMatch(locationCode);
      }
      return false;
    }

    // Mantém compatibilidade com códigos de 4 dígitos numéricos (código legado)
    return code.length == 4 && code.contains(RegExp(r'^[0-9]+$'));
  }

  Widget _buildQrView() {
    final qrCodeView = QRView(
      key: qrKey,
      onQRViewCreated: _onQRViewCreated,
      overlay: QrScannerOverlayShape(
        borderRadius: 10,
        borderLength: 30,
        borderWidth: 10,
        // cutOutSize: (30.h > 254.69 ? 254.69 : 30.h),
      ),
    );

    return SizedBox(
      // constraints: BoxConstraints(maxWidth: 390, maxHeight: 356),
      // margin: EdgeInsets.only(bottom: 3.55.h),
      width: 100.w,
      height: 100.h,
      child: qrCodeView,
      // Container(color: Colors.black),
    );
  }

  void _handleGoToMatchPage(String friendId) {
    Modular.to.pushReplacementNamed(AppRoutes.checkFriendMatch,
        arguments: {'friendId': friendId, 'isNewFriend': true});
  }

  void _handleLocationCode(String locationUrl) {
    // Extrai o código de localização da URL
    final parts = locationUrl.split('/game/local/');
    if (parts.length >= 2) {
      final locationCode = parts[1].split('/')[0].split('?')[0];

      ShowMessage(
          message:
              'Location code detected: $locationCode. Processing location-based game...',
          duration: Duration(seconds: 3));

      // TODO: Implementar navegação ou processamento específico para códigos de localização
      // Exemplo: Modular.to.pushNamed('/game/local', arguments: {'locationCode': locationCode});

      _resumeCamera();
    } else {
      ShowMessage(
          message: 'Invalid location URL format',
          duration: Duration(seconds: 3));
      _resumeCamera();
    }
  }

  void _handleValidateAndOpenGameByQrCode(String qrText) async {
    if (qrText.isEmpty || _isVerifyingCode) return;

    _isVerifyingCode = true;

    _pauseCamera();

    if (!isValidCode(qrText)) {
      ShowDialogConfirmation(
          onOk: _resumeCamera,
          onClose: _resumeCamera,
          title: 'INVALID QR CODE',
          titleColor: CustomColors.orangeSoda,
          textColor: CustomColors.orangeSoda,
          text:
              "This QR code isn't connected to a Quycky lobby. Please scan a valid one to join.",
          buttonText: 'CANCEL');
      return;
    }

    if (qrText == _tempCodeData.code) {
      ShowMessage(
          message: 'You cannot add yourself as a friend',
          duration: Duration(seconds: 5));
      return;
    }

    _pauseCamera();

    if (qrText.contains('/game/local/')) {
      _handleLocationCode(qrText);
      return;
    }

    // Processa código de amigo normal
    await _controller.validateProfileTempCode(qrText).then((value) {
      ShowMessage(
          message: 'Friend added successfully',
          duration: Duration(seconds: 3),
          onPressed: () => _handleGoToMatchPage(value.userId),
          callback: () => _handleGoToMatchPage(value.userId));
    }).catchError((errorResult) {
      _handleRequisitionError(errorResult);
    });
  }

  Widget _bottomText() {
    return Column(
      children: [
        Text(
          'SCAN A LOCATION',
          style: TextStyle(
              letterSpacing: 1,
              fontFamily: "Roboto",
              fontWeight: FontWeight.w700,
              color: Colors.white,
              fontSize: 16.sp),
        ),
        SizedBox(height: 2.38.h),
        Text(
          'See a Quycky QR Code nearby?',
          style: TextStyle(
              height: 1.3,
              letterSpacing: 0.1,
              fontWeight: FontWeight.w500,
              fontFamily: "Roboto",
              color: Colors.white,
              fontSize: 16.sp),
        ),
        SizedBox(height: 0.2.h),
        Text(
          'Scan to instantly connect and play with people\naround you — in cafés, bars, or clubs near you.',
          style: TextStyle(
              letterSpacing: 0.25,
              fontFamily: "Roboto",
              fontWeight: FontWeight.w400,
              color: Colors.white,
              fontSize: 16.sp),
        ),
      ],
    );
  }

  Widget _buildBody() {
    return _isTryingToAddFriend
        ? QuyckyModalProgress(
            loadingIndex: 1,
            state: true,
            child: Container(),
          )
        : SafeArea(
            child: Column(
              children: [
                AppHeader(
                  noTitleSubtitleSpace: true,
                  logoSectionLeftWidget: IconButton(
                      onPressed: handleBack,
                      icon: Icon(
                        QuyckyIcons.arrow_left_circle,
                        color: Colors.white,
                        size: 23,
                      )),
                ),
                SizedBox(height: 3.31.h),
                Spacer(),
                _bottomText(),
                SizedBox(
                  height: 8.h,
                ),
              ],
            ),
          );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: GradientContainer(
          useDefault: true,
          child: Stack(
            children: [
              _buildQrView(),
              _buildBody(),
            ],
          )),
    );
  }
}
