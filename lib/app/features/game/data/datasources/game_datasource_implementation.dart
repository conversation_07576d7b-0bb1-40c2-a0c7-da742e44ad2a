import 'package:quycky/app/features/game/data/datasources/endpoints/game_endpoints.dart';
import 'package:quycky/app/features/game/data/datasources/game_datasource.dart';
import 'package:quycky/app/features/game/data/models/friend_invite_response_model.dart';
import 'package:quycky/app/features/game/domain/entities/friend_invite_params_entity.dart';
import 'package:quycky/core/http_client/http_client.dart';
import 'package:quycky/core/usecase/errors/exceptions.dart';

class GameDatasourceImplementation implements IGameDatasource {
  final HttpClient client;

  GameDatasourceImplementation(this.client);

  @override
  Future<FriendInviteResponseModel> inviteFriendToPlay(
      FriendInviteParamsEntity params) async {
    final response = await client.get(GameEndpoints.inviteFriendToPlay(params));
    if (response.statusCode == 200) {
      return FriendInviteResponseModel.fromJson(response.data);
    }
    throw ServerException();
  }
}
