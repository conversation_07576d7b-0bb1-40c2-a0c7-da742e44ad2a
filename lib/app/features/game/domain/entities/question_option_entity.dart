// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:convert';

import 'package:equatable/equatable.dart';

class QuestionOptionEntity extends Equatable {
  final int id;
  final String value;
  final int questionId;
  final String questionsOptionsTags;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String status;

  const QuestionOptionEntity({
    required this.id,
    required this.value,
    required this.questionId,
    required this.questionsOptionsTags,
    required this.createdAt,
    required this.updatedAt,
    required this.status,
  });

  QuestionOptionEntity copyWith({
    int? id,
    String? value,
    int? questionId,
    String? questionsOptionsTags,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? status,
  }) {
    return QuestionOptionEntity(
      id: id ?? this.id,
      value: value ?? this.value,
      questionId: questionId ?? this.questionId,
      questionsOptionsTags: questionsOptionsTags ?? this.questionsOptionsTags,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      status: status ?? this.status,
    );
  }

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'value': value,
      'question_id': questionId,
      'questions_options_tags': questionsOptionsTags,
      'created_at': createdAt.millisecondsSinceEpoch,
      'updated_at': updatedAt.millisecondsSinceEpoch,
      'status': status,
    };
  }

  factory QuestionOptionEntity.fromMap(Map<String, dynamic> map) {
    var olderDate = DateTime.fromMillisecondsSinceEpoch(1);
    return QuestionOptionEntity(
      id: int.tryParse(map['id']) ?? 0,
      value: map['value'] as String,
      questionId: int.tryParse(map['question_id']) ?? 0,
      questionsOptionsTags: map['questions_options_tags'] as String,
      createdAt: map['created_at'].runtimeType != Null
          ? DateTime.parse(map['created_at'])
          : olderDate,
      updatedAt: map['updated_at'].runtimeType != Null
          ? DateTime.parse(map['updated_at'])
          : olderDate,
      status: map['status'] as String,
    );
  }

  String toJson() => json.encode(toMap());

  factory QuestionOptionEntity.fromJson(String source) =>
      QuestionOptionEntity.fromMap(json.decode(source) as Map<String, dynamic>);

  @override
  bool get stringify => true;

  @override
  List<Object> get props {
    return [
      id,
      value,
      questionId,
      questionsOptionsTags,
      createdAt,
      updatedAt,
      status,
    ];
  }
}
