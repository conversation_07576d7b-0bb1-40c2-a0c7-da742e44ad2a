import 'dart:convert';

class FriendInviteParamsEntity {
  FriendInviteParamsEntity({
    required this.friendIdentifier,
    required this.roomName,
  });

  final String friendIdentifier;
  final String roomName;

  FriendInviteParamsEntity copyWith({
    String? friendIdentifier,
    String? roomName,
  }) =>
      FriendInviteParamsEntity(
        friendIdentifier: friendIdentifier ?? this.friendIdentifier,
        roomName: roomName ?? this.roomName,
      );

  factory FriendInviteParamsEntity.fromRawJson(String str) =>
      FriendInviteParamsEntity.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory FriendInviteParamsEntity.fromJson(Map<String, dynamic> json) =>
      FriendInviteParamsEntity(
        friendIdentifier: json["friend_identifier"],
        roomName: json["room_name"],
      );

  Map<String, dynamic> toJson() => {
        "friend_identifier": friendIdentifier,
        "room_name": roomName,
      };
}
