// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:convert';

import 'package:equatable/equatable.dart';

import 'package:quycky/app/features/user/domain/entities/user_entity.dart';

class SocketUserEntity extends Equatable {
  final String? socketId;
  final UserEntity userData;
  final bool roomOwner;
  final String? userName;
  final String? roomName;
  final DateTime? updatedAt;
  const SocketUserEntity(
      {this.socketId,
      required this.userData,
      this.userName,
      this.roomName,
      this.updatedAt,
      this.roomOwner = false});

  @override
  // TODO: implement props
  List<Object> get props {
    return [];
  }

  SocketUserEntity copyWith({
    String? socketId,
    UserEntity? userData,
    String? userName,
    String? roomName,
    DateTime? updatedAt,
    bool? roomOwner,
  }) {
    return SocketUserEntity(
        socketId: socketId ?? this.socketId,
        userData: userData ?? this.userData,
        userName: userName ?? this.userName,
        roomName: roomName ?? this.roomName,
        updatedAt: updatedAt ?? this.updatedAt,
        roomOwner: roomOwner ?? this.roomOwner);
  }

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'socketId': socketId,
      'userData': userData.toMap(),
      'userName': userName,
      'roomName': roomName,
      'roomOwner': roomOwner,
      'updated_at': updatedAt?.millisecondsSinceEpoch,
    };
  }

  factory SocketUserEntity.fromMap(Map<String, dynamic> map) {
    return SocketUserEntity(
      socketId: map['socketId'] as String,
      userData: UserEntity.fromJson(map['userData'] as Map<String, dynamic>),
      userName: map['userName'] ?? '',
      roomOwner: map['roomOwner'] as bool,
      roomName: map['roomName'] as String,
      updatedAt: DateTime.tryParse(map['updated_at']),
    );
  }

  String toJson() => json.encode(toMap());

  factory SocketUserEntity.fromJson(String source) =>
      SocketUserEntity.fromMap(json.decode(source) as Map<String, dynamic>);

  @override
  bool get stringify => true;
}
