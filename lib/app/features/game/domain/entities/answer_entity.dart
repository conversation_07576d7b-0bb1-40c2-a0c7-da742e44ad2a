import 'dart:convert';

class AnswerEntity {
  final int currentRound;
  final int questionId;
  final String userIndentifier;
  final String answer;

  AnswerEntity({
    required this.currentRound,
    required this.questionId,
    required this.userIndentifier,
    required this.answer,
  });

  AnswerEntity copyWith({
    int? currentRound,
    int? questionId,
    String? userIndentifier,
    String? answer,
  }) =>
      AnswerEntity(
        currentRound: currentRound ?? this.currentRound,
        questionId: questionId ?? this.questionId,
        userIndentifier: userIndentifier ?? this.userIndentifier,
        answer: answer ?? this.answer,
      );

  factory AnswerEntity.fromRawJson(String str) =>
      AnswerEntity.fromMap(json.decode(str));

  String toRawJson() => json.encode(toMap());

  factory AnswerEntity.fromMap(Map<String, dynamic> json) {
    int? questionId = int.tryParse(json["questionId"]);
    return AnswerEntity(
      currentRound: json["currentRound"],
      questionId: questionId ?? 0,
      userIndentifier: json["userIndentifier"],
      answer: json["answer"],
    );
  }

  Map<String, dynamic> toMap() => {
        "currentRound": currentRound,
        "questionId": questionId,
        "userIndentifier": userIndentifier,
        "answer": answer,
      };
}
