// int pos, FriendInviteParamsEntity friendInvite, UserEntity? friendData

import 'package:equatable/equatable.dart';
import 'package:quycky/app/features/game/domain/entities/friend_invite_params_entity.dart';
import 'package:quycky/app/features/user/domain/entities/user_entity.dart';

class GameFriendInviteEntity extends Equatable {
  final int invitePos;
  final FriendInviteParamsEntity? friendInvite;
  final UserEntity? friendData;

  const GameFriendInviteEntity({
    this.invitePos = -1,
    this.friendInvite,
    this.friendData,
  });

  copyWith({
    int? invitePos,
    FriendInviteParamsEntity? friendInvite,
    UserEntity? friendData,
  }) =>
      GameFriendInviteEntity(
        invitePos: invitePos ?? this.invitePos,
        friendInvite: friendInvite ?? this.friendInvite,
        friendData: friendData ?? this.friendData,
      );

  @override
  List<Object> get props => [];
}
