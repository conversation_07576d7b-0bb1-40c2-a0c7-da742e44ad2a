import 'package:equatable/equatable.dart';

class GameRoundInfoEntity extends Equatable {
  final String currentQuestion;
  final int totalRounds;
  final int currentRound;
  final bool answering;

  const GameRoundInfoEntity(
      {required this.currentQuestion,
      required this.currentRound,
      required this.totalRounds,
      required this.answering});

  copyWith(
          {String? currentQuestion,
          int? currentRound,
          int? totalRounds,
          bool? answering}) =>
      GameRoundInfoEntity(
          currentQuestion: currentQuestion ?? this.currentQuestion,
          currentRound: currentRound ?? this.currentRound,
          totalRounds: totalRounds ?? this.totalRounds,
          answering: answering ?? this.answering);

  @override
  List<Object> get props =>
      [currentQuestion, totalRounds, currentRound, answering];
}
