import 'package:equatable/equatable.dart';

class CurrentGameEntity extends Equatable {
  final int totalRounds;
  final int currentRound;

  const CurrentGameEntity(
      {required this.currentRound, required this.totalRounds});

  copyWith({int? currentRound, int? totalRounds}) => CurrentGameEntity(
      currentRound: currentRound ?? this.currentRound,
      totalRounds: totalRounds ?? this.totalRounds);

  @override
  List<Object> get props => [totalRounds, currentRound];
}
