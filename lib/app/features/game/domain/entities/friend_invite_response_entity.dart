import 'dart:convert';

import 'package:quycky/app/features/user/domain/entities/user_entity.dart';

class FriendInviteResponseEntity {
  FriendInviteResponseEntity({
    required this.user,
    required this.roomName,
  });

  final UserEntity user;
  final String roomName;

  FriendInviteResponseEntity copyWith({
    UserEntity? user,
    String? roomName,
  }) =>
      FriendInviteResponseEntity(
        user: user ?? this.user,
        roomName: roomName ?? this.roomName,
      );

  factory FriendInviteResponseEntity.fromRawJson(String str) =>
      FriendInviteResponseEntity.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory FriendInviteResponseEntity.fromJson(Map<String, dynamic> json) =>
      FriendInviteResponseEntity(
        user: UserEntity.fromJson(json["user"]),
        roomName: json["room_name"],
      );

  Map<String, dynamic> toJson() => {
        "user": user.toMap(),
        "room_name": roomName,
      };
}
