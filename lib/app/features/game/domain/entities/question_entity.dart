// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:convert';

import 'package:equatable/equatable.dart';

import 'package:quycky/app/features/game/domain/entities/question_option_entity.dart';

class QuestionEntity extends Equatable {
  final int id;
  final String description;
  final int isActived;
  final int stage;
  final String type;
  final DateTime createdAt;
  final DateTime updatedAt;
  final List<QuestionOptionEntity> questionsOptions;

  const QuestionEntity({
    required this.id,
    required this.description,
    required this.isActived,
    required this.stage,
    required this.type,
    required this.createdAt,
    required this.updatedAt,
    required this.questionsOptions,
  });

  QuestionEntity copyWith({
    int? id,
    String? description,
    int? isActived,
    int? stage,
    String? type,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<QuestionOptionEntity>? questionsOptions,
  }) {
    return QuestionEntity(
      id: id ?? this.id,
      description: description ?? this.description,
      isActived: isActived ?? this.isActived,
      stage: stage ?? this.stage,
      type: type ?? this.type,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      questionsOptions: questionsOptions ?? this.questionsOptions,
    );
  }

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'description': description,
      'isActived': isActived,
      'stage': stage,
      'type': type,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'updatedAt': updatedAt.millisecondsSinceEpoch,
      'questionsOptions': questionsOptions.map((x) => x.toMap()).toList(),
    };
  }

  factory QuestionEntity.fromMap(Map<String, dynamic> map) {
    var olderDate = DateTime.fromMillisecondsSinceEpoch(1);
    return QuestionEntity(
      id: int.tryParse(map['id'].toString()) ?? 0,
      description: map['description'] as String,
      isActived: int.tryParse(map['is_actived'].toString()) ?? 0,
      stage: int.tryParse(map['stage'].toString()) ?? 0,
      type: map['type'] as String,
      createdAt: map['created_at'].runtimeType != Null
          ? DateTime.parse(map['created_at'])
          : olderDate,
      updatedAt: map['updated_at'].runtimeType != Null
          ? DateTime.parse(map['updated_at'])
          : olderDate,
      questionsOptions: List<QuestionOptionEntity>.from(
        (map['questions_options'] as List<dynamic>).map<QuestionOptionEntity>(
          (x) => QuestionOptionEntity.fromMap(x as Map<String, dynamic>),
        ),
      ),
    );
  }

  String toJson() => json.encode(toMap());

  factory QuestionEntity.fromJson(String source) =>
      QuestionEntity.fromMap(json.decode(source) as Map<String, dynamic>);

  @override
  bool get stringify => true;

  @override
  List<Object> get props {
    return [
      id,
      description,
      isActived,
      stage,
      type,
      createdAt,
      updatedAt,
      questionsOptions,
    ];
  }
}
