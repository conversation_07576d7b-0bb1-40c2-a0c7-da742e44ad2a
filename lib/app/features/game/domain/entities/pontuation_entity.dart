// To parse this JSON data, do
//
//     final pontuationEntity = pontuationEntityFromJson(jsonString);

import 'package:equatable/equatable.dart';
import 'dart:convert';

class PontuationEntity extends Equatable {
  final int currentRound;
  final String userThatSetPontuationIdentifier;
  final String userThatReceivePontuationIdentifier;
  final int pontuation;
  const PontuationEntity({
    required this.currentRound,
    required this.userThatSetPontuationIdentifier,
    required this.userThatReceivePontuationIdentifier,
    required this.pontuation,
  });

  PontuationEntity copyWith({
    int? currentRound,
    String? userThatSetPontuationIdentifier,
    String? userThatReceivePontuationIdentifier,
    int? pontuation,
  }) =>
      PontuationEntity(
        currentRound: currentRound ?? this.currentRound,
        userThatSetPontuationIdentifier: userThatSetPontuationIdentifier ??
            this.userThatSetPontuationIdentifier,
        userThatReceivePontuationIdentifier:
            userThatReceivePontuationIdentifier ??
                this.userThatReceivePontuationIdentifier,
        pontuation: pontuation ?? this.pontuation,
      );

  factory PontuationEntity.fromRawJson(String str) =>
      PontuationEntity.fromMap(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory PontuationEntity.fromMap(Map<String, dynamic> json) {
    return PontuationEntity(
      currentRound: json["currentRound"],
      userThatSetPontuationIdentifier:
          ((json["userThatSetPontuationIdentifier"] as String)),
      userThatReceivePontuationIdentifier:
          ((json["userThatReceivePontuationIdentifier"] as String)),
      pontuation: json["pontuation"],
    );
  }

  Map<String, dynamic> toJson() => {
        "currentRound": currentRound,
        "userThatSetPontuationIdentifier": userThatSetPontuationIdentifier,
        "userThatReceivePontuationIdentifier":
            userThatReceivePontuationIdentifier,
        "pontuation": pontuation,
      };

  @override
  List<Object?> get props => [
        currentRound,
        userThatSetPontuationIdentifier,
        userThatReceivePontuationIdentifier,
        pontuation
      ];
}
