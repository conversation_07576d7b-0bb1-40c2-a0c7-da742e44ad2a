import 'package:dartz/dartz.dart';
import 'package:quycky/app/features/game/domain/entities/friend_invite_params_entity.dart';
import 'package:quycky/app/features/game/domain/entities/friend_invite_response_entity.dart';
import 'package:quycky/core/usecase/errors/failure.dart';

abstract class IGameRepository {
  Future<Either<Failure, FriendInviteResponseEntity>> inviteFriendToPlay(
      FriendInviteParamsEntity params);
}
