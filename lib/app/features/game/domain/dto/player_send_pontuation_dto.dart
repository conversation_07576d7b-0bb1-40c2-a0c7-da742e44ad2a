// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:convert';

import 'package:quycky/core/entities/abs_mappable.dart';

class PlayerSendPontuationDTO implements AbsMappable {
  final int currentRound;
  final String userThatSetPontuationIdentifier;
  final String userThatReceivePontuationIdentifier;
  final int pontuation;
  PlayerSendPontuationDTO({
    required this.currentRound,
    required this.userThatSetPontuationIdentifier,
    required this.userThatReceivePontuationIdentifier,
    required this.pontuation,
  });

  PlayerSendPontuationDTO copyWith({
    int? currentRound,
    String? userThatSetPontuationIdentifier,
    String? userThatReceivePontuationIdentifier,
    int? pontuation,
  }) {
    return PlayerSendPontuationDTO(
      currentRound: currentRound ?? this.currentRound,
      userThatSetPontuationIdentifier: userThatSetPontuationIdentifier ??
          this.userThatSetPontuationIdentifier,
      userThatReceivePontuationIdentifier:
          userThatReceivePontuationIdentifier ??
              this.userThatReceivePontuationIdentifier,
      pontuation: pontuation ?? this.pontuation,
    );
  }

  @override
  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'currentRound': currentRound,
      'userThatSetPontuationIdentifier': userThatSetPontuationIdentifier,
      'userThatReceivePontuationIdentifier':
          userThatReceivePontuationIdentifier,
      'pontuation': pontuation,
    };
  }

  factory PlayerSendPontuationDTO.fromMap(Map<String, dynamic> map) {
    return PlayerSendPontuationDTO(
      currentRound: map['currentRound'] as int,
      userThatSetPontuationIdentifier:
          map['userThatSetPontuationIdentifier'] as String,
      userThatReceivePontuationIdentifier:
          map['userThatReceivePontuationIdentifier'] as String,
      pontuation: map['pontuation'] as int,
    );
  }

  @override
  String toJson() => json.encode(toMap());

  factory PlayerSendPontuationDTO.fromJson(String source) =>
      PlayerSendPontuationDTO.fromMap(
          json.decode(source) as Map<String, dynamic>);

  @override
  String toString() {
    return 'PlayerSendPontuationDTO(currentRound: $currentRound, userThatSetPontuationIdentifier: $userThatSetPontuationIdentifier, userThatReceivePontuationIdentifier: $userThatReceivePontuationIdentifier, pontuation: $pontuation)';
  }

  @override
  bool operator ==(covariant PlayerSendPontuationDTO other) {
    if (identical(this, other)) return true;

    return other.currentRound == currentRound &&
        other.userThatSetPontuationIdentifier ==
            userThatSetPontuationIdentifier &&
        other.userThatReceivePontuationIdentifier ==
            userThatReceivePontuationIdentifier &&
        other.pontuation == pontuation;
  }

  @override
  int get hashCode {
    return currentRound.hashCode ^
        userThatSetPontuationIdentifier.hashCode ^
        userThatReceivePontuationIdentifier.hashCode ^
        pontuation.hashCode;
  }
}
