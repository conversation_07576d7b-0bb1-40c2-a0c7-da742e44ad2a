// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:convert';

import 'package:quycky/core/entities/abs_mappable.dart';

import '../enumerators/egame_action.dart';

class GameMessageDTO implements AbsMappable {
  final EGameAction action;
  final String? authorization;
  final String connectionId;
  final dynamic data;

  GameMessageDTO({
    required this.action,
    required this.connectionId,
    this.authorization,
    this.data,
  });

  GameMessageDTO copyWith({
    EGameAction? action,
    String? connectionId,
    dynamic data,
    String? authorization,
  }) {
    return GameMessageDTO(
      action: action ?? this.action,
      authorization: authorization ?? this.authorization,
      connectionId: connectionId ?? this.connectionId,
      data: data ?? this.data,
    );
  }

  @override
  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'action': action.toString(),
      'authorization': authorization,
      'connectionId': connectionId,
      'data': data
    };
  }

  @override
  factory GameMessageDTO.fromMap(
    dynamic map,
  ) {
    return GameMessageDTO(
      action: EGameAction.fromString(map['action'] as String),
      authorization:
          map['authorization'] != null ? map['authorization'] as String : '',
      connectionId: map['connectionId'] as String,
      data: map['data'],
    );
  }

  @override
  String toJson() => json.encode(toMap());

  factory GameMessageDTO.fromJson(
    String source,
  ) {
    return GameMessageDTO.fromMap(json.decode(source) as dynamic);
  }

  @override
  String toString() =>
      'GameMessageDTO(action: $action, connectionId: $connectionId, data: $data)';

  @override
  bool operator ==(covariant GameMessageDTO other) {
    if (identical(this, other)) return true;

    return other.action == action &&
        other.connectionId == connectionId &&
        other.data == data;
  }

  @override
  int get hashCode => action.hashCode ^ connectionId.hashCode ^ data.hashCode;
}
