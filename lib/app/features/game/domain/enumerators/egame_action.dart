enum EGameAction {
  // outbound
  connect('connect'),
  initiate('initiate'),
  startGame('startGame'),
  cancelGame('cancelGame'),
  addBot('addBot'),
  addBots('addBots'),
  disconnectEndGame('disconnectEndGame'),
  setPlayerAnswer('setPlayerAnswer'),
  setPlayerPontuation('setPlayerPontuation'),
  disconnect('disconnect'),
  // other
  changeState('changeState'),
  // inbound
  welcome('welcome'),
  gameStarting('gameStarting'),
  failure('failure'),
  userEntered('userEntered'),
  userLeft('userLeft'),
  countDown('countDown'),
  isCompleted('isCompleted'),
  gameCancelled('gameCancelled'),
  startRound('startRound'),
  updateAnswers('updateAnswers'),
  reviewRound('reviewRound'),
  playerReceivePontuation('playerReceivePontuation'),
  endGame('endGame'),
  endGameUpdateData('endGameUpdateData');

  final String label;

  @override
  String toString() {
    return label;
  }

  const EGameAction(this.label);

  static EGameAction fromString(String label) {
    return values.firstWhere(
      (v) => v.label == label,
      orElse: () => EGameAction.endGame,
    );
  }
}
