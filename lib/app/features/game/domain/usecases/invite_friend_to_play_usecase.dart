// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:dartz/dartz.dart';
import 'package:quycky/app/features/game/domain/entities/friend_invite_params_entity.dart';
import 'package:quycky/app/features/game/domain/entities/friend_invite_response_entity.dart';
import 'package:quycky/app/features/game/domain/repositories/game_repository.dart';

import 'package:quycky/core/usecase/errors/failure.dart';
import 'package:quycky/core/usecase/usecase.dart';

class InviteFriendToPlayUseCase
    implements UseCase<FriendInviteResponseEntity, FriendInviteParamsEntity> {
  final IGameRepository repository;

  InviteFriendToPlayUseCase(
    this.repository,
  );

  @override
  Future<Either<Failure, FriendInviteResponseEntity>> call(
      FriendInviteParamsEntity params) async {
    return await repository.inviteFriendToPlay(params);
  }
}
