import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:quycky/analytics.dart';
import 'package:quycky/app/features/start/presenter/widgets/intro/intro_card_1.dart';
import 'package:quycky/app/widgets/app_header.dart';
import 'package:quycky/app/widgets/gradient_container.dart';
import 'package:quycky/core/utils/app_routes.dart';

class TutorialPage extends StatefulWidget {
  final String title;
  final bool toHome;

  const TutorialPage(
      {Key? key, this.title = 'TutorialPage', this.toHome = false})
      : super(key: key);

  @override
  TutorialPageState createState() => TutorialPageState();
}

class TutorialPageState extends State<TutorialPage> {
  int currentIntroCardId = 0;

  void _handleChangeValue(int? value) {
    if (value! >= 3) {
      onClose();
      return;
    }

    setState(() {
      currentIntroCardId = value;
    });
  }

  void onNext() {
    _handleChangeValue(currentIntroCardId + 1);
  }

  void onBack() {
    _handleChangeValue(currentIntroCardId - 1);
  }

  void onClose() async {
    await Analytics.instance.logEvent(name: 'intro_complete');
    Modular.to.pushReplacementNamed(
        widget.toHome ? AppRoutes.home : AppRoutes.userRegister());
  }

  Widget getCurrentIntroCard() {
    Widget currentIntroCardWidget =
        IntroCard1(onClose: onClose, onNext: onNext);
    // switch (currentIntroCardId) {
    //   case 0:
    //     currentIntroCardWidget = IntroCard1(onClose: onClose, onNext: onNext);
    //     break;
    //   case 1:
    //     currentIntroCardWidget = IntroCard2(onClose: onClose, onNext: onNext);
    //     break;
    //   case 2:
    //     currentIntroCardWidget = IntroCard3(onClose: onClose, onNext: onNext);
    //     break;
    // }
    return currentIntroCardWidget;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: GradientContainer(
            useDefault: true,
            normalOpacity: 0,
            hotOpacity: 0,
            coldOpacity: 0,
            child: SafeArea(
              child: Stack(
                alignment: Alignment.center,
                children: [
                  const Align(
                      alignment: Alignment.topCenter,
                      child: AppHeader(logo: LogoType.welcome)),
                  // Padding(
                  // padding: const EdgeInsets.symmetric(horizontal: 25.0),
                  // child:
                  Align(
                    alignment: Alignment.center,
                    child: Container(
                        padding: const EdgeInsets.only(top: 45),
                        height: 600,
                        constraints: const BoxConstraints(maxWidth: 359),
                        child: getCurrentIntroCard()),
                  ),
                  // ),
                ],
              ),
            )));
  }
}
