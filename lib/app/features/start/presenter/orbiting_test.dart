import 'package:flutter/material.dart';
import 'dart:math';

Iterable<Rect> getBounds(Rect rect, int length) sync* {
  final s = Size.square(rect.shortestSide / 6.5);
  final radius = (rect.shortestSide - s.shortestSide) * 0.40;
  for (var i = 0; i < length; i++) {
    /// distance +
    final angle = i * pi / 6 + pi * .01;
    final center = rect.center + Offset(cos(angle), sin(angle)) * radius;
    yield Rect.fromCenter(center: center, width: s.width, height: s.height);
  }
}

class Avatar3xBtnMultiChildLayoutDelegate extends MultiChildLayoutDelegate {
  final double iconSize;
  Avatar3xBtnMultiChildLayoutDelegate(this.iconSize);

  @override
  void performLayout(Size size) {
    int id = 1;

    getBounds(Offset.zero & size, 3).forEach(
      (rect) {
        layoutChild(id, BoxConstraints.tight(rect.size));
        positionChild(id, rect.centerRight);
        id++;
      },
    );
  }

  @override
  bool shouldRelayout(
      covariant Avatar3xBtnMultiChildLayoutDelegate oldDelegate) {
    return true;
  }
}

class OrbitingTest extends StatefulWidget {
  const OrbitingTest({super.key});

  @override
  State<StatefulWidget> createState() => _OrbitingTest();
}

class _OrbitingTest extends State<OrbitingTest> {
  double value = 300;
  final iconSize = 24.0;

  Widget getTest() {
    return Column(children: [
      Slider(
          value: 50,
          max: 500,
          onChanged: (v) {
            value = v;
          }),
      Container(
        width: value,
        height: value,
        clipBehavior: Clip.none,
        decoration: BoxDecoration(
          color: Colors.cyanAccent.withOpacity(.2),
          shape: BoxShape.circle,
          border: Border.all(),
        ),
        child: CustomMultiChildLayout(
          delegate: Avatar3xBtnMultiChildLayoutDelegate(iconSize),
          children: [
            LayoutId(
                id: 1,
                child: const Material(
                  color: Colors.purple,
                  shape: CircleBorder(),
                  child: Icon(Icons.one_k, size: 24.0),
                )),
            LayoutId(
                id: 2,
                child: const Material(
                  color: Colors.purple,
                  shape: CircleBorder(),
                  child: Icon(Icons.two_k, size: 24.0),
                )),
            LayoutId(
              id: 3,
              child: const Material(
                color: Colors.purple,
                shape: CircleBorder(),
                child: Icon(Icons.three_k_outlined, size: 24.0),
              ),
            ),
          ],
        ),
      )
    ]);
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: getTest(),
    );
  }
}

void main() => runApp(MyApp());

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      home: Scaffold(
        body: Center(
          child: SizedBox(width: 250, height: 250, child: Orbit()),
        ),
      ),
    );
  }
}

class Orbit extends StatefulWidget {
  @override
  _Orbit createState() => _Orbit();
}

class _Orbit extends State<Orbit> with SingleTickerProviderStateMixin {
  late AnimationController invertedController;
  late AnimationController controller;

  @override
  void initState() {
    super.initState();
    controller = AnimationController(vsync: this);
    controller.repeat(min: 0.0, max: 1.0, period: const Duration(seconds: 7));
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.center,
      children: <Widget>[
        RotationTransition(
          turns: controller,
          child: Align(
            alignment: Alignment.topCenter,
            child: Container(
                decoration: const BoxDecoration(
                  borderRadius: BorderRadius.all(Radius.circular(30)),
                  color: Colors.green,
                ),
                height: 50.0,
                width: 50.0,
                child: const Icon(Icons.abc)),
          ),
        ),
        RotationTransition(
          turns: controller,
          child: Align(
            alignment: Alignment.centerRight,
            child: Container(
              decoration: const BoxDecoration(
                borderRadius: BorderRadius.all(Radius.circular(30)),
                color: Colors.green,
              ),
              height: 50.0,
              width: 50.0,
              child: const Icon(Icons.abc),
            ),
          ),
        ),
        RotationTransition(
          turns: controller,
          child: Align(
            alignment: Alignment.bottomCenter,
            child: Container(
              decoration: const BoxDecoration(
                borderRadius: BorderRadius.all(Radius.circular(30)),
                color: Colors.green,
              ),
              height: 50.0,
              width: 50.0,
              child: const Icon(Icons.abc),
            ),
          ),
        ),
        Container(
          width: 215,
          height: 215,
          decoration: BoxDecoration(
              color: const Color(0xFF0C7D0D).withOpacity(0.2),
              borderRadius: const BorderRadius.all(Radius.circular(120))),
        ),
      ],
    );
  }
}
