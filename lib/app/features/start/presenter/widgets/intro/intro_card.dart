import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:quycky/app/features/start/presenter/widgets/intro/three_circle_widget.dart';
import 'package:quycky/app/services/remote_config/remote_config_store.dart';
import 'package:quycky/app/theme/colors.dart';
import 'package:quycky/app/widgets/button.dart';
import 'package:quycky/app/widgets/custom_image.dart';
import 'package:quycky/core/utils/app_env.dart';
import 'package:quycky/core/utils/assets_strings.dart';
import 'package:quycky/core/utils/quycky_icons_icons.dart';

class IntroCard extends StatefulWidget {
  final Function() onNext;
  final Function() onClose;

  const IntroCard({super.key, required this.onClose, required this.onNext});

  @override
  State<IntroCard> createState() => _IntroCard();
}

class _IntroCard extends State<IntroCard> {
  int stateRadio = 0;
  Map<int, dynamic> cardsData = {};
  int itemsCount = 3;

  void _fillCardsData() {
    String card3Image = Assets.png.intro3;
    if (Platform.isAndroid) {
      final remoteConfigStore = Modular.get<RemoteConfigStore>();
      card3Image = remoteConfigStore.state.popupDisallowedVersions.isNotEmpty &&
              remoteConfigStore.state.popupDisallowedVersions
                  .contains(AppEnv.appVersion)
          ? Assets.png.intro3Android
          : Assets.png.intro3;
    }
    cardsData[0] = {
      'title': 'MATCH',
      'image': Container(
        padding: const EdgeInsets.only(top: 36),
        width: 330,
        child: CustomImage(
          Assets.png.intro1,
        ),
      ), //Assets.png.intro1,
      'comment':
          'Match with players from all around\nthe world and get to know them intimately.',
    };
    cardsData[1] = {
      'title': 'PLAY',
      'image': Container(
        padding: const EdgeInsets.only(top: 36),
        width: 330,
        child: CustomImage(
          Assets.png.intro2,
        ),
      ),
      'comment':
          'Talk about your sex preferences\nthrough a fun and interactive game.',
    };
    cardsData[2] = {
      'title': 'WIN',
      'image': Container(
        padding: const EdgeInsets.only(top: 36),
        width: 330,
        child: CustomImage(
          card3Image,
        ),
      ),
      'comment':
          'Earn real-life rewards, make new friends,\nand discover more about yourself.',
    };
  }

  @override
  void initState() {
    _fillCardsData();
    super.initState();
  }

  void handleChangeValue(int? value) {
    if (value! >= itemsCount) {
      widget.onClose();
      return;
    }

    setState(() {
      stateRadio = value;
    });
  }

  void handleNext() {
    handleChangeValue(stateRadio + 1);
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      color: Colors.white,
      shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
              bottomLeft: Radius.circular(30),
              bottomRight: Radius.circular(30))),
      child: Padding(
        padding: const EdgeInsets.all(2.0),
        child: Container(
          decoration: const BoxDecoration(
              borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(20),
                  topRight: Radius.circular(20),
                  bottomLeft: Radius.circular(30),
                  bottomRight: Radius.circular(30)),
              color: CustomColors.cultured),
          child: Column(
            children: [
              cardsData[stateRadio]['image'],
              Padding(
                padding: const EdgeInsets.only(top: 30, bottom: 20),
                child: Text(
                  cardsData[stateRadio]['title'],
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                      fontFamily: "SFProText",
                      letterSpacing: 4.5,
                      fontWeight: FontWeight.w700,
                      color: CustomColors.fieryRose,
                      fontSize: 30),
                ),
              ),
              Text(
                cardsData[stateRadio]['comment'],
                textAlign: TextAlign.center,
                style: const TextStyle(
                    letterSpacing: 0.65,
                    fontFamily: "SFProText",
                    fontWeight: FontWeight.w500,
                    color: CustomColors.americanPurple,
                    fontSize: 13),
              ),
              const Spacer(),
              Padding(
                padding:
                    const EdgeInsets.only(right: 15.0, left: 15.0, bottom: 15),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    SizedBox(
                      width: 66,
                      height: 33,
                      child: Button(
                        autoSized: true,
                        onPressed: widget.onClose,
                        text: 'SKIP',
                        color: Colors.transparent,
                        textColor: CustomColors.primary,
                      ),
                    ),
                    ThreeCirclesWidget(
                      itemsCount: itemsCount,
                      onChange: handleChangeValue,
                      value: stateRadio,
                    ),
                    SizedBox(
                      width: 66,
                      height: 33,
                      child: Button(
                          autoSized: true,
                          onPressed: handleNext,
                          noAnimation: true,
                          child: const Icon(QuyckyIcons.arrow_right,
                              size: 16, color: Colors.white)),
                    ),
                  ],
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}
