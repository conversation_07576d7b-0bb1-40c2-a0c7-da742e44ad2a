import 'package:flutter/material.dart';
import 'package:quycky/app/theme/colors.dart';
import 'package:quycky/core/utils/quycky_icons_icons.dart';
import 'package:quycky/app/widgets/button.dart';

class IntroCard3 extends StatelessWidget {
  final Function() onNext;
  final Function() onClose;
  const IntroCard3({super.key, required this.onClose, required this.onNext});
  @override
  Widget build(BuildContext context) {
    return Card(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              IconButton(
                  onPressed: onClose,
                  color: CustomColors.americanPurple2,
                  icon: const Icon(QuyckyIcons.close_circle))
            ],
          ),
          const Padding(
            padding: EdgeInsets.only(top: 0, bottom: 30),
            child: Text(
              'USE THE QUYCKIES!',
              textAlign: TextAlign.center,
              style: TextStyle(
                  fontFamily: "SFProText",
                  letterSpacing: 1.4,
                  fontWeight: FontWeight.bold,
                  color: CustomColors.americanPurple,
                  fontSize: 15),
            ),
          ),
          Stack(alignment: AlignmentDirectional.bottomCenter, children: [
            Image.asset("assets/img/png/intro/intro3.png"),
            Padding(
              padding: const EdgeInsets.only(bottom: 8.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    width: 12,
                    height: 12,
                    margin: const EdgeInsets.only(right: 10),
                    decoration: const BoxDecoration(
                        color: CustomColors.charmPink, shape: BoxShape.circle),
                  ),
                  Container(
                    width: 12,
                    height: 12,
                    margin: const EdgeInsets.only(right: 10),
                    decoration: const BoxDecoration(
                        color: CustomColors.charmPink, shape: BoxShape.circle),
                  ),
                  Container(
                    width: 12,
                    height: 12,
                    decoration: const BoxDecoration(
                        color: CustomColors.americanPurple,
                        shape: BoxShape.circle),
                  ),
                ],
              ),
            )
          ]),
          const Padding(
            padding: EdgeInsets.only(top: 30, bottom: 30),
            child: Text(
              'THE QUYCKIES YOU EARN\nUNLOCK A WORLD OF POSSIBILITIES!',
              textAlign: TextAlign.center,
              style: TextStyle(
                  fontFamily: "SFProText",
                  fontWeight: FontWeight.bold,
                  color: CustomColors.americanPurple,
                  fontSize: 14),
            ),
          ),
          Container(
            child: Button(
              text: 'GO',
              onPressed: onNext,
            ),
          ),
        ],
      ),
    );
  }
}
