import 'dart:math';

import 'package:flutter/material.dart';
import 'package:quycky/core/utils/interval_utils.dart';

class WelcomePageAnimatedBackground extends StatefulWidget {
  final Widget child;
  const WelcomePageAnimatedBackground({super.key, required this.child});

  @override
  _WelcomePageAnimatedBackgroundState createState() =>
      _WelcomePageAnimatedBackgroundState();
}

class _WelcomePageAnimatedBackgroundState
    extends State<WelcomePageAnimatedBackground>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<Color?> _colorAnimation;
  int movementFactorX = 2;
  int movementFactorY = 2;

  final List<Color> colors = [
    // const Color(0xFFFF5F7C),
    const Color.fromARGB(255, 101, 23, 104),
    const Color.fromARGB(255, 101, 23, 104),
    const Color.fromARGB(255, 101, 23, 104),
    const Color.fromARGB(255, 101, 23, 104),
    const Color.fromARGB(255, 101, 23, 104),
    const Color.fromARGB(255, 101, 23, 104),
    const Color.fromARGB(255, 101, 23, 104),
    const Color(0xFFFF7FA5),
    const Color.fromARGB(255, 255, 77, 133),
    const Color.fromARGB(255, 255, 77, 133),
    const Color(0xFFFF5F7C),
    const Color(0xFFF85A3E),
    const Color(0xFFFF7FA5)
  ];
  // const t = [
  //   const Color(0xFF471249),
  //   const Color(0xFFF85A3E),
  //   const Color(0xFFFF5F7C),
  //   const Color(0xFF87558B),
  //   const Color(0xFFE63B2E),
  //   const Color(0xFFFF7FA5),
/**
  const Color(0xFFFF5F7C),
  const Color(0xFF471249),
  const Color(0xFF87558B),
  const Color(0xFFFF7FA5),
  const Color(0xFFFF7FA5),
  const Color.fromARGB(255, 255, 77, 133),
  const Color(0xFFFF5F7C),
  const Color(0xFFF85A3E),
  const Color(0xFFFF7FA5),**/
  // ];
  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 10),
      vsync: this,
    )..forward();
    _controller.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        setTimeout(
            duration: const Duration(milliseconds: 200),
            callback: _controller.reverse);
        return;
      }
      if (status == AnimationStatus.dismissed) {
        movementFactorX = Random().nextInt(3) + 1;
        movementFactorY = Random().nextInt(3) + 1;
        setTimeout(
            duration: const Duration(milliseconds: 300),
            callback: _controller.forward);
      }
    });
    final tweens = <TweenSequenceItem<Color?>>[];
    for (int i = 0; i < colors.length; i++) {
      tweens.add(
        TweenSequenceItem<Color?>(
          tween: ColorTween(
            begin: colors[i],
            end: colors[(i + 1) % colors.length],
          ),
          weight: 1,
        ),
      );
    }

    _colorAnimation = TweenSequence(tweens).animate(_controller);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: AnimatedBuilder(
          animation: _colorAnimation,
          builder: (context, child) {
            // final colorIndex = colors.indexOf(_colorAnimation.value!);
            // final nextColor = colors[(colorIndex + 1) % colors.length];

            return Container(
              decoration: BoxDecoration(
                gradient: RadialGradient(
                  colors: [
                    _colorAnimation.value!,
                    const Color(0xFFF85A3E),
                  ],
                  center: Alignment(
                    (_controller.value * movementFactorX - 1),
                    (_controller.value * movementFactorY - 1),
                  ),
                  radius: 2,
                  tileMode: TileMode.mirror,
                ),
              ),
              child: child,
            );
          },
          child: widget.child),
    );
  }
}
