import 'dart:io';

import 'package:quycky/app/features/user/data/models/user_iq_model.dart';
import 'package:quycky/app/features/user/data/models/user_login_model.dart';
import 'package:quycky/app/features/user/data/models/user_login_result_model.dart';
import 'package:quycky/app/features/user/data/models/user_model.dart';

abstract class IUserDatasource {
  Future<int> getNumberOfRoundsOfLoggedPlayer();
  Future<UserModel> getUserById(int id);
  Future<UserModel> registerUser(UserModel user);
  Future<UserLoginResultModel> doLogin(UserLoginModel loginData);
  Future<UserLoginResultModel> doLoginWithGoogle(UserLoginModel loginData);
  Future<UserLoginResultModel> doLoginWithApple(UserLoginModel loginData);
  Future<UserModel> uploadUserAvatar(File avatar);
  Future<UserModel> updateUser(UserModel user);
  Future<UserModel> getUserByFriendUserId(int friendUserId);
  Future<UserIQModel> getUserIQ();
  Future<bool> deleteUserAccount(int id);
}
