import 'package:quycky/core/utils/server_url.dart';

class UserEndpoints {
  static String register() => ServerUrl.getConfiguredUrl("register");
  static String update(String id) => ServerUrl.getConfiguredUrl("users/$id");
  static String getUserById(int id) =>
      ServerUrl.getConfiguredUrl("users/id?id=$id");
  static String uploadAvatar() => ServerUrl.getConfiguredUrl("upload-avatar");
  static String getUserbyProfileUserId(int id) => ServerUrl.getConfiguredUrl(
      "users/getProfileByFriendUserId?friend_id=$id");
  static String userLogin() => ServerUrl.getConfiguredUrl("login");
  static String userAppleLogin() => ServerUrl.getConfiguredUrl("login/apple");
  static String userGoogleLogin() => ServerUrl.getConfiguredUrl("login/google");
  static String userGetNumberOfRoundsOfLoggedPlayer() =>
      ServerUrl.getConfiguredUrl("users/getNumberOfRoundsByPlayerId");
  static String deleteUserAccount(int id) =>
      ServerUrl.getConfiguredUrl("users/eraseById?id=$id");
  static String getIQ() => ServerUrl.getConfiguredUrl("users/iq");
}
