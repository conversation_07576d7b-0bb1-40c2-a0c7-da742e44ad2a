// To parse this JSON data, do
//
//     final UserIQModel = UserIQModelFromJson(jsonString);

import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:quycky/app/features/user/domain/entities/user_iq_entity.dart';
import 'package:quycky/core/utils/type_converters.dart';

class UserIQModel extends UserIQEntity {
  const UserIQModel({progress, info}) : super(progress: progress, info: info);
  @override
  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'progress': progress,
      'info': info.map((x) => x.toMap()).toList(),
    };
  }

  factory UserIQModel.fromMap(Map<String, dynamic> map) {
    var t = [];
    try {
      t = List<UserIqInfoEntity>.from(map["info"]
              .map((x) => UserIqInfoEntity.fromMap(x as Map<String, dynamic>)))
          .toList();
      t.sort((a, b) => b.stat.compareTo(a.stat));
    } catch (err) {
      debugPrint('uIQM=>$err');
    }
    var v = UserIQModel(progress: dynamicToDouble(map['progress']), info: t);
    return v;
  }

  factory UserIQModel.fromJson(String source) =>
      UserIQModel.fromMap(json.decode(source) as Map<String, dynamic>);
}
