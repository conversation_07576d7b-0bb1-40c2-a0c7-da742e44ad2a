// To parse this JSON data, do
//
//     final UserLoginModel = UserLoginModelFromJson(jsonString);

import 'dart:convert';

import 'package:quycky/app/features/user/domain/entities/user_login_entity.dart';

class UserLoginModel extends UserLoginEntity {
  const UserLoginModel({
    id = '',
    identifier = '',
    password = '',
    onesignalId = '',
  }) : super(
          id: id,
          identifier: identifier,
          password: password,
          onesignalId: onesignalId,
        );

  factory UserLoginModel.fromRawJson(String str) =>
      UserLoginModel.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory UserLoginModel.fromJson(Map<String, dynamic> json) => UserLoginModel(
        id: json['id'],
        identifier: json['identifier'],
        password: json['password'],
        onesignalId: json['onesignal_id'],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "identifier": identifier,
        "password": password,
        "onesignal_id": onesignalId,
      };
}
