// To parse this JSON data, do
//
//     final userModel = userModelFromJson(jsonString);

import 'dart:convert';

import 'package:quycky/app/features/user/domain/entities/user_entity.dart';
import 'package:quycky/core/utils/type_converters.dart';

class UserModel extends UserEntity {
  const UserModel({
    id,
    uuid,
    appleId,
    googleId,
    required name,
    email,
    phoneNumber,
    identifier,
    required onesignalId,
    createdAt,
    updatedAt,
    avatar,
    avatarUrl,
    isConnected = false,
    pontuation = 0,
  }) : super(
          id: id,
          uuid: uuid,
          appleId: appleId,
          googleId: googleId,
          name: name,
          email: email,
          phoneNumber: phoneNumber,
          identifier: identifier,
          onesignalId: onesignalId,
          createdAt: createdAt,
          updatedAt: updatedAt,
          avatar: avatar,
          avatarUrl: avatarUrl,
          isConnected: isConnected,
          pontuation: pontuation,
        );
  @override
  factory UserModel.fromRawJson(String str) =>
      UserModel.fromJson(json.decode(str));
  @override
  String toRawJson() => json.encode(toJson());

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json["id"] ?? '',
      uuid: json["uuid"] ?? '',
      appleId: json["apple_id"] ?? '',
      googleId: json["google_id"] ?? '',
      name: json["name"] ?? '',
      email: json["email"] ?? '',
      phoneNumber: json["phone_number"] ?? '',
      identifier: json["identifier"] ?? '',
      onesignalId: json["onesignal_id"] ?? '',
      createdAt: DateTime.parse(json["created_at"]),
      updatedAt: DateTime.parse(json["updated_at"]),
      avatar: json["avatar"] ?? '',
      avatarUrl: json["avatarUrl"] ?? '',
      isConnected: json["isConnected"] != null
          ? stringToBoolean(json["isConnected"].toString())
          : false,
      pontuation: json["pontuation"] != null
          ? json["pontuation"] is int
              ? json["pontuation"]
              : int.parse(json["pontuation"])
          : 0,
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "uuid": uuid,
        "apple_id": appleId,
        "google_id": googleId,
        "name": name,
        "email": email == '' ? null : email,
        "phone_number": phoneNumber == '' ? null : phoneNumber,
        "identifier": identifier,
        "onesignal_id": onesignalId,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "isConnected": isConnected,
        "pontuation": pontuation,
        "avatar": avatar,
        "avatarUrl": avatarUrl,
      };
}
