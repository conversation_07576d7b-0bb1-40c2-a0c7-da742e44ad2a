import 'dart:io';
import 'dart:typed_data';

import 'package:dartz/dartz.dart';
import 'package:quycky/app/features/user/data/datasources/user_datasource.dart';
import 'package:quycky/app/features/user/data/models/user_login_model.dart';
import 'package:quycky/app/features/user/data/models/user_model.dart';
import 'package:quycky/app/features/user/domain/entities/user_entity.dart';
import 'package:quycky/app/features/user/domain/entities/user_iq_entity.dart';
import 'package:quycky/app/features/user/domain/entities/user_login_entity.dart';
import 'package:quycky/app/features/user/domain/entities/user_login_return_entity.dart';
import 'package:quycky/app/features/user/domain/repositories/user_repository.dart';
import 'package:quycky/core/usecase/errors/exceptions.dart';
import 'package:quycky/core/usecase/errors/failure.dart';

class UserRepositoryImplementation implements IUserRepository {
  final IUserDatasource datasource;

  UserRepositoryImplementation(this.datasource);

  @override
  Future<Either<Failure, int>> getNumberOfRoundsOfLoggedPlayer() async {
    try {
      final result = await datasource.getNumberOfRoundsOfLoggedPlayer();
      return Right(result);
    } on UnauthorizedException {
      return Left(UnauthorizedFailure());
    } on Exception {
      return const Left(ServerFailure());
    }
  }

  @override
  Future<Either<Failure, UserEntity>> getUserById(int id) async {
    try {
      final result = await datasource.getUserById(id);
      return Right(result);
    } on UnauthorizedException {
      return Left(UnauthorizedFailure());
    } on Exception {
      return const Left(ServerFailure());
    }
  }

  @override
  Future<Either<Failure, UserLoginResultEntity>> doLogin(
      UserLoginEntity loginData) async {
    try {
      final result = await datasource.doLogin(UserLoginModel(
          id: loginData.id,
          identifier: loginData.identifier,
          onesignalId: loginData.onesignalId,
          password: loginData.password));
      return Right(result);
    } on UnauthorizedException {
      return Left(UnauthorizedFailure());
    } on Exception {
      return const Left(ServerFailure());
    }
  }

  @override
  Future<Either<Failure, UserLoginResultEntity>> doLoginWithApple(
      UserLoginEntity loginData) async {
    try {
      final result = await datasource.doLoginWithApple(UserLoginModel(
          id: loginData.id,
          identifier: loginData.identifier,
          onesignalId: loginData.onesignalId,
          password: loginData.password));
      return Right(result);
    } on UnauthorizedException {
      return Left(UnauthorizedFailure());
    } on Exception {
      return const Left(ServerFailure());
    }
  }

  @override
  Future<Either<Failure, UserLoginResultEntity>> doLoginWithGoogle(
      UserLoginEntity loginData) async {
    try {
      final result = await datasource.doLoginWithGoogle(UserLoginModel(
          id: loginData.id,
          identifier: loginData.identifier,
          onesignalId: loginData.onesignalId,
          password: loginData.password));
      return Right(result);
    } on UnauthorizedException {
      return Left(UnauthorizedFailure());
    } on Exception {
      return const Left(ServerFailure());
    }
  }

  @override
  Future<Either<Failure, UserEntity>> getUserByFriendUserId(int friendUserId) {
    // TODO: implement getUserByFriendUserId
    throw UnimplementedError();
  }

  @override
  Future<Either<Failure, UserEntity>> registerUser(UserEntity user) async {
    try {
      final result = await datasource.registerUser(UserModel(
          name: user.name,
          onesignalId: user.onesignalId,
          appleId: user.appleId,
          avatar: user.avatar,
          avatarUrl: user.avatarUrl,
          email: user.email,
          googleId: user.googleId,
          id: user.id,
          identifier: user.identifier,
          phoneNumber: user.phoneNumber,
          uuid: user.uuid));
      return Right(result);
    } on Exception {
      return const Left(ServerFailure());
    }
  }

  @override
  Future<Either<Failure, UserEntity>> updateUser(UserEntity user) async {
    try {
      final result = await datasource.updateUser(UserModel(
          name: user.name,
          onesignalId: user.onesignalId,
          appleId: user.appleId,
          avatar: user.avatar,
          avatarUrl: user.avatarUrl,
          email: user.email,
          googleId: user.googleId,
          id: user.id,
          identifier: user.identifier,
          phoneNumber: user.phoneNumber,
          uuid: user.uuid));
      return Right(result);
    } on Exception {
      return const Left(ServerFailure());
    }
  }

  @override
  Future<Either<Failure, UserEntity>> uploadUserAvatar(File avatar) async {
    try {
      final result = await datasource.uploadUserAvatar(avatar);
      return Right(result);
    } on Exception {
      return const Left(ServerFailure());
    }
  }

  @override
  Future<Either<Failure, UserIQEntity>> getUserIQ() async {
    try {
      final result = await datasource.getUserIQ();
      return Right(result);
    } on Exception {
      return const Left(ServerFailure());
    }
  }

  @override
  Future<Either<Failure, bool>> deleteUserAccount(int id) async {
    try {
      final result = await datasource.deleteUserAccount(id);
      return Right(result);
    } on Exception {
      return const Left(ServerFailure());
    }
  }
}
