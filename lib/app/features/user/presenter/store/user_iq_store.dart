import 'package:flutter_triple/flutter_triple.dart';
import 'package:quycky/app/features/user/domain/entities/user_iq_entity.dart';
import 'package:quycky/app/features/user/domain/entities/user_iq_store_entity.dart';
import 'package:quycky/core/usecase/errors/failure.dart';

class UserIQStore extends Store<UserIQStoreEntity> {
  UserIQStore() : super(const UserIQStoreEntity());

  setIQ(UserIQEntity iq, {updateMinAndMax = true}) {
    setLoading(true);
    int xMin = state.xMin;
    int xMax = state.xMax;

    update(state.copyWith(iq: iq, xMin: xMin, xMax: xMax), force: true);
    setLoading(false);
  }
}
