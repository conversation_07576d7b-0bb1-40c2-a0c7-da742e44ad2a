import 'dart:io';
import 'dart:typed_data';

import 'package:flutter_triple/flutter_triple.dart';
import 'package:quycky/app/features/user/domain/usecases/update_user_usecase.dart';
import 'package:quycky/app/features/user/domain/usecases/upload_user_avatar_usecase.dart';
import 'package:quycky/app/features/user/domain/entities/user_entity.dart';
import 'package:quycky/app/features/user/domain/entities/user_login_entity.dart';
import 'package:quycky/app/features/user/domain/entities/user_login_return_entity.dart';
import 'package:quycky/app/features/user/domain/usecases/login_usecase.dart';
import 'package:quycky/app/features/user/domain/usecases/register_user_usecase.dart';
import 'package:quycky/app/features/user/presenter/storage/user_storage.dart';
import 'package:quycky/core/usecase/errors/failure.dart';
import 'package:share_plus/share_plus.dart';

class UserStoreFake extends Store<UserLoginResultEntity> {
  UserStoreFake(
      {initialState = const UserLoginResultEntity(
          isReview: false,
          token: '',
          blacklist: [],
          isGreaterThan18: true,
          user: UserEntity(
            avatarUrl:
                'https://static.wikia.nocookie.net/vsbattles/images/3/3f/Liontransparent.png/revision/latest/scale-to-width-down/1200?cb=20180323225034',
            name: 'Melissa Soda',
          ))})
      : super(initialState);
}

class UserStore extends Store<UserLoginResultEntity> {
  final UpdateUserUseCase _updateUserUseCase;
  final RegisterUserUseCase _registerUserUseCase;
  final LoginUseCase _loginUseCase;
  final UploadUserAvatarUseCase _uploadUserAvatarUseCase;
  final UserStorage _userStorage;

  UserStore(this._registerUserUseCase, this._updateUserUseCase,
      this._loginUseCase, this._uploadUserAvatarUseCase, this._userStorage)
      : super(const UserLoginResultEntity(
            user: UserEntity(name: ''),
            token: '',
            isReview: false,
            isGreaterThan18: false)) {
    getLoginResultData();
  }
  setIsGreaterThan18(bool value) {
    update(state.copyWith(isGreaterThan18: value), force: true);
  }

  get isGreaterThan18 => state.isGreaterThan18;

  bool get isUserLogged => state.user.id != '';

  setUserToken(String token) {
    update(state.copyWith(token: token), force: true);
  }

  setUser(UserEntity userData) {
    update(state.copyWith(user: userData), force: true);
  }

  Future<String> getToken() async {
    String res =
        state.token != '' ? state.token : await _userStorage.getUserToken();

    return res;
  }

  setData(UserLoginResultEntity data) {
    _userStorage.setUserToken(data.token);
    _userStorage.setIsReview(data.isReview);
    _userStorage.setUser(data.user);
    update(data, force: true);
  }

  setAvatarUrl(String? img) {
    update(state.copyWith(
        user: UserEntity(name: DateTime.now().toString(), avatarUrl: img)));
  }

  SetError(Failure err) {
    setError(err, force: true);
  }

  cleanStore() {
    update(const UserLoginResultEntity(
        user: UserEntity(name: ''),
        token: '',
        isReview: false,
        isGreaterThan18: false));
  }

  getLoginResultData() async {
    UserEntity? userData = await _userStorage.getUser();
    userData = userData ?? const UserEntity(name: '');
    String token = await _userStorage.getUserToken();
    bool isReview = await _userStorage.getIsReview();
    update(
        UserLoginResultEntity(user: userData, token: token, isReview: isReview),
        force: true);
  }

  addFriend() async {
    UserEntity? userData = await _userStorage.getUser();
    if (userData == null) return;
    String token = await _userStorage.getUserToken();
    bool isReview = await _userStorage.getIsReview();
    update(state.copyWith(user: userData, token: token, isReview: isReview));
  }

  Future<bool> doLogin(UserLoginEntity loginData) async {
    bool ret = false;
    setLoading(true);
    final result = await _loginUseCase(loginData);
    result.fold((left) => {setError(left)}, (right) {
      setData(right);
      ret = true;
    });
    setLoading(false);
    return ret;
  }

  Future<UserEntity?> registerUser(UserEntity user) async {
    UserEntity? ret;
    setLoading(true);
    try {
      final result = await _registerUserUseCase(user);
      result.fold((left) => {setError(left)}, (right) {
        update(state.copyWith(user: right));
        ret = right;
      });
    } catch (e) {}
    setLoading(false);
    return ret;
  }

  Future<UserEntity?> updateUser(UserEntity user) async {
    UserEntity? ret;
    setLoading(true, force: true);
    try {
      final result = await _updateUserUseCase(user);
      result.fold((left) => {setError(left)}, (right) {
        ret = right;
      });
    } catch (e) {}
    setLoading(false, force: true);
    return ret;
  }

  Future<UserEntity?> uploadAvatar(File file) async {
    UserEntity? ret;
    setLoading(true);
    final result = await _uploadUserAvatarUseCase(file);
    result.fold((left) => {setError(left)}, (right) {
      update(state.copyWith(user: right), force: true);
      ret = right;
      _userStorage.setUser(ret ?? state.user);
    });
    setLoading(false);
    return ret;
  }
}
