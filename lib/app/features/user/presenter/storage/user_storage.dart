import 'package:quycky/app/features/user/domain/entities/user_entity.dart';
import 'package:quycky/core/services/storage/storage_client.dart';
import 'package:quycky/core/utils/app_storage_keys.dart';

class UserStorage {
  final StorageClient _storage;
  UserStorage(this._storage);

  setUserToken(String token) async {
    return await _storage.write(AppStorageKeys.userToken, token);
  }

  setIsReview(bool isReview) async {
    return await _storage.write(AppStorageKeys.isReview, isReview.toString());
  }

  setAlreadyPlayed(bool alreadyPlayed) async {
    return await _storage.write(
        AppStorageKeys.alreadyPlayed, alreadyPlayed.toString());
  }

  Future<bool> setUser(UserEntity? userData) async {
    String userJson = userData != null ? userData.toRawJson() : '';
    return await _storage.write(AppStorageKeys.userData, userJson);
  }

  setPushId(String pushId) async {
    return await _storage.write(AppStorageKeys.pushId, pushId);
  }

  Future<bool> getIsReview() async {
    return (await _storage.read(AppStorageKeys.isReview)) == 'true';
  }

  Future<bool> getAlreadyPlayed() async {
    return (await _storage.read(AppStorageKeys.alreadyPlayed)) == 'true';
  }

  Future<String> getPushId() async {
    return (await _storage.read(AppStorageKeys.pushId)) ?? '';
  }

  Future<String> getUserToken() async {
    // return "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyIjp7ImlkIjoiMzAzIiwidXVpZCI6ImZmZjk2ZDNjLWU0OGMtNDE5YS1hZWQ4LWY5YjA5YWJiMWE1MiIsImFwcGxlX2lkIjoiIiwiZ29vZ2xlX2lkIjoiIiwibmFtZSI6IlVzZXJUZXN0MDAyIiwiZW1haWwiOm51bGwsInBob25lX251bWJlciI6bnVsbCwiaWRlbnRpZmllciI6ImNjMjFkM2Y1LWZiNTAtNDBkOC05YWY2LTI3YjcxNmUwMjIwMyIsInBhc3N3b3JkIjoiJDJhJDA4JDB4VGoxekdudlFrVXV0T1JLejRienUwLlBobkN1V3hIYy8xdUpLV0toWW5JVm0ycDJDTWxtIiwib25lc2lnbmFsX2lkIjpudWxsLCJjcmVhdGVkX2F0IjoiMjAyMy0wMS0yNVQwNzoxNDo1Mi4wMDBaIiwidXBkYXRlZF9hdCI6IjIwMjMtMDEtMjVUMDc6MTQ6NTIuMDAwWiIsImF2YXRhciI6bnVsbH0sImlhdCI6MTY3NDYzMDkwNywiZXhwIjoxNjc1MjM1NzA3LCJzdWIiOiJmZmY5NmQzYy1lNDhjLTQxOWEtYWVkOC1mOWIwOWFiYjFhNTIifQ.P0SyJK2cy7xQRYjVtDkyqJvV8YxGBuNWz4IUPO2edL4";
    return (await _storage.read(AppStorageKeys.userToken)) ?? '';
  }

  Future<UserEntity?> getUser() async {
    UserEntity? res;
    String? userJson = await _storage.read(AppStorageKeys.userData);
    // userJson = "{\"id\":\"13078\",\"uuid\":\"c8f3d81e-bd8c-4b3b-9b21-24656949e5a9\",\"apple_id\":\"\",\"google_id\":\"\",\"name\":\"USER_TEST!\",\"email\":null,\"phone_number\":null,\"identifier\":\"fd6364ba-620a-4999-aecc-3c2c2fc7e2a4\",\"onesignal_id\":\"\",\"created_at\":\"2024-02-03T02:15:51.000Z\",\"updated_at\":\"2024-02-09T02:13:58.000Z\",\"isConnected\":false,\"pontuation\":0,\"avatar\":\"avatar_4a8f813e32cd6074e8a7.jpg\",\"avatarUrl\":\"https://quycky-space.s3.eu-central-1.amazonaws.com/avatar/avatar_4a8f813e32cd6074e8a7.jpg\"}";
    if (userJson!.isNotEmpty) {
      res = UserEntity.fromRawJson(userJson);
    }
    return res;
  }
}
