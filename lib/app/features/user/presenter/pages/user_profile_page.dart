import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:quycky/app/features/user/presenter/pages/state/user_profile_iq_state.dart';
import 'package:quycky/app/services/remote_config/remote_config_store.dart';

class UserProfilePage extends StatefulWidget {
  final String title;
  late final State<UserProfilePage> _pageState;

  UserProfilePage({Key? key, this.title = 'UserProfilePage'})
      : super(key: key) {
    final remoteConfig = Modular.get<RemoteConfigStore>();
    _pageState = UserProfileIQState();
  }

  @override
  State<UserProfilePage> createState() => _pageState;
}
