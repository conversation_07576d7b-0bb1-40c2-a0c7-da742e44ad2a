import 'package:flutter/material.dart';

class ProfileThreeCirclesAnimated extends StatefulWidget {
  final Duration duration;
  final Function()? onTap;
  const ProfileThreeCirclesAnimated(
      {super.key,
      this.duration = const Duration(milliseconds: 1500),
      this.onTap});

  @override
  _ProfileThreeCirclesAnimatedState createState() =>
      _ProfileThreeCirclesAnimatedState();
}

class _ProfileThreeCirclesAnimatedState
    extends State<ProfileThreeCirclesAnimated>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scale;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    )..repeat(reverse: true);
    _scale = Tween<double>(begin: 1.0, end: 1.5).animate(_controller);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void handleOnTap() {
    if (widget.onTap != null) {
      widget.onTap!();
    }
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: handleOnTap,
      child: AnimatedBuilder(
        animation: _controller,
        builder: (context, child) => SizedBox(
          width: 100,
          height: 20,
          child: CustomPaint(
            painter: ProfileThreeCirclesPainter(scale: _scale.value),
          ),
        ),
      ),
    );
  }
}

class ProfileThreeCirclesPainter extends CustomPainter {
  final double scale;

  ProfileThreeCirclesPainter({this.scale = 1.0});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..color = Colors.grey;
    const radius = 5.0; // 10px de diâmetro = 5px de raio

    canvas.drawCircle(
        Offset(size.width / 4, size.height / 2), radius * scale, paint);
    canvas.drawCircle(
        Offset(size.width / 2, size.height / 2), radius * scale, paint);
    canvas.drawCircle(
        Offset(3 * size.width / 4, size.height / 2), radius * scale, paint);
  }

  @override
  bool shouldRepaint(ProfileThreeCirclesPainter oldDelegate) =>
      scale != oldDelegate.scale;
}
