import 'package:flutter/material.dart';
import 'package:quycky/app/theme/colors.dart';
import 'package:quycky/core/utils/quycky_icons_icons.dart';
import 'package:quycky/core/utils/type_converters.dart';

class UserProgressBar extends StatelessWidget {
  final double progressValue;

  const UserProgressBar({super.key, this.progressValue = 0});

  String getProgressFormattedValue() {
    return decimalToPercentageConverter(progressValue);
  }

  Widget getWidget() {
    return Align(
      alignment: Alignment.bottomCenter,
      child: Container(
          width: 288,
          transform: Matrix4.translationValues(0, -28, 0),
          child: Transform.translate(
            offset:
                Offset(progressValue * (progressValue > 0.5 ? 265 : 275), 0),
            child: SizedBox(
              width: 5,
              height: 39,
              child: Stack(
                children: [
                  const Positioned(
                    bottom: 7,
                    child: Icon(QuyckyIcons.arrow_down_rounded,
                        size: 13, color: CustomColors.brinkPink),
                  ),
                  Positioned(
                    top: 0,
                    child: Text("${getProgressFormattedValue()}%",
                        style: const TextStyle(
                            fontFamily: "Roboto",
                            fontWeight: FontWeight.w600,
                            color: CustomColors.americanPurple,
                            fontSize: 13)),
                  ),
                ],
              ),
            ),
          )),
    );
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 288,
      height: 70,
      child: Stack(
        children: [
          Align(
            alignment: Alignment.bottomCenter,
            child: Container(
              decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade200, width: 15),
                  borderRadius: const BorderRadius.all(Radius.circular(37))),
              child: SizedBox(
                height: 12,
                child: ClipRRect(
                  borderRadius: const BorderRadius.all(Radius.circular(23)),
                  child: LinearProgressIndicator(
                      minHeight: 4,
                      borderRadius: const BorderRadius.all(Radius.circular(30)),
                      backgroundColor: Colors.white,
                      value: progressValue,
                      color: CustomColors.brinkPink),
                ),
              ),
            ),
          ),
          getWidget(),
        ],
      ),
    );
  }
}

class RedTrianglePainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final path = Path();
    path.moveTo(size.width / 2, 0);
    path.lineTo(0, size.height);
    path.lineTo(size.width, size.height);
    path.close();

    final paint = Paint()..color = Colors.red;
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(RedTrianglePainter oldDelegate) => false;
}
