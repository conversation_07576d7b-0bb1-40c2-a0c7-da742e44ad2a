import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_triple/flutter_triple.dart';
import 'package:quycky/app/features/friendship/domain/entities/friendship_profile_entity.dart';
import 'package:quycky/app/features/friendship/domain/entities/friendship_store_entity.dart';
import 'package:quycky/app/features/friendship/presenter/controllers/friendship_controller.dart';
import 'package:quycky/app/features/friendship/presenter/store/friendship_store.dart';
import 'package:quycky/app/features/friendship/presenter/pages/widgets/history_friend_card.dart';
import 'package:quycky/app/services/remote_config/remote_config_store.dart';
import 'package:quycky/app/theme/colors.dart';
import 'package:skeletonizer/skeletonizer.dart';

class UserProfileHistory extends StatefulWidget {
  final String id;
  final String title;
  final Widget Function(Widget)? customScrollView;

  const UserProfileHistory(
      {super.key,
      this.title = 'UserProfileHistory',
      required this.id,
      this.customScrollView});

  @override
  UserProfileHistoryState createState() => UserProfileHistoryState();
}

class UserProfileHistoryState extends State<UserProfileHistory> {
  final _remoteConfig = Modular.get<RemoteConfigStore>();
  final FriendshipController _controller = Modular.get<FriendshipController>();
  final FriendshipStore _store = Modular.get<FriendshipStore>();
  final List<FriendshipAnswerEntity> defaultAnswers = [
    FriendshipAnswerEntity(
        id: '1',
        createdAt: DateTime.now(),
        points: [],
        questionId: '',
        roomId: '',
        userId: '',
        value: '',
        updatedAt: DateTime.now(),
        room: FriendshipRoomEntity(
            createdAt: DateTime.now(),
            howManyPeople: 4,
            id: '',
            isBloqued: false,
            name: '',
            questionTimeInSeconds: 8,
            responseTimeInSeconds: 8,
            updatedAt: DateTime.now()),
        question: FriendshipQuestionEntity(
            isActived: 1,
            updatedAt: DateTime.now(),
            createdAt: DateTime.now(),
            description: '',
            id: '',
            stage: 1)),
  ];

  @override
  void initState() {
    super.initState();
    if (_store.state.friendProfile == null) {
      _controller.getFriendProfile(int.parse(widget.id));
    }
  }

  int getPontuation(List<FriendshipPointEntity> points) {
    int res = 0;
    final pontuation = _remoteConfig.state.pontuation;

    for (int i = 0; i < points.length; i++) {
      res += points[i].pointTypeId == 1 ? pontuation.fire : pontuation.ice;
    }

    return res;
  }

  Widget getListItem(FriendshipAnswerEntity item) {
    return HistoryFriendCard(
        timeText: getWhenAnsweredLabel(item.createdAt),
        pontuation: getPontuation(item.points),
        answer: item.question.description,
        response: item.value,
        timeTextColor: CustomColors.americanPurple2);
  }

  String getWhenAnsweredLabel(DateTime date) {
    String result = "";
    final now = DateTime.now();
    Duration durationDifference = now.difference(date);

    if (durationDifference.inMinutes < 60) {
      result = "just now";
    } else if (durationDifference.inMinutes < 1440) {
      result = durationDifference.inHours > 1
          ? "$durationDifference.inHours hours ago"
          : "$durationDifference.inHours hour ago";
    } else {
      result = "1 day ago";
    }
    return result;
  }

  Widget getList(List<FriendshipAnswerEntity> answers) {
    List<Widget> items = [];
    for (int i = 0; i < answers.length; i++) {
      items.add(Padding(
        padding: const EdgeInsets.only(top: 18),
        child: getListItem(answers[i]),
      ));
    }
    return Material(
      color: Colors.transparent,
      child: Container(
          height: MediaQuery.of(context).size.height - 325,
          constraints: const BoxConstraints(maxWidth: 400),
          child: Column(
            children: items,
          )
          //       ListView.builder(
          //   padding: const EdgeInsets.symmetric(horizontal: 8),
          //   // Let the ListView know how many items it needs to build.
          //   itemCount: answers.length,
          //   // Provide a builder function. This is where the magic happens.
          //   // Convert each item into a widget based on the type of item it is.
          //   itemBuilder: (BuildContext context, int index) {
          //     final item = answers[index];
          //     return Padding(
          //       padding: const EdgeInsets.only(top: 18),
          //       child: getListItem(item),
          //     );
          //   },
          // ),
          ),
    );
  }

  Widget getNoDataWidget() {
    return SizedBox(
      height: MediaQuery.of(context).size.height - 470,
      child: const Column(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              'NOT ENOUGH DATA',
              style: TextStyle(
                  letterSpacing: 0.25,
                  fontFamily: "Roboto",
                  fontWeight: FontWeight.w900,
                  color: CustomColors.americanPurple2,
                  fontSize: 13),
            ),
            SizedBox(height: 4),
            Text('Come back after playing\na few more matches!',
                textAlign: TextAlign.center,
                style: TextStyle(
                    letterSpacing: 0.25,
                    fontFamily: "Roboto",
                    height: 1.2,
                    fontWeight: FontWeight.w400,
                    color: CustomColors.americanPurple2,
                    fontSize: 13))
          ]),
    );
  }

  Widget getDataWidget(bool isLoading, FriendProfileEntity? friendProfileData) {
    Widget child = isLoading
        ? Skeletonizer(
            child: getList([
            FriendshipAnswerEntity(
                id: '',
                userId: '',
                roomId: '',
                questionId: '',
                value: 'Value, value, value, value, value',
                createdAt: DateTime.now(),
                updatedAt: DateTime.now(),
                room: FriendshipRoomEntity(
                    id: '',
                    name: '',
                    howManyPeople: 3,
                    isBloqued: false,
                    questionTimeInSeconds: 7,
                    responseTimeInSeconds: 8,
                    createdAt: DateTime.now(),
                    updatedAt: DateTime.now()),
                question: FriendshipQuestionEntity(
                    id: '',
                    description:
                        'Description, description, description, description, description',
                    isActived: 1,
                    stage: 1,
                    createdAt: DateTime.now(),
                    updatedAt: DateTime.now()),
                points: []),
            FriendshipAnswerEntity(
                id: '',
                userId: '',
                roomId: '',
                questionId: '',
                value: 'Value, value, value, value, value',
                createdAt: DateTime.now(),
                updatedAt: DateTime.now(),
                room: FriendshipRoomEntity(
                    id: '',
                    name: '',
                    howManyPeople: 3,
                    isBloqued: false,
                    questionTimeInSeconds: 7,
                    responseTimeInSeconds: 8,
                    createdAt: DateTime.now(),
                    updatedAt: DateTime.now()),
                question: FriendshipQuestionEntity(
                    id: '',
                    description:
                        'Description, description, description, description, description',
                    isActived: 1,
                    stage: 1,
                    createdAt: DateTime.now(),
                    updatedAt: DateTime.now()),
                points: []),
            FriendshipAnswerEntity(
                id: '',
                userId: '',
                roomId: '',
                questionId: '',
                value: 'Value, value, value, value, value',
                createdAt: DateTime.now(),
                updatedAt: DateTime.now(),
                room: FriendshipRoomEntity(
                    id: '',
                    name: '',
                    howManyPeople: 3,
                    isBloqued: false,
                    questionTimeInSeconds: 7,
                    responseTimeInSeconds: 8,
                    createdAt: DateTime.now(),
                    updatedAt: DateTime.now()),
                question: FriendshipQuestionEntity(
                    id: '',
                    description:
                        'Description, description, description, description, description',
                    isActived: 1,
                    stage: 1,
                    createdAt: DateTime.now(),
                    updatedAt: DateTime.now()),
                points: [])
          ]))
        : getList(friendProfileData!.answers);
    return widget.customScrollView != null
        ? widget.customScrollView!(child)
        : SingleChildScrollView(child: child);
  }

  Widget getWidget(bool isLoading, FriendProfileEntity? friendProfileData) {
    return friendProfileData == null || friendProfileData.answers.isEmpty
        ? getNoDataWidget()
        : getDataWidget(isLoading, friendProfileData); // : getNoDataWidget();
  }

  @override
  Widget build(BuildContext context) {
    return TripleBuilder(
        store: _store,
        builder: (context, tripleObj) {
          Triple<FriendshipStoreEntity> triple =
              tripleObj as Triple<FriendshipStoreEntity>;
          return getWidget(triple.isLoading, triple.state.friendProfile);
        });
  }
}
