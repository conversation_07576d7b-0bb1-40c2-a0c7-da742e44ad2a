import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_triple/flutter_triple.dart';
import 'package:quycky/app/features/user/domain/entities/user_login_return_entity.dart';
import 'package:quycky/app/features/user/presenter/controllers/user_controller.dart';
import 'package:quycky/app/features/user/presenter/store/user_store.dart';
import 'package:quycky/app/theme/colors.dart';
import 'package:quycky/app/widgets/app_header.dart';
import 'package:quycky/app/widgets/button.dart';
import 'package:quycky/app/widgets/custom_image.dart';
import 'package:quycky/app/widgets/gradient_container.dart';
import 'package:quycky/app/widgets/quycky_modal_progress.dart';
import 'package:quycky/core/usecase/errors/failure.dart';
import 'package:quycky/core/utils/app_routes.dart';
import 'package:quycky/core/utils/interval_utils.dart';
import 'package:quycky/core/utils/quycky_icons_icons.dart';
import 'package:quycky/core/utils/social_sign_in.dart';
import 'package:sizer/sizer.dart';
import 'package:url_launcher/url_launcher.dart';

enum ECurrentMenuState { initial, flagAbusiveContent, blockUser, endMenu }

class UserProfileSettingsMenu extends StatefulWidget {
  Future<bool> Function() handleSave;
  UserProfileSettingsMenu({super.key, required this.handleSave});

  @override
  State<UserProfileSettingsMenu> createState() => _UserProfileSettingsMenu();
}

class _UserProfileSettingsMenu extends State<UserProfileSettingsMenu> {
  bool isClicked = false;
  final _userState = Modular.get<UserStore>();
  final _controller = Modular.get<UserController>();
  final SocialSignIn _socialSignIn = SocialSignIn();
  bool isLoading = false;
  String title = 'ADD FRIEND';
  String subtitle = '';
  ECurrentMenuState currentMenuState = ECurrentMenuState.initial;

  @override
  void initState() {
    super.initState();
  }

  void handleClose() {
    widget.handleSave();

    Navigator.pop(context);
  }

  void onPressed() {
    handleClose();
    //  widget.onPressed!();
  }

  Widget getButton(Function() onPressed, String label,
      {Color textColor = Colors.white,
      Color color = CustomColors.button,
      Color borderColor = CustomColors.button,
      bool visible = true,
      bool outlined = false,
      Widget? child}) {
    return Visibility(
      visible: visible,
      child: SizedBox(
        width: double.infinity,
        child: Button(
            onPressed: onPressed,
            outlined: outlined,
            autoSized: true,
            text: label,
            textColor: textColor,
            color: color,
            borderColor: borderColor,
            child: child),
      ),
    );
  }

  Widget getButtonChild({String social = 'google'}) {
    return SizedBox(
      height: 28,
      child: SizedBox(
        width: 215,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Row(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  CustomImage(
                    'assets/img/png/${social.toLowerCase()}_logo.png',
                    width: 25,
                  ),
                  SizedBox(
                    width: social == 'google' ? 10 : 18,
                  ),
                  Text(
                    'SIGN IN WITH ${social.toUpperCase()}',
                    style: buttonDefaultTextStyle(Colors.black, fontSize: 10),
                  )
                ]),
          ],
        ),
      ),
    );
  }

  getData() async {
    await _userState.getLoginResultData();
    // final u = Modular.get<UserStorage>();
    final user = await _controller.getUserById(_userState.state.user.id);
    if (user != null) {
      _controller.userNameTextEditingController.text = user.name;
      _userState.setUser(user);
    }
    _controller.userNameTextEditingController.text = _userState.state.user.name;
    handleClose();
  }

  void handleGoogleSignIn() async {
    try {
      UserCredential? signInResult = await _socialSignIn.signInWithGoogle();
      final platformLogin =
          await _controller.doLoginWithGoogle(signInResult?.user?.uid ?? '');

      if (platformLogin == null) {
        final res = await _controller.updateUser(
            signInResult?.user?.displayName ??
                _controller.userNameTextEditingController.text,
            googleId: signInResult?.user?.uid);
      }
      setTimeout(callback: getData, duration: const Duration(seconds: 2));
    } catch (e) {
      print(
          '==>${e.toString() == '[firebase_auth/web-context-canceled] The web operation was canceled by the user.'}');
    }
  }

  void handleAppleSingIn() async {
    try {
      UserCredential signInResult = await _socialSignIn.signInWithApple();
      final platformLogin =
          await _controller.doLoginWithApple(signInResult.user?.uid ?? '');
      if (platformLogin == null) {
        final res = await _controller.updateUser(
            signInResult.user?.displayName ??
                _controller.userNameTextEditingController.text,
            appleId: signInResult.user?.uid);
      }
      setTimeout(callback: getData, duration: const Duration(seconds: 1));
    } catch (e) {
      print(
          '==>${e.toString() == '[firebase_auth/web-context-canceled] The web operation was canceled by the user.'}');
    }
  }

  void _openUrl(String url) {
    final uri = Uri.parse(url);
    launchUrl(uri, mode: LaunchMode.externalApplication);
  }

  void _handleOpenHowToPage() {
    _openUrl('https://www.quycky.app/how-to');
  }

  void _handleOpenBlogPage() {
    _openUrl('https://quycky.shop/articles');
  }

  void _handleOpenRulesPage() {
    _openUrl('https://www.quycky.app/prize-rules');
  }

  void _handleOpenAboutPage() {
    _openUrl('https://www.quycky.app/');
  }

  List<Widget> getButtons(bool alreadySocialLogin) {
    List<Widget> children = [];
    if (!alreadySocialLogin) {
      children.add(Padding(
        padding: EdgeInsets.only(bottom: 2.37.h),
        child: Text(
          'SIGN IN TO SAVE YOUR PROGRESS',
          textAlign: TextAlign.center,
          style: TextStyle(
              letterSpacing: 3,
              fontFamily: "Roboto",
              fontWeight: FontWeight.bold,
              color: Colors.white,
              fontSize: 10),
        ),
      ));
      children.add(getButton(handleGoogleSignIn, 'GOOGLE',
          color: Colors.white,
          textColor: CustomColors.button,
          child: getButtonChild(),
          borderColor: Colors.transparent,
          visible: !alreadySocialLogin));
      children.add(SizedBox(height: 1.9.h));
      children.add(getButton(handleAppleSingIn, 'APPLE',
          color: Colors.white,
          textColor: CustomColors.button,
          child: getButtonChild(social: 'apple'),
          visible: !alreadySocialLogin,
          borderColor: Colors.transparent));
      children.add(SizedBox(height: 1.9.h));
      children.add(getButton(_handleOpenHowToPage, 'HOW TO',
          outlined: true, textColor: Colors.white, borderColor: Colors.white));
      // children.add(SizedBox(height: 1.9.h));
      // children.add(getButton(_handleOpenRulesPage, 'PRIZE RULES',
      //     outlined: true, textColor: Colors.white, borderColor: Colors.white));
      children.add(SizedBox(height: 1.9.h));

      children.add(getButton(
        _handleOpenAboutPage,
        'ABOUT QUYCKY',
        outlined: true,
        textColor: Colors.white,
        borderColor: Colors.white,
      ));

      children.add(SizedBox(height: 1.9.h));

      children.add(getButton(
        _handleOpenBlogPage,
        'BLOG',
        outlined: true,
        textColor: Colors.white,
        borderColor: Colors.white,
      ));
    } else {
      children.add(getButton(_handleOpenHowToPage, 'HOW TO',
          color: Colors.white, textColor: CustomColors.button));
      children.add(SizedBox(height: 1.9.h));
      // children.add(getButton(_handleOpenRulesPage, 'PRIZE RULES',
      //     color: Colors.white, textColor: CustomColors.button));
      // children.add(SizedBox(height: 1.9.h));
      children.add(getButton(() {}, 'ABOUT QUYCKY',
          color: Colors.white, textColor: CustomColors.button));

      children.add(SizedBox(height: 1.9.h));

      children.add(getButton(_handleOpenBlogPage, 'BLOG',
          color: Colors.white, textColor: CustomColors.button));
    }

    children.add(Padding(
      padding: EdgeInsets.only(top: 3.55.h, bottom: 1.77.h),
      child: Text(
        'ACCOUNT',
        textAlign: TextAlign.center,
        style: TextStyle(
            letterSpacing: 3.6,
            fontFamily: "Roboto",
            fontWeight: FontWeight.bold,
            color: Colors.white,
            fontSize: 12),
      ),
    ));

    children.add(getButton(() => _controller.doLogout(), 'LOGOUT',
        borderColor: Colors.transparent,
        color: Colors.white,
        textColor: CustomColors.button));

    children.add(SizedBox(height: 1.9.h));

    children.add(getButton(
      _controller.deleteUserAccount,
      'DELETE ACCOUNT',
      outlined: true,
      textColor: Colors.white,
      borderColor: Colors.white,
    ));

    children.add(SizedBox(height: 1.9.h));

    // children.add(getButton(
    //   () => null,
    //   'SUSPEND',
    //   outlined: true,
    //   textColor: Colors.white,
    //   borderColor: Colors.white,
    // ));

    children.add(SizedBox(height: 1.9.h));
    return children;
  }

  void handleBack() {
    Navigator.pop(context);
  }

  List<Widget> getWidgets(bool alreadySocialLogin) {
    return getButtons(alreadySocialLogin);
  }

  handleOpenTutorial() =>
      Modular.to.pushReplacementNamed(AppRoutes.tutorial, arguments: true);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: GradientContainer(
          useDefault: true,
          coldOpacity: 0,
          normalOpacity: 0,
          hotOpacity: 0,
          child: TripleBuilder(
            store: _userState,
            builder: (context, triple) {
              var state = triple as Triple<UserLoginResultEntity>;
              final alreadySocialLogin = state.state.user.appleId!.isNotEmpty ||
                  state.state.user.googleId!.isNotEmpty;

              return QuyckyModalProgress(
                  state: state.isLoading,
                  child: SafeArea(
                    child: Column(
                      children: [
                        AppHeader(
                          logoSectionLeftWidget: IconButton(
                              onPressed: handleBack,
                              icon: const Icon(
                                QuyckyIcons.arrow_left_circle,
                                color: Colors.white,
                                size: 23,
                              )),
                          title: 'SETTINGS',
                        ),
                        Container(
                          padding: EdgeInsets.only(
                              top: alreadySocialLogin ? 59 : 25,
                              left: 30,
                              right: 30),
                          constraints: const BoxConstraints(maxWidth: 500),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [...getWidgets(alreadySocialLogin)],
                          ),
                        ),
                      ],
                    ),
                  ));
            },
          )),
    );
  }
}
