import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:quycky/app/features/user/domain/entities/user_iq_entity.dart';
import 'package:quycky/app/presenter/app_widget.dart';
import 'package:quycky/app/theme/colors.dart';
import 'package:quycky/app/widgets/button.dart';
import 'package:quycky/app/widgets/custom_image.dart';
import 'package:quycky/core/utils/app_routes.dart';
import 'package:quycky/core/utils/assets_strings.dart';
import 'package:quycky/core/utils/quycky_icons_icons.dart';

class ShowDimensionDialog {
  ShowDimensionDialog({required UserIqInfoEntity data}) {
    BuildContext context = AppWidget.globalKey.currentState!.context;
    showGeneralDialog(
      context: context,
      barrierDismissible: true,
      transitionDuration: const Duration(milliseconds: 300),
      barrierLabel: MaterialLocalizations.of(context).dialogLabel,
      barrierColor: Colors.black.withOpacity(0.5),
      pageBuilder: (context, _, __) => getDialogWidget(data: data),
      transitionBuilder: getTransitionBuilder,
    );
  }

  void _handleClose() {
    Navigator.pop(AppWidget.globalKey.currentState!.context);
  }

  void handleTapPlayToUnlock() {
    Modular.to.pushNamed(AppRoutes.gameLobby());
  }

  Widget getDialogWidget({required UserIqInfoEntity data}) {
    Color closeIconColor = Colors.white;
    Color bgColor = data.tag.color;
    Color textColor = Colors.white;
    String description = data.tag.description;
    String icon = Assets.svg.iq.getByName(data.tag.cod);
    Widget playToUnlockButton = const SizedBox();

    if (!data.isUnlocked) {
      bgColor = Colors.white;
      closeIconColor = CustomColors.orangeSoda;
      textColor = CustomColors.americanPurple2;
      icon = Assets.svg.iq["${data.tag.cod}_disabled"];
      description = "Play a few more matches\nto unlock this dimension.";
      playToUnlockButton = Container(
        margin: const EdgeInsets.only(top: 15),
        height: 50,
        width: 220,
        child: Button(
          autoSized: true,
          text: 'PLAY TO UNLOCK',
          onPressed: handleTapPlayToUnlock,
        ),
      );
    }
    return Column(mainAxisAlignment: MainAxisAlignment.center, children: [
      Container(
        width: 325,
        height: 343,
        decoration: BoxDecoration(
            color: bgColor,
            borderRadius: const BorderRadius.all(Radius.circular(30))),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Padding(
                  padding: const EdgeInsets.only(top: 2, right: 2),
                  child: SizedBox(
                    height: 33,
                    child: IconButton(
                        iconSize: 26,
                        onPressed: _handleClose,
                        icon: Icon(
                          QuyckyIcons.close_circle,
                          color: closeIconColor,
                          size: 26,
                        )),
                  ),
                )
              ],
            ),
            getImage(icon, withShadow: !data.isUnlocked),
            // CustomImage(
            //   icon,
            //   height: 128,
            //   width: 128,
            // ),
            Text(
              data.tag.title.toUpperCase(),
              style: TextStyle(
                  decoration: TextDecoration.none,
                  letterSpacing: 1,
                  fontFamily: "Roboto",
                  fontWeight: FontWeight.w700,
                  color: textColor,
                  fontSize: 16),
            ),
            Text(
              '(${data.tag.cod.toUpperCase()})',
              style: TextStyle(
                  decoration: TextDecoration.none,
                  letterSpacing: 1,
                  fontFamily: "Roboto",
                  fontWeight: FontWeight.w400,
                  color: textColor,
                  fontSize: 16),
            ),
            Padding(
              padding: const EdgeInsets.only(left: 25, right: 25, top: 24),
              child: Text(
                description,
                textAlign: TextAlign.center,
                style: TextStyle(
                    decoration: TextDecoration.none,
                    letterSpacing: 1.4,
                    fontFamily: "Roboto",
                    fontWeight: FontWeight.w400,
                    color: textColor,
                    fontSize: 13),
              ),
            ),
            playToUnlockButton
          ],
        ),
      ),
    ]);
  }

  Widget getImage(String imagePath, {withShadow = true}) {
    final Widget image =
        CustomImage(imagePath, width: 128, height: 128, fit: BoxFit.fitHeight);
    if (withShadow) {
      return Stack(
        children: [
          ImageFiltered(
            imageFilter: ImageFilter.blur(sigmaX: 0.5, sigmaY: 0.5),
            child: ColorFiltered(
              colorFilter:
                  ColorFilter.mode(Colors.black.withAlpha(38), BlendMode.srcIn),
              child: CustomImage(imagePath, width: 130, height: 130),
            ),
          ),
          image,
        ],
      );
    }
    return image;
  }

  Widget getTransitionBuilder(BuildContext context, Animation<double> animation,
      Animation<double> secondaryAnimation, Widget child) {
    Offset begin = const Offset(0, -1);
    begin = (const Offset(0, 1));

    return SlideTransition(
      position: CurvedAnimation(
        parent: animation,
        curve: Curves.easeOut,
      ).drive(Tween<Offset>(
        begin: begin,
        end: Offset.zero,
      )),
      child: child,
    );
  }
}
