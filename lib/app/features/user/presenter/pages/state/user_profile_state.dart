import 'dart:io';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_triple/flutter_triple.dart';
import 'package:image_picker/image_picker.dart';
import 'package:quycky/app/features/user/domain/entities/user_login_return_entity.dart';
import 'package:quycky/app/features/user/domain/entities/user_profile_store_entity.dart';
import 'package:quycky/app/features/user/presenter/controllers/user_controller.dart';
import 'package:quycky/app/features/user/presenter/pages/user_profile_page.dart';
import 'package:quycky/app/features/user/presenter/store/user_profile_store.dart';
import 'package:quycky/app/features/user/presenter/store/user_store.dart';
import 'package:quycky/app/widgets/app_header.dart';
import 'package:quycky/app/widgets/custom_image.dart';
import 'package:quycky/app/widgets/gradient_container.dart';
import 'package:quycky/core/utils/assets_strings.dart';
import 'package:quycky/core/utils/interval_utils.dart';
import 'package:quycky/core/utils/quycky_icons_icons.dart';
import 'package:quycky/core/utils/show_dialog_get_image.dart';
import 'package:quycky/core/utils/show_message.dart';
import 'package:quycky/core/utils/social_sign_in.dart';
import 'package:quycky/core/utils/text_formatter/upper_case_text_formatter.dart';
import 'package:quycky/app/widgets/avatar.dart';
import 'package:quycky/app/widgets/button.dart';
import 'package:quycky/app/theme/colors.dart';
import 'package:quycky/app/widgets/quycky_modal_progress.dart';
import 'package:quycky/core/usecase/errors/failure.dart';

class UserProfileState extends State<UserProfilePage> {
  final _userProfileStore = Modular.get<UserProfileStore>();
  String userName = "";
  final _controller = Modular.get<UserController>();
  final SocialSignIn _socialSignIn = SocialSignIn();
  final _userState = Modular.get<UserStore>();

  @override
  void initState() {
    super.initState();
    getData();
  }

  getData() async {
    await _userState.getLoginResultData();
    final user = await _controller.getUserById(_userState.state.user.id);
    if (user != null) {
      _controller.userNameTextEditingController.text = user.name;
      _userState.setUser(user);
    }
    _controller.userNameTextEditingController.text = _userState.state.user.name;
  }

  void handleSetImage(XFile? file) {
    file!
        .readAsBytes()
        .then((value) => _userState.uploadAvatar(File(file.path)));
    // _userState.uploadAvatar(File(file!.path));
  }

  void getImageAndUpload() async {
    ShowDialogGetImage(onOk: handleSetImage);
  }

  // void getImageAndUpload2() async {
  //   XFile? pickedImage = await _pickImage.getImageFrom();
  //   _userState.uploadAvatar(File(pickedImage!.path));
  // }
  Widget getButton(Function() onPressed, String label,
      {Color textColor = Colors.white,
      Color color = CustomColors.button,
      Color borderColor = CustomColors.button,
      bool visible = true,
      bool outlined = false,
      Widget? child}) {
    return Visibility(
      visible: visible,
      child: SizedBox(
        width: double.infinity,
        child: Button(
            onPressed: onPressed,
            outlined: outlined,
            autoSized: true,
            text: label,
            textColor: textColor,
            color: color,
            borderColor: borderColor,
            child: child),
      ),
    );
  }

  void handleGoogleSignIn() async {
    try {
      UserCredential? signInResult = await _socialSignIn.signInWithGoogle();
      final platformLogin =
          await _controller.doLoginWithGoogle(signInResult?.user?.uid ?? '');
      if (platformLogin == null) {
        final res = await _controller.updateUser(
            signInResult?.user?.displayName ??
                _controller.userNameTextEditingController.text,
            googleId: signInResult?.user?.uid);
        final photoURL = signInResult?.user?.photoURL ?? '';
        if (photoURL.isNotEmpty) {
          _controller.updateUserImageFromURL(photoURL);
        }
      }
      setTimeout(callback: getData, duration: const Duration(seconds: 2));
    } catch (e) {
      print(
          '==>${e.toString() == '[firebase_auth/web-context-canceled] The web operation was canceled by the user.'}');
    }
  }

  void handleAppleSingIn() async {
    try {
      UserCredential signInResult = await _socialSignIn.signInWithApple();
      final platformLogin =
          await _controller.doLoginWithApple(signInResult.user?.uid ?? '');
      if (platformLogin == null) {
        final res = await _controller.updateUser(
            signInResult.user?.displayName ??
                _controller.userNameTextEditingController.text,
            appleId: signInResult.user?.uid);
      }
      setTimeout(callback: getData, duration: const Duration(seconds: 1));
    } catch (e) {
      print(
          '==>${e.toString() == '[firebase_auth/web-context-canceled] The web operation was canceled by the user.'}');
    }
  }

  void handleAccept() {
    showDialog(
        context: context,
        builder: (_) => CupertinoAlertDialog(
              title: const Text('Alert'),
              content: const Text("Please read until the end to accept"),
              actions: [
                CupertinoDialogAction(
                  onPressed: () => Navigator.of(context).pop(),
                  isDefaultAction: true,
                  child: const Text("Ok"),
                )
              ],
            ));
  }

  Future<bool> handleSave() async {
    try {
      if (_controller.userNameTextEditingController.text.isEmpty) {
        return false;
      }
      if (_controller.userNameTextEditingController.text !=
          _userState.state.user.name) {
        await _controller
            .updateUser(_controller.userNameTextEditingController.text);
      }
      return true;
    } catch (e) {
      print('Err:UPST=>$e');
      ShowMessage(message: defaultFailureMessage, type: MessageType.error);
    }

    return false;
  }

  Widget getButtonChild({String social = 'google'}) {
    return SizedBox(
      height: 28,
      child: SizedBox(
        width: 215,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Row(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  CustomImage(
                    'assets/img/png/${social.toLowerCase()}_logo.png',
                    width: 25,
                  ),
                  SizedBox(
                    width: social == 'google' ? 10 : 18,
                  ),
                  Text(
                    'SIGN IN WITH ${social.toUpperCase()}',
                    style: buttonDefaultTextStyle(Colors.black, fontSize: 10),
                  )
                ]),
          ],
        ),
      ),
    );
  }

  Future<bool> handleBack() async {
    bool res = await handleSave();
    Modular.to.pop();
    return res;
    //Modular.to.pop();
  }

  Widget getButtons(bool alreadySocialLogin) {
    List<Widget> children = [];
    if (!alreadySocialLogin) {
      children.add(const SizedBox(height: 21));
      children.add(getButton(handleGoogleSignIn, 'GOOGLE',
          color: Colors.white,
          borderColor: Colors.white,
          textColor: CustomColors.button,
          child: getButtonChild(),
          visible: !alreadySocialLogin));
      children.add(const SizedBox(height: 18));
      children.add(getButton(handleAppleSingIn, 'APPLE',
          color: Colors.white,
          borderColor: Colors.white,
          textColor: CustomColors.button,
          child: getButtonChild(social: 'apple'),
          visible: !alreadySocialLogin));
    }

    children.add(const Padding(
      padding: EdgeInsets.only(top: 36, bottom: 29),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            'ACCOUNT SETTINGS',
            style: TextStyle(
                letterSpacing: 4,
                fontFamily: "Roboto",
                fontWeight: FontWeight.bold,
                color: Colors.white,
                fontSize: 12),
          ),
        ],
      ),
    ));

    children.add(getButton(() => _controller.doLogout(), 'LOGOUT',
        borderColor: Colors.transparent,
        color: Colors.white,
        textColor: CustomColors.button));

    children.add(const SizedBox(height: 18));

    children.add(getButton(() => null, 'SUSPEND',
        borderColor: Colors.white, outlined: true));

    children.add(const SizedBox(height: 18));

    children.add(getButton(
      _controller.deleteUserAccount,
      'DELETE ACCOUNT',
      borderColor: Colors.white,
      outlined: true,
    ));

    children.add(const SizedBox(height: 18));
    return SizedBox(
      height: MediaQuery.of(context).size.height - 380,
      child: ListView(
        children: children,
      ),
    );
  }

  // Widget getIQWidget() {
  //   return TripleBuilder(
  //       store: _userIQStore,
  //       builder: (context, triple) {
  //         var store = triple as Triple<UserIQStoreEntity>;

  //         final xmaxxmindiff = store.state.xMax - store.state.xMin;
  //         return Padding(
  //             padding: const EdgeInsets.only(top: 30.0),
  //             child: Column(children: [
  //               const Row(
  //                 mainAxisAlignment: MainAxisAlignment.center,
  //                 children: [
  //                   Text(
  //                     'YOUR iQ',
  //                     style: TextStyle(
  //                         fontSize: 18,
  //                         color: Colors.white,
  //                         fontWeight: FontWeight.bold),
  //                   ),
  //                 ],
  //               ),
  //               const Row(
  //                   mainAxisAlignment: MainAxisAlignment.center, children: []),
  //               Container(
  //                 padding: const EdgeInsets.only(top: 10),
  //                 height: MediaQuery.of(context).size.height - 380,
  //                 child: ListView.builder(
  //                     itemCount: store.state.iq.length,
  //                     itemBuilder: (context, index) {
  //                       final item = store.state.iq[index];
  //                       final value = (item.count - store.state.xMin) /
  //                           xmaxxmindiff; // xcurrent-xmin/xmax-xmin
  //                       final String name = item.tag.title;
  //                       final color = value < 0.41
  //                           ? CustomColors.orangeSoda70
  //                           : (value < 0.80
  //                               ? CustomColors.orangeSoda90
  //                               : CustomColors.orangeSoda);
  //                       return ListTile(
  //                         title: Text(
  //                           name,
  //                           style: const TextStyle(
  //                               color: Colors.white, fontSize: 18),
  //                         ),
  //                         subtitle: Padding(
  //                           padding: const EdgeInsets.only(top: 10.0),
  //                           child: LinearProgressIndicator(
  //                               value: value,
  //                               color: color,
  //                               backgroundColor: CustomColors.backdrop,
  //                               minHeight: 30),
  //                         ),
  //                       );
  //                     }),
  //               ),
  //             ]));
  //       });
  // }

  Widget getWidgets(bool alreadySocialLogin) {
    Widget ret = TripleBuilder(
        store: _userProfileStore,
        builder: (context, triple) {
          var store = triple as Triple<UserProfileStoreEntity>;
          return getButtons(alreadySocialLogin);
          // return store.state.showSettings
          //     ? getIQWidget()
          //     : getButtons(alreadySocialLogin);
        }); // getButtons(alreadySocialLogin);
    return ret;
  }

  // handleToogleshowSettingsState() {
  //   if (!_userProfileStore.state.showSettings &&
  //       _userIQStore.state.iq.isEmpty) {
  //     _userProfileController.getIQ();
  //   }
  //   _userProfileController.toogleshowSettingsState();
  // }

  // Widget getHeaderRightButton() {
  //   return TripleBuilder(
  //       store: _userProfileStore,
  //       builder: (context, triple) {
  //         var store = triple as Triple<UserProfileStoreEntity>;
  //         return IconButton(
  //             onPressed: handleToogleshowSettingsState,
  //             icon: Icon(
  //               store.state.showSettings ? QuyckyIcons.gear : QuyckyIcons.iq,
  //               color: Colors.white,
  //               size: 22,
  //             ));
  //       });
  // }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: handleBack,
      child: Scaffold(
        body: GradientContainer(
            useDefault: true,
            coldOpacity: 0,
            normalOpacity: 0,
            hotOpacity: 0,
            child: TripleBuilder(
              store: _userState,
              builder: (context, triple) {
                var state = triple as Triple<UserLoginResultEntity>;
                final alreadySocialLogin =
                    state.state.user.appleId!.isNotEmpty ||
                        state.state.user.googleId!.isNotEmpty;

                return QuyckyModalProgress(
                    state: state.isLoading,
                    child: SafeArea(
                      child: Column(
                        children: [
                          AppHeader(
                            logoSectionLeftWidget: IconButton(
                                onPressed: handleBack,
                                icon: const Icon(
                                  QuyckyIcons.arrow_left_circle,
                                  color: Colors.white,
                                  size: 23,
                                )),
                            title: 'PROFILE',
                            logoPath: 'assets/img/svg/quycky_logo_white.svg',
                          ),
                          // logoSectionRightWidget: getHeaderRightButton()),
                          Container(
                            padding: const EdgeInsets.only(
                                top: 25, left: 30, right: 30),
                            constraints: const BoxConstraints(maxWidth: 500),
                            child: Column(
                              children: [
                                Stack(
                                  alignment: AlignmentDirectional.bottomCenter,
                                  children: [
                                    const SizedBox(height: 140),
                                    Column(
                                      children: [
                                        Container(
                                          height: 50,
                                          decoration: const BoxDecoration(
                                              color: CustomColors.orangeSoda90,
                                              borderRadius: BorderRadius.only(
                                                  topLeft: Radius.circular(30),
                                                  topRight:
                                                      Radius.circular(30))),
                                          child: TextFormField(
                                            controller: _controller
                                                .userNameTextEditingController,
                                            inputFormatters: [
                                              UpperCaseTextFormatter()
                                            ],
                                            onChanged: (value) {
                                              userName = value;
                                            },
                                            onSaved: (value) {
                                              userName = value ?? "";
                                            },
                                            validator: (value) {
                                              if (value != null &&
                                                  value.length < 3) {
                                                return 'a minimum of 3 characters is required';
                                              }
                                              return null;
                                            },
                                            decoration: const InputDecoration(
                                              contentPadding:
                                                  EdgeInsets.all(18.0),
                                              border: InputBorder.none,
                                              hintText: 'CHOOSE A NAME!',
                                              hintStyle: TextStyle(
                                                  letterSpacing: 3,
                                                  fontFamily: "Roboto",
                                                  color: Colors.white,
                                                  fontSize: 10),
                                            ),
                                            style: const TextStyle(
                                                letterSpacing: 3,
                                                fontFamily: "Roboto",
                                                color: Colors.white,
                                                fontSize: 10),
                                          ),
                                        ),
                                        Container(
                                          height: 50,
                                          decoration: const BoxDecoration(
                                              color: CustomColors.orangeSoda40,
                                              borderRadius: BorderRadius.only(
                                                  bottomLeft:
                                                      Radius.circular(30),
                                                  bottomRight:
                                                      Radius.circular(30))),
                                          child: Padding(
                                            padding: const EdgeInsets.symmetric(
                                                horizontal: 18.0),
                                            child: Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment
                                                      .spaceBetween,
                                              children: [
                                                const Text(
                                                  'RANKING',
                                                  style: TextStyle(
                                                      letterSpacing: 3,
                                                      fontFamily: "Roboto",
                                                      fontWeight:
                                                          FontWeight.bold,
                                                      color: Colors.white,
                                                      fontSize: 10),
                                                ),
                                                Row(
                                                  children: [
                                                    Padding(
                                                      padding:
                                                          const EdgeInsets.only(
                                                              top: 6,
                                                              right: 12.0),
                                                      child: Text(
                                                        state.state.user
                                                            .pontuation
                                                            .toString(),
                                                        style: const TextStyle(
                                                            letterSpacing: 3,
                                                            fontFamily:
                                                                "Roboto",
                                                            fontWeight:
                                                                FontWeight.bold,
                                                            color: Colors.white,
                                                            fontSize: 10),
                                                      ),
                                                    ),
                                                    Image.asset(
                                                      Assets.png.fire,
                                                      width: 20,
                                                    ),
                                                  ],
                                                )
                                              ],
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                    Positioned(
                                      top: -50,
                                      child: Padding(
                                        padding:
                                            const EdgeInsets.only(top: 50.0),
                                        child: Avatar(
                                          size: 50,
                                          outlined: true,
                                          addPhotoButton: true,
                                          imagePath: state.state.user.avatarUrl,
                                          onPressed: getImageAndUpload,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                                getWidgets(alreadySocialLogin)
                              ],
                            ),
                          ),
                        ],
                      ),
                    ));
              },
            )),
      ),
    );
  }
}
