import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_triple/flutter_triple.dart';
import 'package:image_picker/image_picker.dart';
import 'package:quycky/app/features/friendship/presenter/pages/widgets/friend_match_history.dart';
import 'package:quycky/app/features/user/domain/entities/user_entity.dart';
import 'package:quycky/app/features/user/domain/entities/user_iq_entity.dart';
import 'package:quycky/app/features/user/domain/entities/user_iq_store_entity.dart';
import 'package:quycky/app/features/user/domain/entities/user_login_return_entity.dart';
import 'package:quycky/app/features/user/domain/entities/user_profile_store_entity.dart';
import 'package:quycky/app/features/user/presenter/controllers/user_controller.dart';
import 'package:quycky/app/features/user/presenter/controllers/user_profile_controller.dart';
import 'package:quycky/app/features/user/presenter/pages/dialog/show_dimension_dialog.dart';
import 'package:quycky/app/features/user/presenter/pages/dialog/show_user_profile_settings_dialog.dart';
import 'package:quycky/app/features/user/presenter/pages/widgets/dimensions_card.dart';
import 'package:quycky/app/features/user/presenter/pages/widgets/profile_three_circles.dart';
import 'package:quycky/app/features/user/presenter/pages/user_profile_page.dart';
import 'package:quycky/app/features/user/presenter/pages/widgets/user_personal_tag_progress_item.dart';
import 'package:quycky/app/features/user/presenter/pages/widgets/user_profile_history.dart';
import 'package:quycky/app/features/user/presenter/pages/widgets/user_profile_tab.dart';
import 'package:quycky/app/features/user/presenter/pages/widgets/user_progressbar.dart';
import 'package:quycky/app/features/user/presenter/pages/widgets/user_radius_chart.dart';
import 'package:quycky/app/features/user/presenter/pages/widgets/user_stage_tag_detail_card.dart';
import 'package:quycky/app/features/user/presenter/store/user_iq_show_more_store.dart';
import 'package:quycky/app/features/user/presenter/store/user_iq_store.dart';
import 'package:quycky/app/features/user/presenter/store/user_profile_store.dart';
import 'package:quycky/app/features/user/presenter/store/user_store.dart';
import 'package:quycky/app/services/remote_config/remote_config_store.dart';
import 'package:quycky/app/widgets/app_header.dart';
import 'package:quycky/app/widgets/custom_image.dart';
import 'package:quycky/app/widgets/gradient_container.dart';
import 'package:quycky/core/utils/app_routes.dart';
import 'package:quycky/core/utils/assets_strings.dart';
import 'package:quycky/core/utils/interval_utils.dart';
import 'package:quycky/core/utils/quycky_icons_icons.dart';
import 'package:quycky/core/utils/show_dialog_get_image.dart';
import 'package:quycky/app/widgets/avatar.dart';
import 'package:quycky/app/widgets/button.dart';
import 'package:quycky/app/theme/colors.dart';
import 'package:quycky/app/widgets/quycky_modal_progress.dart';
import 'package:quycky/core/usecase/errors/failure.dart';
import 'package:quycky/core/utils/show_message.dart';
import 'package:quycky/core/utils/text_formatter/upper_case_text_formatter.dart';
import 'package:sizer/sizer.dart';

class UserProfileIQState extends State<UserProfilePage> {
  final _userProfileController = Modular.get<UserProfileController>();
  final FocusNode _userNameFieldFocusNode = FocusNode();
  final _userProfileStore = Modular.get<UserProfileStore>();
  final _remoteConfigStore = Modular.get<RemoteConfigStore>();
  final _userIQStore = Modular.get<UserIQStore>();
  final _userPersonalityShowMoreStore =
      Modular.get<UserIQPersonalityShowMoreStore>();
  String userName = "";
  final _controller = Modular.get<UserController>();
  final _userState = Modular.get<UserStore>();
  final _customScrollController = ScrollController();
  // final _userState = Modular.get<UserStore>();
  // final PickImage _pickImage = PickImage();
  late final List<Map<String, dynamic>> tagsChartData;

  void userNameFieldFocusListener() {
    if (!_userNameFieldFocusNode.hasFocus) {
      handleEditUserName(state: false);
      handleSave();
    }
  }

  @override
  void initState() {
    _userNameFieldFocusNode.addListener(userNameFieldFocusListener);

    getData();
    _userProfileController.getIQ().then((value) => setTimeout(
        duration: const Duration(milliseconds: 300),
        callback: () => handleTogglePersonalityShowMoreState(value: false)));

    _customScrollController.addListener(() {
      if (!_userProfileStore.state.pinnedTab &&
          _customScrollController.position.extentAfter <= 114) {
        _userProfileStore.setPinnedTab(true);
        return;
      }
      if (_userProfileStore.state.pinnedTab &&
          _customScrollController.position.extentAfter >= 20) {
        _userProfileStore.setPinnedTab(false);
      }
    });
    super.initState();
  }

  getData() async {
    await _userState.getLoginResultData();
    UserEntity userData = _userState.state.user;

    final user = await _controller.getUserById(userData.id);

    _userState.setUser(user!);
    userData = user;

    _controller.userNameTextEditingController.text = userData.name;
  }

  void handleSetImage(XFile? file) {
    file!
        .readAsBytes()
        .then((value) => _userState.uploadAvatar(File(file.path)));
  }

  void getImageAndUpload() async {
    ShowDialogGetImage(onOk: handleSetImage);
  }

  // void getImageAndUpload2() async {
  //   XFile? pickedImage = await _pickImage.getImageFrom();
  //   _userState.uploadAvatar(File(pickedImage!.path));
  // }

  Widget getButton(Function() onPressed, String label,
      {Color textColor = Colors.white,
      Color color = CustomColors.button,
      bool visible = true,
      Widget? child}) {
    return Visibility(
      visible: visible,
      child: SizedBox(
        width: double.infinity,
        child: Button(
            onPressed: onPressed,
            autoSized: true,
            text: label,
            textColor: textColor,
            color: color,
            borderColor: color,
            child: child),
      ),
    );
  }

  void handleAccept() {
    showDialog(
        context: context,
        builder: (_) => CupertinoAlertDialog(
              title: const Text('Alert'),
              content: const Text("Please read until the end to accept"),
              actions: [
                CupertinoDialogAction(
                  onPressed: () => Navigator.of(context).pop(),
                  isDefaultAction: true,
                  child: const Text("Ok"),
                )
              ],
            ));
  }

  Future<bool> handleSave() async {
    try {
      if (_controller.userNameTextEditingController.text.isEmpty) {
        return false;
      }
      if (_controller.userNameTextEditingController.text !=
          _userState.state.user.name) {
        await _controller
            .updateUser(_controller.userNameTextEditingController.text);
      }
      return true;
    } catch (e) {
      print('Err:UPIQS=>$e');
      ShowMessage(message: defaultFailureMessage, type: MessageType.error);
    }

    return false;
  }

  void goToLobbyPage() {
    Modular.to.pushNamed(AppRoutes.gameLobby());
  }

  Future<bool> handleBack() async {
    bool res = await handleSave();
    Modular.to.pop();
    return res;
  }

  Widget getProfileAccuracyWidget(double progressValue) {
    return ProfileCard(
      height: 200,
      width: 325,
      withShadow: false,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          const Text(
            'PROFILE ACCURACY',
            style: TextStyle(
                letterSpacing: 1.8,
                fontFamily: "Roboto",
                fontWeight: FontWeight.bold,
                color: CustomColors.americanPurple2,
                fontSize: 16),
          ),
          UserProgressBar(
            progressValue: progressValue,
          ),
          SizedBox(
            width: 252,
            child: RichText(
              text: const TextSpan(
                children: [
                  TextSpan(
                    text: "PLAY MORE",
                    style: TextStyle(
                        letterSpacing: 0.33,
                        fontFamily: "Roboto",
                        fontWeight: FontWeight.w900,
                        color: CustomColors.americanPurple2,
                        fontSize: 13),
                  ),
                  TextSpan(
                    text: " ",
                  ),
                  TextSpan(
                    text:
                        "to increase your profile \naccuracy and discover more dimensions.",
                    style: TextStyle(
                        letterSpacing: 0.33,
                        fontFamily: "Roboto",
                        color: CustomColors.americanPurple2,
                        fontSize: 13),
                  ),
                ],
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  Widget getPersonalityTagsProgress(
      List<UserIqInfoEntity> data, bool allItems) {
    List<Widget> res = [];
    data.sort(
      (a, b) => b.stat.compareTo(a.stat),
    );
    final length = data.length;
    if (length > 0) {
      final qtdItemsToAdd =
          (allItems ? data.length : (length > 3 ? 3 : length)) - 1;
      for (int i = 0; i < qtdItemsToAdd; i++) {
        res.add(Padding(
            padding: const EdgeInsets.only(bottom: 5.0),
            child: UserPersonalTagProgressItem(
              data: data[i],
            )));
      }
      if (allItems) {
        final widget = UserPersonalTagProgressItem(
          data: data[qtdItemsToAdd],
        );
        res.add(widget);
        res.add(
          Container(
            padding: const EdgeInsets.only(
                // left: 12,
                top: 25),
            child: SizedBox(
              width: 252,
              child: RichText(
                text: const TextSpan(
                  children: [
                    TextSpan(
                      text: "PLAY MATCHES TO\nDISCOVER MORE DIMENSIONS",
                      style: TextStyle(
                          letterSpacing: 0.6,
                          fontFamily: "Roboto",
                          fontWeight: FontWeight.w500,
                          color: CustomColors.americanPurple2,
                          fontSize: 12),
                    ),
                  ],
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
        );
      } else {
        // final widget = ShaderMask(
        //     shaderCallback: (Rect bounds) {
        //       return LinearGradient(
        //         begin: Alignment.topCenter,
        //         end: Alignment.bottomCenter,
        //         stops: const [0.0, 0.5, 1.0],
        //         colors: [
        //           Colors.white,
        //           Colors.white.withOpacity(0.5),
        //           Colors.white.withOpacity(0.15),
        //         ],
        //       ).createShader(bounds);
        //     },
        //     child: UserPersonalTagProgressItem(
        //       data: data[qtdItemsToAdd],
        //     ));
        res.add(UserPersonalTagProgressItem(
          data: data[qtdItemsToAdd],
        ));
      }
    }

    return AnimatedSize(
        duration: const Duration(milliseconds: 300),
        curve: Curves.decelerate,
        child: Column(
          children: res,
        ));
  }

  Widget getPersonalityCard(List<UserIqInfoEntity> data) {
    return TripleBuilder(
        store: _userPersonalityShowMoreStore,
        builder: (_, triple) {
          final state = (triple as Triple<bool>).state;
          return getPersonalityTagsProgress(data, state);
        });
  }

  void handleTogglePersonalityShowMoreState({bool? value}) {
    bool newValue = value ?? !_userPersonalityShowMoreStore.state;
    _userPersonalityShowMoreStore.setState(newValue);
  }

  Widget getCategoriesCard(UserEntity userInfo, List<UserIqInfoEntity> info) {
    Widget userRadiusChart = const SizedBox();

    try {
      userRadiusChart = UserRadiusChart(
        userAvatar: userInfo.avatarUrl,
        data: info,
        height: 298,
        width: 322,
        showDimensionDetails: handleShowDimensionInfo,
      );
      // Container(
      //     transform:  Matrix4.translationValues(0, dataCleaned.isNotEmpty ? -28 : 0, 0),
      //     child: );
    } catch (err) {
      // print('err: $err');
    }

    return userRadiusChart;
  }

  Widget getStagesItemsCards(List<UserIqInfoEntity> infoData) {
    final List<Widget> items = [];
    for (int i = 0; i < infoData.length; i++) {
      items.add(UserStageTagDetailCard(
        onTapPlayToUnlock: goToLobbyPage,
        data: infoData[i],
        margin: const EdgeInsets.only(bottom: 15, left: 12, right: 12),
      ));
    }
    return Column(
      children: items,
    );
  }

  void handleShowDimensionInfo(UserIqInfoEntity dimensionInfo) {
    ShowDimensionDialog(data: dimensionInfo);
  }

  Widget getStagesCard(List<UserIqInfoEntity> infoData) {
    return DimensionsCard(
      iqInfoData: infoData,
      onShowDimensionInfo: handleShowDimensionInfo,
    );
    // Column(
    //   mainAxisSize: MainAxisSize.min,
    //   children: [
    //     DimensionsCard(
    //       iqInfoData: infoData,
    //       onShowDimensionInfo: handleShowDimensionInfo,
    //     ),
    //     Padding(
    //       padding: const EdgeInsets.only(bottom: 21),
    //       child: getStagesItemsCards(infoData),
    //     ),
    //   ],
    // );
  }

  Widget getWidgetInsideCustomScrollView(Widget w) {
    return Builder(
      builder: (BuildContext context) {
        return CustomScrollView(
          slivers: <Widget>[
            SliverOverlapInjector(
              handle: NestedScrollView.sliverOverlapAbsorberHandleFor(context),
            ),
            SliverPadding(
                padding: const EdgeInsets.all(1.0),
                sliver: SliverToBoxAdapter(child: w)),
          ],
        );
      },
    );
  }

  Widget getMyQuyckynessCharts(UserEntity userInfo, UserIQEntity iq) {
    double? height;
    List<Widget> children = [
      Padding(
        padding: const EdgeInsets.only(top: 23.0),
        child: Text(
          _remoteConfigStore.state.profileTitleChart,
          style: const TextStyle(
              letterSpacing: 1.8,
              fontFamily: "Roboto",
              fontWeight: FontWeight.bold,
              color: CustomColors.americanPurple2,
              fontSize: 16),
        ),
      ),
    ];
    late final List<UserIqInfoEntity> dataCleaned;

    dataCleaned = iq.info.where((element) => element.score > 0).toList();
    dataCleaned.sort((a, b) => b.score.compareTo(a.score));

    if (dataCleaned.length > 1) {
      children.addAll([
        const SizedBox(height: 5),
        getCategoriesCard(userInfo, dataCleaned),
        // const SizedBox(height: 10),
        getPersonalityCard(iq.info),
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 20.0),
          child: ProfileThreeCirclesAnimated(
            onTap: handleTogglePersonalityShowMoreState,
            duration: const Duration(milliseconds: 900),
          ),
        ),
      ]);
    } else {
      height = 295;
      children.add(getHasNoEnoughData());
    }
    return ProfileCard(
      width: 325,
      height: height,
      withShadow: false,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 18.0),
        child: Column(
          // mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: children,
        ),
      ),
    );
  }

  Widget getChartView(UserEntity userInfo) {
    var v = TripleBuilder(
        store: _userIQStore,
        builder: (context, tripleState) {
          final state = (tripleState as Triple<UserIQStoreEntity>).state;
          return Container(
            color: CustomColors.cultured,
            child: Column(
              children: [
                const SizedBox(height: 20),
                getProfileAccuracyWidget(state.iq.progress),
                const SizedBox(height: 20),
                getMyQuyckynessCharts(userInfo, state.iq),
                const SizedBox(height: 20),
                getStagesCard(state.iq.info),
              ],
            ),
          );
        });

    return getWidgetInsideCustomScrollView(v);
  }

  Widget getMatchHistoryView() {
    return SingleChildScrollView(
      child: Padding(
          padding: EdgeInsets.only(top: 4.98.h, bottom: 3.h),
          child: FriendMatchHistory()),
    );
  }

  Widget getHistoryView() {
    return UserProfileHistory(
        id: _userState.state.user.id,
        customScrollView: getWidgetInsideCustomScrollView);
  }

  Widget getBadgesView() {
    return SizedBox(
      height: MediaQuery.of(context).size.height - 470,
      child: const Column(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              'COMING SOON',
              style: TextStyle(
                  letterSpacing: 0.25,
                  fontFamily: "Roboto",
                  fontWeight: FontWeight.w900,
                  color: CustomColors.americanPurple2,
                  fontSize: 13),
            ),
            SizedBox(height: 4),
            Text('New features are coming,\nhold tight for more updates!',
                textAlign: TextAlign.center,
                style: TextStyle(
                    letterSpacing: 0.25,
                    fontFamily: "Roboto",
                    height: 1.2,
                    fontWeight: FontWeight.w400,
                    color: CustomColors.americanPurple2,
                    fontSize: 13))
          ]),
    );
  }

  Widget getHasNoEnoughData() {
    return const Expanded(
      child: Column(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              'NOT ENOUGH DATA',
              style: TextStyle(
                  letterSpacing: 0.25,
                  fontFamily: "Roboto",
                  fontWeight: FontWeight.w900,
                  color: CustomColors.americanPurple2,
                  fontSize: 13),
            ),
            SizedBox(height: 4),
            Text('Come back after playing\na few more matches!',
                textAlign: TextAlign.center,
                style: TextStyle(
                    letterSpacing: 0.25,
                    fontFamily: "Roboto",
                    height: 1.2,
                    fontWeight: FontWeight.w400,
                    color: CustomColors.americanPurple2,
                    fontSize: 13))
          ]),
    );
  }

  Widget getWidgets() {
    // Widget ret = TripleBuilder(
    //     store: _userProfileStore,
    //     builder: (context, triple) {
    //       var store = triple as Triple<UserProfileStoreEntity>;
    //       return store.state.showSettings
    //           ? getIQWidget()
    //           : getButtons(alreadySocialLogin);
    //     }); // getButtons(alreadySocialLogin);
    return const UserProfileTab(tabs: ['CHART', "TOP VIBES", "HISTORY"]);
    //   children: [
    //     getChartView(),
    //     getHistoryView(),
    //     getBadgesView(),
    //   ],
    // );
  }

  handleOpenSettingsModal() {
    ShowUserProfileSettings(handleSave: handleSave);
  }

  Widget getHeaderRightButton() {
    return IconButton(
        onPressed: handleOpenSettingsModal,
        icon: const Icon(
          QuyckyIcons.gear,
          color: Colors.white,
          size: 22,
        ));
  }

  void handleUserTextFormLostFocus(PointerDownEvent v) {
    handleEditUserName(state: false);
    handleSave();
  }

  Widget getTextForm() {
    return Padding(
      padding: const EdgeInsets.only(top: 16.0),
      child: TapRegion(
        onTapOutside: handleUserTextFormLostFocus,
        child: TextFormField(
          textAlign: TextAlign.center,
          controller: _controller.userNameTextEditingController,
          autofocus: true,
          inputFormatters: [UpperCaseTextFormatter()],
          focusNode: _userNameFieldFocusNode,
          onChanged: (value) {
            userName = value;
          },
          onSaved: (value) {
            userName = value ?? "";
          },
          validator: (value) {
            if (value != null && value.length < 3) {
              return 'a minimum of 3 characters is required';
            }
            return null;
          },
          decoration: InputDecoration(
              filled: true,
              fillColor: Colors.transparent,
              border: OutlineInputBorder(
                  borderSide: BorderSide.none,
                  borderRadius: BorderRadius.circular(50)),
              hintText: 'Type Your Name!',
              hintStyle: const TextStyle(
                  letterSpacing: 1.6,
                  fontFamily: "Roboto",
                  fontWeight: FontWeight.w700,
                  color: CustomColors.orangeSoda,
                  fontSize: 16)),
          style: const TextStyle(
              letterSpacing: 2,
              fontFamily: "Roboto",
              fontWeight: FontWeight.bold,
              color: CustomColors.orangeSoda,
              fontSize: 16),
        ),
      ),
    );
  }

  Widget getUserNameWidget() {
    return Center(
      child: InkWell(
        onTap: handleEditUserName,
        child: TripleBuilder(
            store: _userState,
            builder: (context, triple) {
              var state = (triple as Triple<UserLoginResultEntity>).state;
              return Text(
                state.user.name,
                style: const TextStyle(
                    letterSpacing: 1.6,
                    fontFamily: "Roboto",
                    fontWeight: FontWeight.w700,
                    color: CustomColors.orangeSoda,
                    fontSize: 16),
              );
            }),
      ),
    );
  }

  void handleEditUserName({state = true}) {
    if (!_userNameFieldFocusNode.hasFocus) {
      _userProfileStore.setshowSettings(state);
    }
  }

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      initialIndex: 0,
      length: 3,
      child: Scaffold(
        body: GradientContainer(
          useDefault: true,
          normalOpacity: 0,
          hotOpacity: 0,
          coldOpacity: 0,
          child: SafeArea(
            bottom: false,
            child: NestedScrollView(
              controller: _customScrollController,
              headerSliverBuilder: (context, innerBoxIsScrolled) {
                return [
                  SliverOverlapAbsorber(
                      handle: NestedScrollView.sliverOverlapAbsorberHandleFor(
                          context),
                      sliver: TripleBuilder(
                          store: _userProfileStore,
                          builder: (context, triple) {
                            final userProfileState =
                                (triple as Triple<UserProfileStoreEntity>)
                                    .state;
                            return SliverAppBar(
                              titleSpacing: 0.0,
                              forceMaterialTransparency: true,
                              pinned: userProfileState.pinnedTab,
                              expandedHeight: 300,
                              toolbarHeight: userProfileState.pinnedTab
                                  ? kToolbarHeight
                                  : 380,
                              // backgroundColor: Colors.transparent,
                              bottom: PreferredSize(
                                preferredSize: Size.fromHeight(
                                    userProfileState.pinnedTab ? 0.0 : 0.0),
                                child: Container(
                                  width: double.maxFinite,
                                  color: Colors.white,
                                  child: const Padding(
                                    padding: EdgeInsets.symmetric(
                                        vertical: 15.0, horizontal: 18),
                                    child: UserProfileTab(
                                      tabs: ['CHART', "TOP VIBES", "HISTORY"],
                                      useController: false,
                                    ),
                                  ),
                                ),
                              ),
                              title: TripleBuilder(
                                store: _userState,
                                builder: (context, triple) {
                                  var state =
                                      (triple as Triple<UserLoginResultEntity>)
                                          .state;

                                  return Column(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        const SizedBox(
                                          height: 40,
                                        ),
                                        AppHeader(
                                            logoSectionLeftWidget: IconButton(
                                                onPressed: handleBack,
                                                icon: const Icon(
                                                  QuyckyIcons.arrow_left_circle,
                                                  color: Colors.white,
                                                  size: 23,
                                                )),
                                            title: 'YOUR PROFILE',
                                            logoPath:
                                                'assets/img/svg/quycky_logo_white.svg',
                                            logoSectionRightWidget:
                                                getHeaderRightButton()),
                                        SizedBox(
                                          height: 240,
                                          child: Stack(
                                            children: [
                                              Align(
                                                alignment: Alignment.topCenter,
                                                child: SizedBox(
                                                  height: 230,
                                                  child: Stack(
                                                    alignment: Alignment.center,
                                                    children: [
                                                      Stack(
                                                        alignment: Alignment
                                                            .bottomCenter,
                                                        children: [
                                                          Container(
                                                              height: 40,
                                                              color: CustomColors
                                                                  .gainsbora),
                                                          Align(
                                                            alignment: Alignment
                                                                .bottomCenter,
                                                            child: Container(
                                                              height: 163,
                                                              width: double
                                                                  .maxFinite,
                                                              decoration:
                                                                  const BoxDecoration(
                                                                      color: Colors
                                                                          .white,
                                                                      borderRadius:
                                                                          BorderRadius
                                                                              .only(
                                                                        topLeft:
                                                                            Radius.circular(30),
                                                                        topRight:
                                                                            Radius.circular(30),
                                                                      )),
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                      Align(
                                                        alignment:
                                                            Alignment.topCenter,
                                                        child: Padding(
                                                          padding:
                                                              const EdgeInsets
                                                                  .symmetric(
                                                                  horizontal:
                                                                      0),
                                                          child: SizedBox(
                                                            height: 130,
                                                            width: 105,
                                                            child: Stack(
                                                              alignment:
                                                                  Alignment
                                                                      .center,
                                                              children: [
                                                                Avatar(
                                                                  outlined:
                                                                      true,
                                                                  size: 100,
                                                                  addPhotoButton:
                                                                      false,
                                                                  imagePath: state
                                                                      .user
                                                                      .avatarUrl,
                                                                  onPressed:
                                                                      getImageAndUpload,
                                                                ),
                                                                Align(
                                                                  alignment:
                                                                      Alignment
                                                                          .bottomCenter,
                                                                  child:
                                                                      Container(
                                                                    width: 82,
                                                                    height: 33,
                                                                    decoration: BoxDecoration(
                                                                        border: Border.all(width: 2, color: Colors.white),
                                                                        color: CustomColors.cultured,
                                                                        borderRadius: const BorderRadius.only(
                                                                          topLeft:
                                                                              Radius.circular(20),
                                                                          topRight:
                                                                              Radius.circular(20),
                                                                          bottomLeft:
                                                                              Radius.circular(20),
                                                                          bottomRight:
                                                                              Radius.circular(20),
                                                                        )),
                                                                    child: Row(
                                                                      mainAxisAlignment:
                                                                          MainAxisAlignment
                                                                              .spaceAround,
                                                                      children: [
                                                                        Image
                                                                            .asset(
                                                                          Assets
                                                                              .png
                                                                              .fire,
                                                                          width:
                                                                              20,
                                                                        ),
                                                                        Text(
                                                                          state
                                                                              .user
                                                                              .pontuation
                                                                              .toString(),
                                                                          style: const TextStyle(
                                                                              letterSpacing: 3,
                                                                              fontFamily: "Roboto",
                                                                              fontWeight: FontWeight.bold,
                                                                              color: CustomColors.americanPurple,
                                                                              fontSize: 12),
                                                                        ),
                                                                      ],
                                                                    ),
                                                                  ),
                                                                )
                                                              ],
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                      Align(
                                                          alignment: Alignment
                                                              .bottomCenter,
                                                          child: Column(
                                                            mainAxisSize:
                                                                MainAxisSize
                                                                    .min,
                                                            children: [
                                                              GestureDetector(
                                                                onTap:
                                                                    handleEditUserName,
                                                                child:
                                                                    Container(
                                                                  width: 213,
                                                                  height: 37,
                                                                  margin:
                                                                      const EdgeInsets
                                                                          .only(
                                                                          bottom:
                                                                              38),
                                                                  decoration:
                                                                      BoxDecoration(
                                                                          border: Border.all(
                                                                              width:
                                                                                  2,
                                                                              color: Colors
                                                                                  .white),
                                                                          color: CustomColors
                                                                              .cultured,
                                                                          borderRadius:
                                                                              const BorderRadius.only(
                                                                            topLeft:
                                                                                Radius.circular(20),
                                                                            topRight:
                                                                                Radius.circular(20),
                                                                            bottomLeft:
                                                                                Radius.circular(20),
                                                                            bottomRight:
                                                                                Radius.circular(20),
                                                                          )),
                                                                  child:
                                                                      TripleBuilder(
                                                                          store:
                                                                              _userProfileStore,
                                                                          builder:
                                                                              (context, triple) {
                                                                            final userProfileState =
                                                                                (triple as Triple<UserProfileStoreEntity>).state;
                                                                            return userProfileState.showSettings
                                                                                ? getTextForm()
                                                                                : getUserNameWidget();
                                                                          }),
                                                                ),
                                                              ),
                                                              // const Padding(
                                                              //   padding:
                                                              //       EdgeInsets.only(
                                                              //           bottom: 12.0),
                                                              //   child: UserProfileTab(
                                                              //     tabs: [
                                                              //       'CHART',
                                                              //       "HISTORY",
                                                              //       "HISTORY"
                                                              //     ],
                                                              //     useController: false,
                                                              //   ),
                                                              // ),
                                                            ],
                                                          )),
                                                    ],
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ]);
                                },
                              ),
                              automaticallyImplyLeading: false,

                              forceElevated: innerBoxIsScrolled,
                            );
                          }))
                ];
              },
              body: Container(
                color: CustomColors.cultured,
                child: TripleBuilder(
                  store: _userState,
                  builder: (context, triple) {
                    var state = triple as Triple<UserLoginResultEntity>;
                    return QuyckyModalProgress(
                        state: state.isLoading,
                        child: Column(children: [
                          Flexible(
                            // height: MediaQuery.of(context).size.height,
                            child: Padding(
                              padding: const EdgeInsets.only(
                                  top: kIsWeb ? 40 : 0, bottom: 20),
                              child: TabBarView(
                                children: [
                                  getChartView(state.state.user),
                                  getMatchHistoryView(),
                                  getHistoryView(),
                                ],
                              ),
                            ),
                          ),
                        ]));
                  },
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class ProfileCard extends StatelessWidget {
  final Widget? child;
  final double? height;
  final double? width;
  final bool withShadow;
  final Color borderColor;
  final Color backgroundColor;

  const ProfileCard({
    super.key,
    this.child,
    this.height,
    this.width,
    this.backgroundColor = Colors.white,
    this.withShadow = true,
    this.borderColor = Colors.white,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
        height: height,
        width: width,
        margin: const EdgeInsets.only(top: 4),
        decoration: BoxDecoration(
          color: backgroundColor,
          border: Border.all(color: borderColor, width: 4),
          borderRadius: const BorderRadius.all(Radius.circular(30)),
          boxShadow: withShadow
              ? [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.5),
                    spreadRadius: 1,
                    blurRadius: 3,
                    offset: const Offset(0, 0), // Changes shadow direction
                  ),
                ]
              : null,
        ),
        child: child);
  }
}
