import 'package:equatable/equatable.dart';

class UserLoginEntity extends Equatable {
  final String id;
  final String identifier;
  final String password;
  final String onesignalId;

  const UserLoginEntity({
    this.id = '',
    this.identifier = '',
    this.password = '',
    this.onesignalId = '',
  });

  @override
  List<Object> get props => [
        id,
        identifier,
        password,
        onesignalId,
      ];
}
