// ignore_for_file: public_member_api_docs, sort_constructors_first

import 'dart:convert';

import 'package:equatable/equatable.dart';

class UserProfileStoreEntity extends Equatable {
  final bool showSettings;
  final bool pinnedTab;

  const UserProfileStoreEntity({
    this.showSettings = false,
    this.pinnedTab = false,
  });

  UserProfileStoreEntity copyWith({
    bool? showSettings,
    bool? pinnedTab,
  }) {
    return UserProfileStoreEntity(
      showSettings: showSettings ?? this.showSettings,
      pinnedTab: pinnedTab ?? this.pinnedTab,
    );
  }

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'showSettings': showSettings,
      'pinnedTab': pinnedTab,
    };
  }

  factory UserProfileStoreEntity.fromMap(Map<String, dynamic> map) {
    return UserProfileStoreEntity(
      showSettings: map['showSettings'] as bool,
      pinnedTab: map['pinnedTab'] as bool,
    );
  }

  String toJson() => json.encode(toMap());

  factory UserProfileStoreEntity.fromJson(String source) =>
      UserProfileStoreEntity.fromMap(
          json.decode(source) as Map<String, dynamic>);

  @override
  bool get stringify => true;

  @override
  List<Object> get props => [showSettings, pinnedTab];
}
