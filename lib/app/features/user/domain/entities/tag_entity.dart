// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:convert';
import 'dart:ui';

import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:quycky/app/theme/colors.dart';
import 'package:quycky/core/utils/type_converters.dart';

class TagEntity extends Equatable {
  final String cod;
  final String title;
  final String description;
  final String vibecheckDescription;
  final DateTime createdAt;
  final DateTime updatedAt;
  final Color color;
  final String icon;

  const TagEntity({
    required this.cod,
    required this.title,
    required this.description,
    required this.vibecheckDescription,
    required this.createdAt,
    required this.updatedAt,
    this.color = CustomColors.primary,
    this.icon = '',
  });

  TagEntity copyWith({
    Color? color,
    String? icon,
    String? cod,
    String? title,
    String? description,
    String? vibecheckDescription,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return TagEntity(
        icon: icon ?? this.icon,
        cod: cod ?? this.cod,
        title: title ?? this.title,
        description: description ?? this.description,
        vibecheckDescription: vibecheckDescription ?? this.vibecheckDescription,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        color: color ?? this.color);
  }

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'cod': cod,
      'title': title,
      'description': description,
      'vibecheck_description': vibecheckDescription,
      'created_at': createdAt.millisecondsSinceEpoch,
      'updated_at': updatedAt.millisecondsSinceEpoch,
      'icon': icon,
      'color': '#${color.value.toRadixString(16)}',
    };
  }

  factory TagEntity.fromMap(Map<String, dynamic> map) {
    var olderDate = DateTime.fromMillisecondsSinceEpoch(1);
    var color =
        map['color'].runtimeType != Null && (map['color'] as String) != ''
            ? stringToColor((map['color'] as String))
            : CustomColors.primary;
    return TagEntity(
      cod: map['cod'] as String,
      icon: map['icon'].runtimeType != Null ? map['icon'] as String : '',
      color: color,
      title: map['title'] as String,
      description: map['description'] as String,
      vibecheckDescription: dynamicToString(map['vibecheck_description']),
      createdAt: map['created_at'].runtimeType != Null
          ? DateTime.parse(map['created_at'])
          : olderDate,
      updatedAt: map['updated_at'].runtimeType != Null
          ? DateTime.parse(map['updated_at'])
          : olderDate,
    );
  }

  String toJson() => json.encode(toMap());

  factory TagEntity.fromJson(String source) =>
      TagEntity.fromMap(json.decode(source) as Map<String, dynamic>);

  @override
  bool get stringify => true;

  @override
  List<Object> get props {
    return [
      cod,
      title,
      description,
      vibecheckDescription,
      createdAt,
      updatedAt,
    ];
  }
}
