import 'dart:io';

import 'package:dartz/dartz.dart';
import 'package:quycky/app/features/user/domain/repositories/user_repository.dart';

import 'package:quycky/app/features/user/domain/entities/user_entity.dart';
import 'package:quycky/core/usecase/errors/failure.dart';
import 'package:quycky/core/usecase/usecase.dart';

class UploadUserAvatarUseCase implements UseCase<UserEntity, File> {
  final IUserRepository repository;

  UploadUserAvatarUseCase(
    this.repository,
  );

  @override
  Future<Either<Failure, UserEntity>> call(File file) async {
    return await repository.uploadUserAvatar(file);
  }
}
