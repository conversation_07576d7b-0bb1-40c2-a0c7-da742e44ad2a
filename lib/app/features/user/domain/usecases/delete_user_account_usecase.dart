import 'package:dartz/dartz.dart';
import 'package:quycky/app/features/user/domain/repositories/user_repository.dart';
import 'package:quycky/core/usecase/errors/failure.dart';
import 'package:quycky/core/usecase/usecase.dart';

class DeleteUserAccountUseCase implements UseCase<bool, int> {
  final IUserRepository repository;

  DeleteUserAccountUseCase(
    this.repository,
  );

  @override
  Future<Either<Failure, bool>> call(int id) async {
    return await repository.deleteUserAccount(id);
  }
}
