// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:dartz/dartz.dart';
import 'package:quycky/app/features/user/domain/entities/user_iq_entity.dart';
import 'package:quycky/app/features/user/domain/repositories/user_repository.dart';

import 'package:quycky/core/usecase/errors/failure.dart';
import 'package:quycky/core/usecase/usecase.dart';

class GetUserIQUseCase implements UseCase<UserIQEntity, NoParams> {
  final IUserRepository repository;

  GetUserIQUseCase(
    this.repository,
  );

  @override
  Future<Either<Failure, UserIQEntity>> call(NoParams noParams) async {
    return await repository.getUserIQ();
  }
}
