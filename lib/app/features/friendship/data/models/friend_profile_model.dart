import 'dart:convert';

import 'package:quycky/app/features/friendship/domain/entities/friendship_profile_entity.dart';
import 'package:quycky/app/features/user/data/models/user_model.dart';
import 'package:quycky/core/utils/type_converters.dart';

class FriendProfileModel extends FriendProfileEntity {
  FriendProfileModel({
    required UserModel user,
    required List<FriendshipAnswerModel> answers,
  }) : super(user: user, answers: answers);

  factory FriendProfileModel.fromRawJson(String str) =>
      FriendProfileModel.fromJson(json.decode(str));

  @override
  String toRawJson() => json.encode(toJson());

  factory FriendProfileModel.fromJson(Map<String, dynamic> json) {
    return FriendProfileModel(
      user: UserModel.fromJson(json["user"]),
      answers: List<FriendshipAnswerModel>.from(
          json["answers"].map((x) => FriendshipAnswerModel.from<PERSON>son(x))),
    );
  }

  Map<String, dynamic> toJson() => {
        "user": (user as UserModel).toJson(),
        "answers": List<dynamic>.from(
            answers.map((x) => (x as FriendshipAnswerModel).toJson())),
      };
}

class FriendshipAnswerModel extends FriendshipAnswerEntity {
  FriendshipAnswerModel({
    required id,
    required userId,
    required roomId,
    required questionId,
    required value,
    required createdAt,
    required updatedAt,
    required FriendshipRoomModel room,
    required FriendshipQuestionModel question,
    required List<FriendshipPointModel> points,
  }) : super(
            id: id,
            userId: userId,
            roomId: roomId,
            questionId: questionId,
            value: value,
            createdAt: createdAt,
            updatedAt: updatedAt,
            room: room,
            question: question,
            points: points);

  @override
  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'user_id': userId,
      'room_id': roomId,
      'question_id': questionId,
      'value': value,
      'created_at': createdAt.millisecondsSinceEpoch,
      'updated_at': updatedAt.millisecondsSinceEpoch,
      'room': (room as FriendshipRoomModel).toMap(),
      'question': (question as FriendshipQuestionModel).toMap(),
      'points': points.map((x) => (x as FriendshipPointModel).toMap()).toList(),
    };
  }

  factory FriendshipAnswerModel.fromMap(Map<String, dynamic> map) {
    var olderDate = DateTime.fromMillisecondsSinceEpoch(1);
    return FriendshipAnswerModel(
      id: map['id'] as String,
      userId: map['user_id'] as String,
      roomId: map['room_id'] as String,
      questionId: map['question_id'] as String,
      value: map['value'] as String,
      createdAt: map['created_at'].runtimeType != Null
          ? DateTime.parse(map['created_at'])
          : olderDate,
      updatedAt: map['updated_at'].runtimeType != Null
          ? DateTime.parse(map['updated_at'])
          : olderDate,
      room: FriendshipRoomModel.fromMap(map['room'] as Map<String, dynamic>),
      question: FriendshipQuestionModel.fromMap(
          map['question'] as Map<String, dynamic>),
      points: List<FriendshipPointModel>.from(
        (map['points'] as List).map<FriendshipPointModel>(
          (x) => FriendshipPointModel.fromMap(x as Map<String, dynamic>),
        ),
      ),
    );
  }

  String toJson() => json.encode(toMap());

  factory FriendshipAnswerModel.fromJson(
          Map<String, dynamic> friendshipAnswerModel) =>
      FriendshipAnswerModel.fromMap(friendshipAnswerModel);

  factory FriendshipAnswerModel.fromRawJson(String source) =>
      FriendshipAnswerModel.fromMap(
          json.decode(source) as Map<String, dynamic>);
}

class FriendshipQuestionModel extends FriendshipQuestionEntity {
  FriendshipQuestionModel({
    required id,
    required description,
    required isActived,
    required stage,
    required createdAt,
    required updatedAt,
  }) : super(
          id: id,
          description: description,
          isActived: isActived,
          stage: stage,
          createdAt: createdAt,
          updatedAt: updatedAt,
        );

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'description': description,
      'isActived': isActived,
      'stage': stage,
      'created_at': createdAt.millisecondsSinceEpoch,
      'updated_at': updatedAt.millisecondsSinceEpoch,
    };
  }

  factory FriendshipQuestionModel.fromMap(Map<String, dynamic> map) {
    return FriendshipQuestionModel(
      id: map['id'] as String,
      description: map['description'] as String,
      isActived: map['is_actived'] as int,
      stage: map['stage'] as int,
      createdAt: map['created_at'] != null
          ? DateTime.parse(map['created_at'])
          : DateTime.now(),
      updatedAt: map['updated_at'] != null
          ? DateTime.parse(map['updated_at'])
          : DateTime.now(),
    );
  }

  String toJson() => json.encode(toMap());

  factory FriendshipQuestionModel.fromJson(String source) =>
      FriendshipQuestionModel.fromMap(
          json.decode(source) as Map<String, dynamic>);
}

class FriendshipRoomModel extends FriendshipRoomEntity {
  FriendshipRoomModel({
    required id,
    required name,
    required howManyPeople,
    required isBloqued,
    required questionTimeInSeconds,
    required responseTimeInSeconds,
    required createdAt,
    required updatedAt,
  }) : super(
          id: id,
          name: name,
          howManyPeople: howManyPeople,
          isBloqued: isBloqued,
          questionTimeInSeconds: questionTimeInSeconds,
          responseTimeInSeconds: responseTimeInSeconds,
          createdAt: createdAt,
          updatedAt: updatedAt,
        );

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'name': name,
      'howManyPeople': howManyPeople,
      'isBloqued': isBloqued,
      'questionTimeInSeconds': questionTimeInSeconds,
      'responseTimeInSeconds': responseTimeInSeconds,
      'created_at': createdAt.millisecondsSinceEpoch,
      'updated_at': updatedAt.millisecondsSinceEpoch,
    };
  }

  factory FriendshipRoomModel.fromMap(Map<String, dynamic> map) {
    return FriendshipRoomModel(
      id: map['id'] as String,
      name: map['name'] as String,
      howManyPeople: dynamicToInt(map['how_many_people']),
      isBloqued: map['is_bloqued'] as bool,
      questionTimeInSeconds: dynamicToInt(map['question_time_in_seconds']),
      responseTimeInSeconds: dynamicToInt(map['response_time_in_seconds']),
      createdAt: map['created_at'] != null
          ? DateTime.parse(map['created_at'])
          : map['created_at'],
      updatedAt: map['updated_at'] != null
          ? DateTime.parse(map['updated_at'])
          : map['updated_at'],
    );
  }

  String toJson() => json.encode(toMap());

  factory FriendshipRoomModel.fromJson(String source) =>
      FriendshipRoomModel.fromMap(json.decode(source) as Map<String, dynamic>);
}

class FriendshipPointModel extends FriendshipPointEntity {
  FriendshipPointModel({
    required id,
    required userId,
    required roomId,
    required questionId,
    required levelId,
    required answerId,
    required pointTypeId,
    required userGiveId,
    required createdAt,
    required updatedAt,
  }) : super(
          id: id,
          userId: userId,
          roomId: roomId,
          questionId: questionId,
          levelId: levelId,
          answerId: answerId,
          pointTypeId: pointTypeId,
          userGiveId: userGiveId,
          createdAt: createdAt,
          updatedAt: updatedAt,
        );

  @override
  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'user_id': userId,
      'room_id': roomId,
      'question_id': questionId,
      'level_id': levelId,
      'answer_id': answerId,
      'point_type_id': pointTypeId,
      'user_give_id': userGiveId,
      'created_at': createdAt.millisecondsSinceEpoch,
      'updated_at': updatedAt.millisecondsSinceEpoch,
    };
  }

  factory FriendshipPointModel.fromMap(Map<String, dynamic> map) {
    var olderDate = DateTime.fromMillisecondsSinceEpoch(1);
    return FriendshipPointModel(
      id: int.tryParse(map['id']) ?? 0,
      userId: int.tryParse(map['user_id']) ?? 0,
      roomId: int.tryParse(map['room_id']) ?? 0,
      questionId: int.tryParse(map['question_id']) ?? 0,
      levelId: int.tryParse(map['level_id']) ?? 0,
      answerId: int.tryParse(map['answer_id']) ?? 0,
      pointTypeId: int.tryParse(map['point_type_id']) ?? 0,
      userGiveId: int.tryParse(map['user_give_id']) ?? 0,
      createdAt: map['created_at'].runtimeType != Null
          ? DateTime.parse(map['created_at'])
          : olderDate,
      updatedAt: map['updated_at'].runtimeType != Null
          ? DateTime.parse(map['updated_at'])
          : olderDate,
    );
  }

  String toJson() => json.encode(toMap());

  factory FriendshipPointModel.fromJson(String source) =>
      FriendshipPointModel.fromMap(json.decode(source) as Map<String, dynamic>);
}
