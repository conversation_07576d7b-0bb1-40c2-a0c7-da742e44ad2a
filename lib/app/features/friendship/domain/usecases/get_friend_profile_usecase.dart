// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:dartz/dartz.dart';
import 'package:quycky/app/features/friendship/domain/entities/friendship_profile_entity.dart';
import 'package:quycky/app/features/friendship/domain/repositories/friendship_repository.dart';

import 'package:quycky/core/usecase/errors/failure.dart';
import 'package:quycky/core/usecase/usecase.dart';

class GetFriendProfileUseCase implements UseCase<FriendProfileEntity, int> {
  final IFriendshipRepository repository;

  GetFriendProfileUseCase(
    this.repository,
  );

  @override
  Future<Either<Failure, FriendProfileEntity>> call(int id) async {
    return await repository.getFriendProfile(id);
  }
}
