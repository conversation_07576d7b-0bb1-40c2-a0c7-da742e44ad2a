// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:dartz/dartz.dart';
import 'package:quycky/app/features/friendship/domain/entities/invitation_ticket_entity.dart';
import 'package:quycky/app/features/friendship/domain/repositories/friendship_repository.dart';

import 'package:quycky/core/usecase/errors/failure.dart';
import 'package:quycky/core/usecase/usecase.dart';

class ValidateFriendlyInvitationTempCodeUsecase
    implements UseCase<InvitationTicketEntity, String> {
  final IFriendshipRepository repository;

  ValidateFriendlyInvitationTempCodeUsecase(
    this.repository,
  );

  @override
  Future<Either<Failure, InvitationTicketEntity>> call(String code) async {
    return await repository.validateFriendlyInvitationTempCode(code);
  }
}
