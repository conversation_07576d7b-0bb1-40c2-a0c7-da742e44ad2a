// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:dartz/dartz.dart';
import 'package:quycky/app/features/friendship/domain/entities/friend_match_entity.dart';
import 'package:quycky/app/features/friendship/domain/repositories/friendship_repository.dart';

import 'package:quycky/core/usecase/errors/failure.dart';
import 'package:quycky/core/usecase/usecase.dart';

class CheckFriendMatchParamsEntity {
  final String id;
  final bool isNewFriend;

  CheckFriendMatchParamsEntity({required this.id, this.isNewFriend = false});
}

class CheckFriendMatchUsecase
    implements UseCase<FriendMatchEntity, CheckFriendMatchParamsEntity> {
  final IFriendshipRepository repository;

  CheckFriendMatchUsecase(
    this.repository,
  );

  @override
  Future<Either<Failure, FriendMatchEntity>> call(
      CheckFriendMatchParamsEntity params) async {
    return await repository.checkFriendMatch(params.id, params.isNewFriend);
  }
}
