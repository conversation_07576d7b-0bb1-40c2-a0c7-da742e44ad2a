// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:dartz/dartz.dart';
import 'package:quycky/app/features/friendship/domain/entities/friendship_invite_entity.dart';
import 'package:quycky/app/features/friendship/domain/repositories/friendship_repository.dart';

import 'package:quycky/core/usecase/errors/failure.dart';
import 'package:quycky/core/usecase/usecase.dart';

class GetFriendshipInvitationsUseCase
    implements UseCase<List<FriendshipInviteEntity>, int> {
  final IFriendshipRepository repository;

  GetFriendshipInvitationsUseCase(
    this.repository,
  );

  @override
  Future<Either<Failure, List<FriendshipInviteEntity>>> call(int id) async {
    return await repository.getFriendshipInvitations(id);
  }
}
