// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:dartz/dartz.dart';
import 'package:quycky/app/features/friendship/domain/entities/friend_match_entity.dart';
import 'package:quycky/app/features/friendship/domain/repositories/friendship_repository.dart';

import 'package:quycky/core/usecase/errors/failure.dart';
import 'package:quycky/core/usecase/usecase.dart';

class GetFriendMatchHistoryUsecase
    implements UseCase<List<FriendMatchEntity>, NoParams> {
  final IFriendshipRepository repository;

  GetFriendMatchHistoryUsecase(
    this.repository,
  );

  @override
  Future<Either<Failure, List<FriendMatchEntity>>> call(
      NoParams noParams) async {
    return await repository.getFriendMatchHistory();
  }
}
