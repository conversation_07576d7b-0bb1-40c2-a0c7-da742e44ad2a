import 'package:dartz/dartz.dart';
import 'package:quycky/app/features/friendship/domain/entities/friend_match_entity.dart';
import 'package:quycky/app/features/friendship/domain/entities/friendship_entity.dart';
import 'package:quycky/app/features/friendship/domain/entities/friendship_invite_entity.dart';
import 'package:quycky/app/features/friendship/domain/entities/friendship_profile_entity.dart';
import 'package:quycky/app/features/friendship/domain/entities/invitation_ticket_entity.dart';
import 'package:quycky/app/features/friendship/domain/entities/profile_temp_code_entity.dart';
import 'package:quycky/app/features/friendship/domain/request_entities/invite_request.dart';
import 'package:quycky/core/usecase/errors/failure.dart';

abstract class IFriendshipRepository {
  Future<Either<Failure, FriendshipInviteEntity>> inviteUser(
      InviteRequest data);
  Future<Either<Failure, FriendshipInviteEntity>> orderInvitation(
      InviteRequest data);
  Future<Either<Failure, List<FriendshipEntity>>> getFriendships(int id);
  Future<Either<Failure, FriendProfileEntity>> getFriendProfile(int id);
  Future<Either<Failure, List<FriendshipInviteEntity>>>
      getFriendshipInvitationsReceived(int id);
  Future<Either<Failure, List<FriendshipInviteEntity>>>
      getFriendshipInvitations(int id);
  Future<Either<Failure, FriendshipInviteEntity>> acceptInvite(int id);
  Future<Either<Failure, FriendshipInviteEntity>> rejectInvite(int id);
  Future<Either<Failure, FriendMatchEntity>> checkFriendMatch(
      String friendId, bool isNewFriend);
  Future<Either<Failure, List<FriendMatchEntity>>> getFriendMatchHistory();
  Future<Either<Failure, bool>> removeFriend(int friendId);
  Future<Either<Failure, ProfileTempCodeEntity>> generateProfileTempCode();
  Future<Either<Failure, ProfileTempCodeEntity>> validateProfileTempCode(
      String code);
  Future<Either<Failure, InvitationTicketEntity>>
      generateFriendlyInvitationTempCode();
  Future<Either<Failure, InvitationTicketEntity>>
      validateFriendlyInvitationTempCode(String code);
}
