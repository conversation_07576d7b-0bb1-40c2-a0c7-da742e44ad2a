import 'package:quycky/app/features/user/domain/entities/user_entity.dart';

class FriendshipEntity {
  FriendshipEntity({
    required this.id,
    required this.userId,
    required this.friendUserId,
    required this.situation,
    required this.createdAt,
    required this.updatedAt,
    required this.friendUser,
    required this.user,
  });

  final String id;
  final String userId;
  final String friendUserId;
  final String situation;
  final DateTime createdAt;
  final DateTime updatedAt;
  final UserEntity friendUser;
  final UserEntity user;

  FriendshipEntity copyWith({
    String? id,
    String? userId,
    String? friendUserId,
    String? situation,
    DateTime? createdAt,
    DateTime? updatedAt,
    UserEntity? friendUser,
    UserEntity? user,
  }) =>
      FriendshipEntity(
        id: id ?? this.id,
        userId: userId ?? this.userId,
        friendUserId: friendUserId ?? this.friendUserId,
        situation: situation ?? this.situation,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        friendUser: friendUser ?? this.friendUser,
        user: user ?? this.user,
      );
}
