import 'dart:convert';

import 'package:quycky/core/utils/type_converters.dart';

class ProfileTempCodeEntity {
  final String id;
  final String userId;
  final String friendId;
  final String code;
  final String status;
  final DateTime createdAt;
  final String message;

  ProfileTempCodeEntity({
    required this.id,
    required this.userId,
    required this.friendId,
    required this.code,
    required this.status,
    required this.createdAt,
    required this.message,
  });

  ProfileTempCodeEntity copyWith({
    String? id,
    String? userId,
    String? friendId,
    String? code,
    String? status,
    DateTime? createdAt,
    String? message,
  }) =>
      ProfileTempCodeEntity(
        id: id ?? this.id,
        userId: userId ?? this.userId,
        friendId: friendId ?? this.friendId,
        code: code ?? this.code,
        status: status ?? this.status,
        createdAt: createdAt ?? this.createdAt,
        message: message ?? this.message,
      );

  factory ProfileTempCodeEntity.fromRawJson(String str) =>
      ProfileTempCodeEntity.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory ProfileTempCodeEntity.fromJson(Map<String, dynamic> json) =>
      ProfileTempCodeEntity(
        id: dynamicToString(json["id"]),
        userId: dynamicToString(json["user_id"]),
        friendId: dynamicToString(json["friend_id"]),
        code: dynamicToString(json["code"]),
        status: dynamicToString(json["status"]),
        createdAt: DateTime.parse(json["created_at"]),
        message: dynamicToString(json["message"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "user_id": userId,
        "friend_id": friendId,
        "code": code,
        "status": status,
        "created_at": createdAt.toIso8601String(),
        "message": message,
      };
}
