import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:quycky/analytics.dart';
import 'package:quycky/app/features/friendship/domain/entities/friend_match_entity.dart';
import 'package:quycky/app/features/friendship/presenter/controllers/friendship_controller.dart';
import 'package:quycky/app/features/friendship/presenter/pages/widgets/match_bottom_info.dart';
import 'package:quycky/app/features/friendship/presenter/pages/widgets/match_percentage_presentation.dart';
import 'package:quycky/app/features/friendship/presenter/pages/widgets/players_match_photos.dart';
import 'package:quycky/app/features/game/presenter/storage/game_storage.dart';
import 'package:quycky/app/features/user/domain/entities/tag_entity.dart';
import 'package:quycky/app/features/user/domain/entities/user_entity.dart';
import 'package:quycky/app/features/user/domain/usecases/get_user_by_id_usecase.dart';
import 'package:quycky/app/features/user/presenter/store/user_store.dart';
import 'package:quycky/app/services/remote_config/remote_config_store.dart';
import 'package:quycky/app/theme/colors.dart';
import 'package:quycky/app/widgets/app_header.dart';
import 'package:quycky/app/widgets/custom_image.dart';
import 'package:quycky/app/widgets/gradient_container.dart';
import 'package:quycky/core/utils/app_routes.dart';
import 'package:quycky/core/utils/assets_strings.dart';
import 'package:quycky/core/utils/color_utils.dart';
import 'package:quycky/core/utils/quycky_icons_icons.dart';
import 'package:quycky/core/utils/type_converters.dart';
import 'package:sizer/sizer.dart';

class FriendMatch extends StatefulWidget {
  final String friendId;
  final bool isNewFriend;
  const FriendMatch(
      {super.key, required this.friendId, this.isNewFriend = false});

  @override
  State<FriendMatch> createState() => _FriendMatchState();
}

class _FriendMatchState extends State<FriendMatch>
    with SingleTickerProviderStateMixin {
  final _userStore = Modular.get<UserStore>();
  final _controller = Modular.get<FriendshipController>();
  TabController? _slideCardsController;
  final double _progress = 1;
  int _percentage = 0;
  UserEntity _friend = UserEntity(name: '');
  bool _canDoMatch = true;
  final _getUserByIdUseCase = Modular.get<GetUserByIdUseCase>();
  FriendMatchEntity? friendMatch;

  List<TagEntity> commonDimensions = [];
  //   TagEntity(
  //       cod: "rom",
  //       title: "ROMANTIC",
  //       description:
  //           'You both tend to prioritize love, intimacy, and deep emotional bonds. You likely seek long-term, committed relationships full of closeness.',
  //       createdAt: DateTime.now(),
  //       updatedAt: DateTime.now(),
  //       color: CustomColors.oriolesOrange,
  //       icon: 'rom'),
  //   TagEntity(
  //       cod: "cl",
  //       title: "Classic",
  //       color: CustomColors.secondary,
  //       description:
  //           'You both tend to prioritize love, intimacy, and deep emotional bonds. You likely seek long-term, committed relationships full of closeness.',
  //       createdAt: DateTime.now(),
  //       updatedAt: DateTime.now(),
  //       icon: 'cl'),
  //   TagEntity(
  //       cod: "mp",
  //       color: CustomColors.salmon,
  //       title: "MULTIPLAYER",
  //       description:
  //           'You both tend to prioritize love, intimacy, and deep emotional bonds. You likely seek long-term, committed relationships full of closeness.',
  //       createdAt: DateTime.now(),
  //       updatedAt: DateTime.now(),
  //       icon: 'mp'),
  //   TagEntity(
  //       cod: "spo",
  //       color: CustomColors.salmon_4,
  //       title: "SPONTANEOUS",
  //       description:
  //           'You both tend to prioritize love, intimacy, and deep emotional bonds. You likely seek long-term, committed relationships full of closeness.',
  //       createdAt: DateTime.now(),
  //       updatedAt: DateTime.now(),
  //       icon: 'spo'),
  // ];
  bool isLoading = true;

  Color? get _currentDimensionColor => _slideCardsController != null &&
          commonDimensions.isNotEmpty &&
          _slideCardsController!.index > 1
      ? commonDimensions[_slideCardsController!.index - 2].color
      : null;

  String get _headerTitle => !_canDoMatch ? 'UNLOCK VIBE CHECK' : 'VIBE CHECK';

  String get _headerSubtitle => !_canDoMatch
      ? ''
      : isLoading
          ? 'CALCULATING ALL COMPATIBLE DIMENSIONS...'
          : _slideCardsController?.index == 0 || _percentage == 0
              ? 'BASED ON YOUR GAMEPLAY...'
              : '';

  set friend(UserEntity value) {
    setState(() {
      _friend = value;
    });
  }

  @override
  void initState() {
    super.initState();
    tryMatch();
    _logEvent();
    _verifyIfUserCanDoMatch();
  }

  Future<void> _getFriendById() async {
    final res = await _getUserByIdUseCase(dynamicToInt(widget.friendId));
    res.fold((l) => null, (r) {
      friend = r;
    });
  }

  Future<void> _logEvent() async {
    Analytics.instance.logEvent(name: 'new_vibe_check');
  }

  void setSlideCardsController(int length) {
    _slideCardsController = TabController(
      length: 2 + length,
      vsync: this,
    );

    _slideCardsController!.addListener(() {
      if (mounted) {
        setState(() {});
      }
    });

    setState(() {});
  }

  void handleTest() {
    setState(() {
      isLoading = !isLoading;
    });
  }

  void _verifyIfUserCanDoMatch() async {
    final gameStorage = Modular.get<GameStorage>();
    final remoteConfig = Modular.get<RemoteConfigStore>();
    int numberOfGamesPlayed = await gameStorage.getNumberOfGamesPlayed();
    _canDoMatch =
        remoteConfig.state.minimunRoundsToMatch <= numberOfGamesPlayed;
    setState(() {});
  }

  void tryMatch() async {
    await _getFriendById();
    Timer(Duration(seconds: 7), () {
      _controller
          .checkFriendMatch(widget.friendId, widget.isNewFriend)
          .then((value) {
        setState(() {
          _percentage = (value.matchPercent * 100).toInt();
          friendMatch = value;
          commonDimensions = value.commomDimensions.take(3).toList();
          isLoading = false;
          setSlideCardsController(commonDimensions.length);
        });
      });
    });
  }

  void handleGoTo(String route, {bool replaceRoute = false}) {
    if (replaceRoute) {
      Modular.to.pushReplacementNamed(route);
      return;
    }
    Modular.to.pushNamed(route);
  }

  Widget getLeftHeaderButton() {
    return Stack(
      children: [
        IconButton(
            onPressed: () => Modular.to.popAndPushNamed(AppRoutes.home),
            icon: Icon(
              QuyckyIcons.arrow_left_circle,
              color: Colors.white,
              size: 23,
            )),
      ],
    );
  }

  String _getCorrectImgName(String imageName) {
    switch (imageName) {
      case 'classic':
        return 'cl';
      case 'knk':
        return 'kin';
      default:
        return imageName;
    }
  }

  Widget _buildBackgroundImage(String imageName) {
    return CustomImage(Assets.svg.backgrounds[_getCorrectImgName(imageName)],
        height: 4.32.h);
  }

  Widget? _buildCustomBackground() {
    if (commonDimensions.isEmpty ||
        _slideCardsController == null ||
        _slideCardsController!.index < 2) {
      return null;
    }
    TagEntity currentTag = commonDimensions[_slideCardsController!.index - 2];
    return _currentDimensionColor == null
        ? SizedBox(
            height: double.infinity,
            width: double.infinity,
            child: _buildBackgroundImage(currentTag.cod),
          )
        : Container(
            height: double.infinity,
            width: double.infinity,
            decoration: BoxDecoration(
                gradient: LinearGradient(
              colors: [
                generateLighterColorHSL(_currentDimensionColor!),
                _currentDimensionColor!,
              ],
              stops: [0, 1],
              begin: Alignment.bottomCenter,
              end: Alignment.topCenter,
            )),
          );
  }

  int lastTimeWhenCardSlide = 0;

  List<Color> get _backgroundColors {
    if (commonDimensions.isEmpty) {
      return [];
    }
    return commonDimensions.map((e) => e.color).take(3).toList();
  }

  void _changeCardIndex(int index) {
    if (index < 0 || index >= _slideCardsController!.length) {
      return;
    }
    _slideCardsController!.index = index;
    setState(() {});
  }

  void _handleChangeCard(DragUpdateDetails details) {
    if (!_canDoMatch || isLoading) {
      return;
    }
    final currentTime = DateTime.now().millisecondsSinceEpoch;
    if ((currentTime - lastTimeWhenCardSlide) < 500) {
      return;
    }
    lastTimeWhenCardSlide = currentTime;
    if (details.delta.dx > 0 && _slideCardsController!.index > 0) {
      _changeCardIndex(_slideCardsController!.index - 1);
      print('left-right');
      return;
    }
    if (details.delta.dx < 0 &&
        _slideCardsController!.index < _slideCardsController!.length) {
      _changeCardIndex(_slideCardsController!.index + 1);
      print('right-left');
    }
  }

  double _getMatchPercentageSize() {
    double size = 75.w;
    if (isLoading) {
      return size;
    }
    if (!_canDoMatch || _slideCardsController?.index != 0 || _percentage == 0) {
      return 62.w;
    }
    return 74.w;
  }

  Widget getHeroMatchPhotosArea() {
    return PlayersMatchPhotos(isLoading: false);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: GestureDetector(
        onPanUpdate: _handleChangeCard,
        child: GradientContainer(
          useDefault: true,
          customBackground: _buildCustomBackground(),
          normalOpacity: 0,
          hotOpacity: 0,
          coldOpacity: 0,
          child: Stack(
            children: [
              Positioned(
                bottom: 0,
                child: MatchBottomInfo(
                  controller: _slideCardsController,
                  informationDisplayEnabled: _canDoMatch,
                  isLoading: isLoading,
                  player: _userStore.state.user,
                  commonDimensions: commonDimensions,
                  percentage: _percentage,
                  friend: _friend,
                ),
              ),
              SafeArea(
                child: Column(
                  children: [
                    AppHeader(
                      logoSectionLeftWidget: getLeftHeaderButton(),
                      subtitle: _headerSubtitle,
                      title: _headerTitle,
                    ),
                    SizedBox(
                      height: isLoading
                          ? 12.h
                          : 100.h <= 670
                              ? 1.h
                              : 4.h,
                    ),
                    MatchPercentagePresentation(
                      animate: isLoading,
                      informationDisplayEnabled: _canDoMatch,
                      borderColor: _currentDimensionColor,
                      backgroundColor: _slideCardsController != null &&
                              _currentDimensionColor != null
                          ? generateLighterColorHSL(_currentDimensionColor!)
                              .withAlpha(100)
                          : CustomColors.vividTangerine,
                      backgroundColors: _backgroundColors,
                      progress: _progress,
                      percentage: _percentage,
                      smallTextSize: 2.13.h,
                      largeTextSize: 35.sp,
                      size: _getMatchPercentageSize(),
                    ),
                    // test ? getHeroMatchPhotosArea() : MatchBottomInfo(),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
