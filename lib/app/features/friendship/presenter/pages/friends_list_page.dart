import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_triple/flutter_triple.dart';
import 'package:quycky/app/features/friendship/domain/entities/friendship_list_item_entity.dart';
import 'package:quycky/app/features/friendship/domain/entities/friendship_store_entity.dart';
import 'package:quycky/app/features/friendship/presenter/controllers/friendship_controller.dart';
import 'package:quycky/app/features/friendship/presenter/pages/widgets/custom_tab_bar_indicator.dart';
import 'package:quycky/app/features/friendship/presenter/store/friendship_store.dart';
import 'package:quycky/app/features/friendship/presenter/widgets/friendship_item.dart';
import 'package:quycky/app/features/friendship/presenter/widgets/invite_received_item.dart';
import 'package:quycky/app/features/friendship/presenter/widgets/invite_sent_item%20.dart';
import 'package:quycky/app/features/game/presenter/storage/game_storage.dart';
import 'package:quycky/app/services/remote_config/remote_config_store.dart';
import 'package:quycky/app/theme/colors.dart';
import 'package:quycky/app/widgets/app_header.dart';
import 'package:quycky/app/widgets/button.dart';
import 'package:quycky/app/widgets/gradient_container.dart';
import 'package:quycky/core/utils/app_routes.dart';
import 'package:quycky/core/utils/quycky_icons_icons.dart';
import 'package:sizer/sizer.dart';
import 'package:skeletonizer/skeletonizer.dart';

class FriendsListPage extends StatefulWidget {
  final String title;

  const FriendsListPage({super.key, this.title = 'FriendsListPage'});

  @override
  FriendsListPageState createState() => FriendsListPageState();
}

class FriendsListPageState extends State<FriendsListPage>
    with SingleTickerProviderStateMixin {
  late final TabController _tabController;
  final FriendshipController _controller = Modular.get<FriendshipController>();
  final FriendshipStore _store = Modular.get<FriendshipStore>();
  bool canDoMatch = true;

  @override
  void initState() {
    super.initState();
    _verifyIfUserCanDoMatch();
    _tabController = TabController(
      length: 2,
      vsync: this,
    );
    _controller.getAllFriendshipData();
    _setupPopListener();
    _tabController.addListener(() {
      if (mounted) {
        setState(() {});
      }
    });
  }

  void _verifyIfUserCanDoMatch() async {
    final gameStorage = Modular.get<GameStorage>();
    final remoteConfig = Modular.get<RemoteConfigStore>();
    int numberOfGamesPlayed = await gameStorage.getNumberOfGamesPlayed();
    canDoMatch = remoteConfig.state.minimunRoundsToMatch <= numberOfGamesPlayed;
    setState(() {});
  }

  void _handleUpdateInfo() {
    _controller.getAllFriendshipData();
  }

  void _setupPopListener() {
    Modular.to.addListener(_handleUpdateInfo);
  }

  @override
  void dispose() {
    _tabController.dispose();
    Modular.to.removeListener(_handleUpdateInfo);
    // Modular.to.removeListener(listener);
    super.dispose();
  }

  onAccept(int id) => _controller.acceptInvite(id);
  onOpenFriendProfile(int id) =>
      Modular.to.pushNamed(AppRoutes.userFriendProfile(id: id));
  onOpenFriendMatch(String id) =>
      Modular.to.pushNamed(AppRoutes.checkFriendMatch,
          arguments: {'friendId': id, 'isNewFriend': false});
  onCancel(int id) => _controller.rejectInvite(id);

  Widget getPlayMoreToDoMatchsMessage() => SizedBox(
        height: 10.5.h,
        width: 72.82.w,
        child: Container(
          padding: EdgeInsets.symmetric(vertical: 2.6.h),
          child: RichText(
              textAlign: TextAlign.center,
              text: TextSpan(
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  height: 1.4,
                  fontFamily: 'Roboto',
                ),
                children: <TextSpan>[
                  TextSpan(text: 'Play more to unlock '),
                  TextSpan(
                    text: 'VIBE CHECK\n',
                    style: TextStyle(
                        fontFamily: 'Roboto', fontWeight: FontWeight.bold),
                  ),
                  TextSpan(text: 'and compare profiles with your friends'),
                ],
              )),
        ),
      );
  Widget getClickIconToMatchMessage() => SizedBox(
        // color: Colors.amber,
        height: 10.5.h,
        width: 72.82.w,
        child: Container(
          padding: EdgeInsets.symmetric(vertical: 2.6.h),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'Click',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      height: 1.4,
                      fontFamily: 'Roboto',
                    ),
                  ),
                  Padding(
                      padding: EdgeInsets.symmetric(horizontal: 2.w),
                      child: Icon(QuyckyIcons.heart_search,
                          size: 2.13.h, color: Colors.white)),
                  Text(
                    'VIBE CHECK',
                    style: TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        height: 1.4,
                        fontFamily: 'Roboto',
                        fontWeight: FontWeight.bold),
                  ),
                ],
              ),
              SizedBox(
                height: 0.5.h,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'and compare profiles with your friends',
                    style: TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        height: 1.4,
                        fontFamily: 'Roboto',
                        fontWeight: FontWeight.bold),
                  ),
                ],
              )
            ],
          ),
        ),
      );

  Widget getNoFriendshipData() => SizedBox(
        height: 4.5.h,
        width: 72.82.w,
        child: Container(
          margin: EdgeInsets.only(top: 27.34.h),
          child: RichText(
              textAlign: TextAlign.center,
              text: TextSpan(
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  height: 1.4,
                  fontFamily: 'Roboto',
                ),
                children: <TextSpan>[
                  TextSpan(text: 'Add friends and play more to unlock\n'),
                  TextSpan(
                    text: 'VIBE CHECK',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  TextSpan(text: ' and compare profiles with\nyour friends.'),
                ],
              )),
        ),
      );

  Widget getNoFriendshipInvitesData() => SizedBox(
        height: 4.5.h,
        width: 72.82.w,
        child: Container(
          margin: EdgeInsets.only(top: 27.34.h),
          child: RichText(
              textAlign: TextAlign.center,
              text: TextSpan(
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  height: 1.4,
                  fontFamily: 'Roboto',
                ),
                children: <TextSpan>[
                  TextSpan(
                      text:
                          'You have no invites right now. Check back\nlater or invite your friends to join the fun!'),
                ],
              )),
        ),
      );

  Widget getSkeletonItem() {
    return Skeletonizer(
        enabled: true,
        child: Container(
          height: 10.66.h,
          width: 89.18.w,
          decoration: const BoxDecoration(
            color: CustomColors.orangeSoda30,
            borderRadius: BorderRadius.all(Radius.circular(25)),
          ),
          child: ListTile(
            leading: Icon(Icons.ac_unit, size: 5.h),
            title: Text('The title goes here'),
            subtitle: Text('Subtitle here'),
            trailing: Icon(Icons.ac_unit, size: 40),
          ),
        ));
  }

  Widget getListItem(FriendshipListItem item) {
    if (item.type == EFriendshipListItem.friendship) {
      return FriendshipItem(
          friend: item,
          isMatchButtonEnabled: canDoMatch,
          onOpenFriendProfile: onOpenFriendProfile,
          onOpenFriendMatch: onOpenFriendMatch);
    }
    if (item.type == EFriendshipListItem.inviteSent) {
      return InviteSentItem(
        friend: item,
        onCancel: onCancel,
      );
    }
    return InviteReceivedItem(
      friend: item,
      onAccept: onAccept,
      onCancel: onCancel,
    );
  }

  Widget getSkeletonItems() {
    return Container(
      padding: EdgeInsets.only(top: 3.64.h, right: 3.7.w, left: 3.7.w),
      constraints: BoxConstraints(maxWidth: 87.18.w),
      child: ListView.builder(
        padding: const EdgeInsets.symmetric(horizontal: 8),
        itemCount: 5,
        itemBuilder: (BuildContext context, int index) {
          return Padding(
            padding: const EdgeInsets.only(bottom: 14.0),
            child: getSkeletonItem(),
          );
        },
      ),
    );
  }

  Widget getFriendsList(List<FriendshipListItem> friendshipListItems) {
    if (friendshipListItems.isEmpty) {
      return getNoFriendshipData();
    }

    List<Widget> items = [
      getInfoPerTab(),
      ...friendshipListItems.map((item) {
        return Padding(
          padding: const EdgeInsets.only(bottom: 14.0),
          child: getListItem(item),
        );
      })
    ];

    return Container(
      padding: EdgeInsets.only(top: 0.h, right: 3.7.w, left: 3.7.w),
      constraints: BoxConstraints(maxWidth: 87.18.w),
      child: ListView(
        padding: const EdgeInsets.symmetric(horizontal: 8),
        children: items,
      ),
    );
  }

  Widget getInvitesList(FriendshipStoreEntity state) {
    if (state.friendshipInviteReceivedListItems.isEmpty &&
        state.friendshipInviteSentListItems.isEmpty) {
      return getNoFriendshipInvitesData();
    }
    final children = <Widget>[];
    if (state.friendshipInviteReceivedListItems.isNotEmpty) {
      children.add(
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.only(bottom: 1.77.h),
              child: Text(
                'FRIEND INVITES (${state.friendshipInviteReceivedListItems.length})',
                style: TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.bold),
              ),
            ),
            ...state.friendshipInviteReceivedListItems.map((item) => Padding(
                  padding: const EdgeInsets.only(bottom: 14.0),
                  child: getListItem(item),
                ))
          ],
        ),
      );
    }
    if (state.friendshipInviteSentListItems.isNotEmpty) {
      children.add(
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.only(bottom: 1.77.h),
              child: Text(
                'INVITES SENT (${state.friendshipInviteSentListItems.length})',
                textAlign: TextAlign.left,
                style: TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.bold),
              ),
            ),
            ...state.friendshipInviteSentListItems.map((item) => Padding(
                  padding: EdgeInsets.only(bottom: 1.18.h),
                  child: getListItem(item),
                ))
          ],
        ),
      );
    }
    return Container(
      padding: EdgeInsets.only(top: 3.64.h, right: 3.7.w, left: 3.7.w),
      constraints: BoxConstraints(maxWidth: 87.18.w),
      child: ListView.builder(
        padding: const EdgeInsets.symmetric(horizontal: 8),
        // Let the ListView know how many items it needs to build.
        itemCount: children.length,
        // Provide a builder function. This is where the magic happens.
        // Convert each item into a widget based on the type of item it is.
        itemBuilder: (BuildContext context, int index) {
          final item = children[index];
          return Padding(padding: EdgeInsets.only(bottom: 2.36.h), child: item);
        },
      ),
    );
  }

  handleBack() => Modular.to.pop();

  handleOpenQrCodePage() => Modular.to.pushNamed(AppRoutes.profileQrCodeShare);

  Widget getTabItem(String label,
      {EdgeInsetsGeometry? tabPadding, hasBadge = false}) {
    final textWidget = Row(
      children: [
        Icon(
          Icons.people_outline,
          size: 5.29.w,
        ),
        Text(label)
      ],
    );
    final tab = Tab(
        child: FittedBox(
            child: hasBadge
                ? Badge(
                    alignment: Alignment.topCenter,
                    padding: const EdgeInsets.only(bottom: 15),
                    child: textWidget,
                  )
                : textWidget)
        // text: label,
        );
    return tabPadding != null ? Padding(padding: tabPadding, child: tab) : tab;
  }

  Widget getTabBarWidget() {
    final badge = _controller.hasReceivedInvitesPendent()
        ? const Badge(
            largeSize: 12,
            smallSize: 12,
            backgroundColor: Colors.red,
          )
        : const SizedBox();
    return Stack(
      alignment: Alignment.center,
      children: [
        const SizedBox(height: 42),
        Container(
          padding: const EdgeInsets.symmetric(vertical: 8),
          height: 4.5.h,
          constraints: const BoxConstraints(maxWidth: 285),
          child: TabBar(
            controller: _tabController,
            indicator: CustomTabBarIndicator(
              color: CustomColors.primary,
              height: 4.5.h,
              horizontalPadding: 1,
              borderWidth: 1,
            ),
            padding: EdgeInsets.zero, //indicator
            enableFeedback: true,
            indicatorSize: TabBarIndicatorSize.tab,
            labelPadding: EdgeInsets.zero,
            // isScrollable: true,
            labelStyle: const TextStyle(
                fontFamily: 'Outfit',
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.bold,
                letterSpacing: 3),
            // indicatorWeight: 0,
            dividerHeight: 0,
            indicatorColor: Colors.transparent,
            unselectedLabelColor: Colors.white.withAlpha(152),
            tabAlignment: TabAlignment.fill,
            // dividerColor: Colors.transparent,
            tabs: [
              getTabItem('A',
                  tabPadding: const EdgeInsets.symmetric(horizontal: 8)),
              getTabItem('B',
                  tabPadding: const EdgeInsets.symmetric(horizontal: 8)),
            ],
          ),
        ),
        Positioned(right: 3, top: 0, child: badge),
      ],
    );
  }

  Widget _buildTabButton(
      int index, IconData icon, double iconSize, String label,
      {bool hasBadge = false}) {
    final iconWidget = Icon(
      icon,
      color: Colors.white,
      size: iconSize,
    );
    final iconAndBadge = TripleBuilder(
        store: _store,
        builder: (context, triple) {
          return Container(
              child: hasBadge && _controller.hasReceivedInvitesPendent()
                  ? Stack(
                      children: [
                        iconWidget,
                        Positioned(
                            top: 0,
                            right: 0,
                            child: Badge(
                              largeSize: 12,
                              smallSize: 12,
                              backgroundColor: Colors.red,
                            ))
                      ],
                    )
                  : iconWidget);
        });
    return SizedBox(
      width: 41.54.w,
      height: 4.5.h,
      child: Button(
        autoSized: true,
        color: CustomColors.primary
            .withAlpha(_tabController.index == index ? 255 : 115),
        onPressed: () => _tabController.index = index,
        noAnimation: true,
        text: label,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            iconAndBadge,
            SizedBox(
              width: 2.1.w,
            ),
            Text(
              label,
              style: TextStyle(
                  letterSpacing: 3,
                  fontFamily: "Roboto",
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                  fontSize: 14.sp),
            )
          ],
        ),
      ),
    );
  }

  Widget getTabBar() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        _buildTabButton(0, QuyckyIcons.people_outlined, 18.sp, 'FRIENDS'),
        SizedBox(
          width: 4.1.w,
        ),
        _buildTabButton(1, Icons.mail_outline_rounded, 19.sp, 'INVITES',
            hasBadge: true)
      ],
    );
  }

  Widget getTabBarView(Triple<FriendshipStoreEntity> triple) {
    if (triple.isLoading) {
      return Expanded(
          child: TabBarView(
              controller: _tabController,
              children: [getSkeletonItems(), getSkeletonItems()]));
    }

    Widget friends = getFriendsList(triple.state.friendshipListItems);
    Widget invites = getInvitesList(triple.state);

    final tabBarView =
        TabBarView(controller: _tabController, children: [friends, invites]);
    return Expanded(
      child: tabBarView,
    );
  }

  Widget getInfoPerTab() {
    return canDoMatch
        ? getClickIconToMatchMessage()
        : getPlayMoreToDoMatchsMessage();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: GradientContainer(
        useDefault: true,
        normalOpacity: 0,
        hotOpacity: 0,
        coldOpacity: 0,
        child: SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.max,
            children: [
              AppHeader(
                logoSectionLeftWidget: IconButton(
                    onPressed: handleBack,
                    icon: const Icon(QuyckyIcons.arrow_left_circle,
                        color: Colors.white)),
                logoSectionRightWidget: IconButton(
                    onPressed: handleOpenQrCodePage,
                    icon: const Icon(QuyckyIcons.read_qr_code,
                        color: Colors.white)),
              ),
              getTabBar(),
              TripleBuilder(
                  store: _store,
                  builder: (context, tripleObj) {
                    Triple<FriendshipStoreEntity> triple =
                        tripleObj as Triple<FriendshipStoreEntity>;
                    return getTabBarView(triple);
                  }),
            ],
          ),
        ),
      ),
    );
  }
}
