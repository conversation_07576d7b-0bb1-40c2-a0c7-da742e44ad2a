import 'package:flutter/material.dart';
import 'package:quycky/app/features/user/domain/entities/tag_entity.dart';
import 'package:quycky/app/theme/colors.dart';
import 'package:quycky/app/widgets/custom_image.dart';
import 'package:quycky/core/utils/assets_strings.dart';
import 'package:sizer/sizer.dart';

class DimensionCardItem extends StatelessWidget {
  final TagEntity dimension;
  final int position;
  const DimensionCardItem(
      {super.key, required this.position, required this.dimension});

  String _getCorrectIconName(String iconName) {
    switch (iconName) {
      case 'classic':
        return 'cl';
      case 'knk':
        return 'kin';
      default:
        return iconName;
    }
  }

  Widget _buildIcon(String imageName) {
    return CustomImage(Assets.svg.iq[imageName],
        width: 10.w, height: 10.w, fit: BoxFit.fitHeight);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 6.27.h,
      width: 87.20.w,
      decoration: BoxDecoration(
        color: CustomColors.spanishPink.withValues(alpha: 0.4),
        borderRadius: BorderRadius.circular(45),
      ),
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 4.61.w),
        child: Row(
          children: [
            _buildIcon(
              _getCorrectIconName(dimension.icon),
            ),
            SizedBox(
              width: 5.94.w,
            ),
            Text(dimension.title,
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontFamily: "Roboto",
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                  letterSpacing: 1.5,
                  fontSize: 14,
                )),
            Spacer(),
            Text("$positionº",
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontFamily: "Roboto",
                  fontWeight: FontWeight.w900,
                  color: Colors.white,
                  letterSpacing: 0.5,
                  fontSize: 18,
                ))
          ],
        ),
      ),
    );
  }
}
