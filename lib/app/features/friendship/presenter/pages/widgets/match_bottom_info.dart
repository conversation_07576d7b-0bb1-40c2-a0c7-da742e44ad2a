import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:quycky/analytics.dart';
import 'package:quycky/app/features/friendship/presenter/pages/widgets/dimension_card_item.dart';
import 'package:quycky/app/features/friendship/presenter/pages/widgets/players_match_photos.dart';
import 'package:quycky/app/features/user/domain/entities/tag_entity.dart';
import 'package:quycky/app/features/user/domain/entities/user_entity.dart';
import 'package:quycky/app/theme/colors.dart';
import 'package:quycky/app/widgets/button.dart';
import 'package:quycky/app/widgets/dots.dart';
import 'package:quycky/core/utils/app_routes.dart';
import 'package:sizer/sizer.dart';

class MatchBottomInfo extends StatefulWidget {
  final bool isLoading;
  final bool informationDisplayEnabled;
  final int percentage;
  final UserEntity player;
  final UserEntity friend;
  final List<TagEntity> commonDimensions;
  final TabController? controller;

  const MatchBottomInfo(
      {super.key,
      this.informationDisplayEnabled = true,
      this.controller,
      required this.percentage,
      this.isLoading = false,
      this.commonDimensions = const [],
      required this.player,
      required this.friend});

  @override
  State<MatchBottomInfo> createState() => _MatchBottomInfoState();
}

class _MatchBottomInfoState extends State<MatchBottomInfo>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<Offset> _slideAnimation;
  late final TabController _localController;
  int lastTimeWhenCardSlide = 0;
  TabController get controller => widget.controller ?? _localController;
  String get vibeMessage {
    if (widget.percentage <= 25) {
      return 'BAD VIBES';
    } else if (widget.percentage >= 26 && widget.percentage <= 50) {
      return 'AVERAGE VIBES';
    } else if (widget.percentage >= 51 && widget.percentage <= 80) {
      return 'GOOD VIBES';
    } else if (widget.percentage >= 81 && widget.percentage <= 100) {
      return 'GREAT VIBES';
    } else {
      return '';
    }
  }

  Color get _currentColorInfo => controller.index < 2
      ? CustomColors.salmon_4
      : widget.commonDimensions[controller.index - 2].color;

  EdgeInsets get playersMatchEdge {
    return widget.isLoading
        ? EdgeInsets.only(bottom: 3.9.h)
        : EdgeInsets.only(
            top: widget.percentage == 0
                ? 10.5.h
                : controller.index == 0
                    ? 13.h
                    : controller.index == 1
                        ? (100.h <= 670 ? 12.5.h : 10.5.h)
                        : 11.8.h);
  }
  // double get playersMatchPhotosCardHeight => widget.isLoading
  //     ? 25.h
  //     : controller.index == 0
  //         ? 35.h
  //         : 45.h;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    _localController = TabController(length: 2, vsync: this);
  }

  @override
  void didUpdateWidget(covariant MatchBottomInfo oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.isLoading != widget.isLoading) {
      if (widget.isLoading) {
        _animationController.reverse();
      } else {
        _animationController.forward();
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    _localController.dispose();
    super.dispose();
  }

  void _changeCardIndex(int index) {
    if (index < 0 || index >= controller.length) {
      return;
    }
    controller.index = index;
    setState(() {});
  }

  void _handleChangeCard(DragUpdateDetails details) {
    if (widget.isLoading) {
      return;
    }
    final currentTime = DateTime.now().millisecondsSinceEpoch;
    if ((currentTime - lastTimeWhenCardSlide) < 500) {
      return;
    }
    lastTimeWhenCardSlide = currentTime;
    if (details.delta.dx > 0 && controller.index > 0) {
      _changeCardIndex(controller.index - 1);
      print('left-right');
      return;
    }
    if (details.delta.dx < 0 && controller.index < controller.length) {
      _changeCardIndex(controller.index + 1);
      print('right-left');
    }
  }

  void handleGoTo(String route,
      {bool replaceRoute = false, Object? arguments}) {
    if (replaceRoute) {
      Modular.to.pushReplacementNamed(route, arguments: arguments);
      return;
    }
    Modular.to.pushNamed(route);
  }

  Future<void> _logFriendInviteEvent() async {
    Analytics.instance.logEvent(name: 'invite_friend_from_vibe_check');
  }

  void _goToLobby() {
    handleGoTo(AppRoutes.gameLobby(), replaceRoute: true);
  }

  void _goToInviteFriendToAGame() {
    _logFriendInviteEvent();
    handleGoTo(AppRoutes.gameLobby(), replaceRoute: true, arguments: {
      'friend': widget.friend,
    });
  }

  List<Widget> _getFirstCardItems() {
    return [
      Text(
        vibeMessage,
        textAlign: TextAlign.center,
        style: TextStyle(
          fontFamily: "Roboto",
          fontWeight: FontWeight.w800,
          color: Colors.white,
          letterSpacing: 1.5,
          fontSize: 18,
        ),
      ),
      Spacer(),
      SizedBox(
        height: 6.h,
        width: 83.34.w,
        child: Button(
          autoSized: true,
          borderColor: Colors.white,
          outlined: true,
          onPressed: () {
            if (widget.controller == null) return;
            widget.controller!.index = 1;
          },
          child: Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: const [
              Text(
                "SWIPE FOR DETAILS",
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              SizedBox(width: 10),
              AnimatedArrow()
            ],
          ),
        ),
      ),
      const SizedBox(height: 10),
    ];
  }

  Widget _getTopDimensions() {
    List<Widget> topDimensions = widget.commonDimensions
        .take(3)
        .map((dimension) => Padding(
              padding: EdgeInsets.only(top: 0.95.h),
              child: DimensionCardItem(
                position: widget.commonDimensions.indexOf(dimension) + 1,
                dimension: dimension,
              ),
            ))
        .toList();
    return SizedBox(
      height: 21.8.h,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: topDimensions,
      ),
    );
  }

  List<Widget> _getSecondCardItems() {
    return [
      const Text(
        'TOP SHARED DIMENSIONS',
        textAlign: TextAlign.center,
        style: TextStyle(
          fontFamily: "Roboto",
          fontWeight: FontWeight.w800,
          color: Colors.white,
          letterSpacing: 1.5,
          fontSize: 18,
        ),
      ),
      Spacer(),
      SizedBox(height: 100.h <= 670 ? 11.5.h : 0.h),
      _getTopDimensions(),
      const SizedBox(height: 10),
    ];
  }

  Widget _getPlayButton() => SizedBox(
        height: 6.04.h,
        width: 83.4.w,
        child: Button(
          borderColor: Colors.transparent,
          onPressed: _goToLobby,
          autoSized: true,
          text: 'PLAY QUYCKY',
        ),
      );

  Widget _getInviteToPlayButton() => SizedBox(
        height: 6.04.h,
        width: 83.4.w,
        child: Button(
          borderColor: Colors.white,
          outlined: true,
          onPressed: _goToInviteFriendToAGame,
          autoSized: true,
          text: 'INVITE TO PLAY',
        ),
      );

  List<Widget> _getZeroPercentVibeItems() {
    return [
      Text(
        vibeMessage,
        textAlign: TextAlign.center,
        style: TextStyle(
          fontFamily: "Roboto",
          fontWeight: FontWeight.w800,
          color: Colors.white,
          letterSpacing: 1.5,
          fontSize: 18,
        ),
      ),
      Spacer(),
      _getPlayMoreToDoMatchsMessage(),
      Spacer(),
      _getInviteToPlayButton(),
      Spacer(),
    ];
  }

  List<Widget> _getDimensionDetailsCardItems() {
    final dimension = widget.commonDimensions[controller.index - 2];
    return [
      Text(
        dimension.title.toUpperCase(),
        textAlign: TextAlign.center,
        style: TextStyle(
          fontFamily: "Roboto",
          fontWeight: FontWeight.w800,
          color: Colors.white,
          letterSpacing: 1.5,
          fontSize: 18,
        ),
      ),
      Spacer(),
      Container(
        padding: EdgeInsets.only(top: 13.h),
        constraints: BoxConstraints(maxWidth: 94.w),
        child: Text(
          dimension.vibecheckDescription,
          textAlign: TextAlign.center,
          style: TextStyle(
            fontFamily: "Roboto",
            color: Colors.white,
            letterSpacing: 0.5,
            fontSize: 14,
          ),
          maxLines: 3,
          overflow: TextOverflow.ellipsis,
        ),
      ),
      Spacer(),
      _getInviteToPlayButton(),
      SizedBox(height: 3.h),
    ];
  }

  List<Widget> _getPlayMoreToUnlock() {
    return [
      Text(
        'PLAY TO UNLOCK',
        textAlign: TextAlign.center,
        style: TextStyle(
          fontFamily: "Roboto",
          fontWeight: FontWeight.w800,
          color: Colors.white,
          letterSpacing: 1.5,
          fontSize: 18,
        ),
      ),
      Spacer(),
      Container(
        padding: EdgeInsets.only(top: 12.h),
        constraints: BoxConstraints(maxWidth: 94.w),
        child: RichText(
            textAlign: TextAlign.center,
            text: TextSpan(
              style: TextStyle(
                color: Colors.white,
                fontSize: 14,
                height: 1.4,
                fontFamily: 'Roboto',
              ),
              children: <TextSpan>[
                TextSpan(text: 'Play more to unlock '),
                TextSpan(
                  text: 'VIBE CHECK\n',
                  style: TextStyle(
                      fontFamily: 'Roboto', fontWeight: FontWeight.bold),
                ),
                TextSpan(text: 'and compare profiles with your friends'),
              ],
            )),
      ),
      Spacer(),
      _getPlayButton(),
      SizedBox(height: 3.h),
    ];
  }

  Widget _getPlayMoreToDoMatchsMessage() => Container(
        margin: EdgeInsets.only(top: 11.h),
        height: 5.7.h,
        width: 72.82.w,
        child: RichText(
            textAlign: TextAlign.center,
            text: TextSpan(
              style: TextStyle(
                color: Colors.white,
                fontSize: 14,
                height: 1.4,
                fontFamily: 'Roboto',
              ),
              children: <TextSpan>[
                TextSpan(text: 'Play more to unlock '),
                TextSpan(
                  text: 'DIMENSIONS\n',
                  style: TextStyle(
                      fontFamily: 'Roboto', fontWeight: FontWeight.bold),
                ),
                TextSpan(text: 'and compare profiles with your friends'),
              ],
            )),
      );

  List<Widget> _getCurrentItems() {
    switch (controller.index) {
      case 0:
        return _getFirstCardItems();
      case 1:
        return _getSecondCardItems();
      default:
        return _getDimensionDetailsCardItems();
    }
  }

  Widget _getMinifiedCard(List<Widget> children) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      padding: const EdgeInsets.symmetric(vertical: 20),
      decoration: BoxDecoration(
          color: CustomColors.salmon_4,
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20), topRight: Radius.circular(20))),
      height: 32.h,
      width: 100.w,
      child: Column(mainAxisSize: MainAxisSize.max, children: children),
    );
  }

  Widget _getNormalCard(List<Widget> children) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      padding: const EdgeInsets.symmetric(vertical: 20),
      decoration: BoxDecoration(
          color: _currentColorInfo,
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20), topRight: Radius.circular(20))),
      height: 100.h <= 670 ? 45.h : 43.h,
      width: 100.w,
      child: Column(mainAxisSize: MainAxisSize.max, children: children),
    );
  }

  Widget _getZeroPercentVibeCard() {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      padding: const EdgeInsets.symmetric(vertical: 20),
      decoration: BoxDecoration(
          color: CustomColors.salmon_4,
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20), topRight: Radius.circular(20))),
      height: 36.4.h,
      width: 100.w,
      child: Column(
          mainAxisSize: MainAxisSize.max, children: _getZeroPercentVibeItems()),
    );
  }

  Widget _getNoDataCard() {
    return _getNormalCard(_getPlayMoreToUnlock());
  }

  Widget _getCurrentCard() {
    if (widget.percentage == 0) {
      return _getZeroPercentVibeCard();
    }

    if (controller.index == 0) {
      return _getMinifiedCard(_getCurrentItems());
    }
    return _getNormalCard(_getCurrentItems());
  }

  Widget _getDots() {
    Widget dots = widget.percentage > 0 || widget.isLoading
        ? Padding(
            padding: EdgeInsets.only(bottom: 0.2.h),
            child: Dots(
                selectedColor: _currentColorInfo,
                value: controller.index,
                itemsCount: controller.length))
        : SizedBox(height: 4.h);
    return dots;
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        SlideTransition(
          position: _slideAnimation,
          child: IgnorePointer(
            ignoring: widget.isLoading,
            child: Column(
              children: [
                _getDots(),
                widget.informationDisplayEnabled
                    ? _getCurrentCard()
                    : _getNoDataCard(),
              ],
            ),
          ),
        ),
        Positioned(
          bottom: widget.isLoading ? 0 : null,
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            // color: Colors.amber,
            width: 100.w,
            // height: playersMatchPhotosCardHeight,
            padding: playersMatchEdge,
            child: Row(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                PlayersMatchPhotos(
                    color: widget.isLoading
                        ? CustomColors.vividTangerine
                        : Colors.white.withAlpha(50),
                    isLoading: widget.isLoading,
                    playerPhoto: widget.player.avatarUrl,
                    friendPhoto: widget.friend.avatarUrl),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

class AnimatedArrow extends StatefulWidget {
  const AnimatedArrow({super.key});

  @override
  State<AnimatedArrow> createState() => _AnimatedArrowState();
}

class _AnimatedArrowState extends State<AnimatedArrow>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 1),
    )..repeat(reverse: true);

    _animation = Tween<double>(begin: 0, end: 1).animate(_controller);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 30,
      child: Stack(
        alignment: Alignment.centerLeft, // Alinha o ícone à esquerda
        children: [
          AnimatedBuilder(
            animation: _animation,
            builder: (context, child) {
              return Transform.translate(
                offset: Offset(_animation.value * 10, 0), // Movimenta o ícone
                child: child,
              );
            },
            child: const Icon(
              Icons.arrow_forward,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }
}
