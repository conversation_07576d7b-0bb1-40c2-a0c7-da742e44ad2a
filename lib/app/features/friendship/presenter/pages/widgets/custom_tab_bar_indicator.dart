import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

class CustomTabBarIndicator extends Decoration {
  final Color color;
  final double? height;
  final double? horizontalPadding;
  final double borderWidth;

  const CustomTabBarIndicator({
    required this.color,
    this.height,
    this.horizontalPadding,
    this.borderWidth = 1.0,
  });

  @override
  BoxPainter createBoxPainter([VoidCallback? onChanged]) {
    return _CustomTabBarIndicatorPainter(this, onChanged);
  }
}

class _CustomTabBarIndicatorPainter extends BoxPainter {
  late final horizontalPadding;
  late final double height;
  final CustomTabBarIndicator decoration;

  _CustomTabBarIndicatorPainter(this.decoration, VoidCallback? onChanged) {
    height = decoration.height ?? 4.5.h;
    horizontalPadding = decoration.horizontalPadding ?? 3.84.w;
  }

  @override
  void paint(Canvas canvas, Offset offset, ImageConfiguration configuration) {
    final double verticalPadding = (configuration.size!.height - height) / 2;

    final Rect rect = Rect.fromLTWH(
      offset.dx + horizontalPadding,
      offset.dy + verticalPadding,
      configuration.size!.width - (horizontalPadding * 2),
      height,
    );

    final Paint paint = Paint()
      ..color = decoration.color
      ..style = PaintingStyle.stroke
      ..strokeWidth = decoration.borderWidth;

    final RRect rrect = RRect.fromRectAndRadius(
      rect,
      Radius.circular(rect.height / 2), // Formato de pílula
    );
    canvas.drawRRect(rrect, paint);
  }
}
