import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

class PercentageText extends StatefulWidget {
  final bool animate;
  final String text;

  const PercentageText({super.key, required this.animate, required this.text});

  @override
  _PercentageTextState createState() => _PercentageTextState();
}

class _PercentageTextState extends State<PercentageText> {
  late String displayedText;
  Timer? _timer;
  final Random _random = Random();

  @override
  void initState() {
    super.initState();
    displayedText = widget.text;
    if (widget.animate) {
      _startAnimation();
    }
  }

  @override
  void didUpdateWidget(covariant PercentageText oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.animate != oldWidget.animate) {
      if (widget.animate) {
        _startAnimation();
      } else {
        _stopAnimation();
        setState(() {
          displayedText = widget.text;
        });
      }
    }
  }

  void _startAnimation() {
    _timer = Timer.periodic(Duration(milliseconds: 500), (timer) {
      setState(() {
        displayedText = '${_random.nextInt(100)}%';
      });
    });
  }

  void _stopAnimation() {
    _timer?.cancel();
  }

  @override
  void dispose() {
    _stopAnimation();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Text(
      displayedText,
      style: TextStyle(
          fontFamily: 'AlbertSans',
          color: Colors.white,
          fontWeight: FontWeight.w800,
          fontSize: 45.sp,
          letterSpacing: .5,
          decoration: TextDecoration.none),
    );
  }
}
