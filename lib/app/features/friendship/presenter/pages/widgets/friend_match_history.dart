import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:quycky/app/features/friendship/domain/entities/friend_match_entity.dart';
import 'package:quycky/app/features/friendship/presenter/controllers/friendship_controller.dart';
import 'package:quycky/app/theme/colors.dart';
import 'package:quycky/app/widgets/avatar.dart';
import 'package:quycky/core/utils/app_routes.dart';
import 'package:sizer/sizer.dart';

class FriendMatchHistory extends StatefulWidget {
  final String title;

  const FriendMatchHistory({super.key, this.title = 'FriendMatchHistory'});

  @override
  FriendMatchHistoryState createState() => FriendMatchHistoryState();
}

class FriendMatchHistoryState extends State<FriendMatchHistory> {
  final FriendshipController _controller = Modular.get<FriendshipController>();
  bool _isLoading = false;
  List<FriendMatchEntity> history = [];

  @override
  initState() {
    super.initState();
    _getHistory();
  }

  set isLoading(bool value) {
    _isLoading = value;
    setState(() {});
  }

  void _getHistory() async {
    isLoading = true;
    history = await _controller.getFriendMatchHistory();
    isLoading = false;
  }

  @override
  void dispose() {
    super.dispose();
  }

  int getMatchPercentInt(double matchPercent) {
    return (matchPercent * 100).toInt();
  }

  void _handleOpenFriendMatchDetail(String id) =>
      Modular.to.pushNamed(AppRoutes.checkFriendMatch,
          arguments: {'friendId': id, 'isNewFriend': false});

  Widget getItem(FriendMatchEntity item) {
    return GestureDetector(
      onTap: () => _handleOpenFriendMatchDetail(item.friend.id),
      child: Container(
        height: 10.65.h,
        width: 87.17.w,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(25),
        ),
        padding: EdgeInsets.symmetric(horizontal: 5.64.w),
        child: Row(
          children: [
            Avatar(
              size: 6.75.h,
              addPhotoButton: false,
              imagePath: item.friend.avatarUrl,
            ),
            SizedBox(
              width: 4.1.w,
            ),
            Text(
              item.friend.name,
              style: TextStyle(
                  color: CustomColors.primary,
                  fontSize: 14,
                  letterSpacing: 1,
                  fontFamily: 'Roboto',
                  fontWeight: FontWeight.bold),
            ),
            Spacer(),
            Text(
              '${getMatchPercentInt(item.matchPercent)}%',
              style: TextStyle(
                  color: CustomColors.primary,
                  fontSize: 16,
                  letterSpacing: 0.5,
                  fontFamily: 'Roboto',
                  fontWeight: FontWeight.w900),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTopText() {
    return Column(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            'TOP VIBES',
            style: TextStyle(
                letterSpacing: 1,
                fontFamily: "Roboto",
                fontWeight: FontWeight.bold,
                color: CustomColors.americanPurple2,
                fontSize: 16),
          ),
          SizedBox(
            height: 1.54.h,
          ),
          RichText(
            textAlign: TextAlign.center,
            text: TextSpan(
              text: 'CHECK VIBES ',
              style: TextStyle(
                  letterSpacing: 0.25,
                  fontFamily: "Roboto",
                  fontWeight: FontWeight.w900,
                  color: CustomColors.americanPurple2,
                  fontSize: 13),
              children: <TextSpan>[
                TextSpan(
                    text:
                        'with friends to see your\nbest connections displayed here.',
                    style: TextStyle(
                        letterSpacing: 0.25,
                        fontFamily: "Roboto",
                        height: 1.2,
                        fontWeight: FontWeight.w400,
                        color: CustomColors.americanPurple2,
                        fontSize: 13)),
              ],
            ),
          ),
        ]);
  }

  Widget _buildSkeletonItem() {
    return Container(
      height: 10.65.h,
      width: 87.17.w,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(25),
      ),
      padding: EdgeInsets.symmetric(horizontal: 5.64.w),
      child: Row(
        children: [
          Container(
            height: 6.75.h,
            width: 6.75.h,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(50),
            ),
          ),
          SizedBox(
            width: 4.1.w,
          ),
          Container(
            height: 14,
            width: 50,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(50),
            ),
          ),
          Spacer(),
          Container(
            height: 16,
            width: 16,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(50),
            ),
          ),
        ],
      ),
    );
  }

  List<Widget> _buildSkeletonItems() {
    return [
      Padding(
          padding: EdgeInsets.only(top: 1.18.h), child: _buildSkeletonItem()),
      Padding(
          padding: EdgeInsets.only(top: 1.18.h), child: _buildSkeletonItem()),
      Padding(
          padding: EdgeInsets.only(top: 1.18.h), child: _buildSkeletonItem())
    ];
  }

  Widget _buildBodyWidget() {
    final ret = Column(
      children: _isLoading
          ? _buildSkeletonItems()
          : history
              .map(
                (FriendMatchEntity item) => Padding(
                    padding: EdgeInsets.only(top: 1.18.h),
                    child: getItem(item)),
              )
              .toList(),
    );
    return ret;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _buildTopText(),
        SizedBox(
          height: 0.7.h,
        ),
        _buildBodyWidget(),
      ],
    );
  }
}
