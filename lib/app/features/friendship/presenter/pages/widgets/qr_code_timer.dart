import 'dart:async';

import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

class QrCodeTimer extends StatefulWidget {
  final DateTime startTime;
  final int secondsUntilNextCode;
  final void Function() onEndTime;
  const QrCodeTimer(
      {super.key,
      this.secondsUntilNextCode = 60,
      required this.startTime,
      required this.onEndTime});

  @override
  State<QrCodeTimer> createState() => _QrCodeTimerState();
}

class _QrCodeTimerState extends State<QrCodeTimer> {
  Duration _currentTimer = Duration(seconds: 0);
  late Timer _countdownTimer;

  set currentTimer(Duration value) {
    setState(() {
      _currentTimer = value;
    });
  }

  @override
  void initState() {
    super.initState();
    startTimer();
  }

  @override
  void dispose() {
    super.dispose();
    _countdownTimer.cancel();
  }

  String formattedDigits(int n) => n.toString().padLeft(2, '0');

  String getCurrentTimer() {
    final minutes = formattedDigits(_currentTimer.inMinutes.remainder(60));
    final seconds = formattedDigits(_currentTimer.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }

  void startTimer() {
    if (widget.secondsUntilNextCode == 0) {
      return;
    }
    final timeDifference = DateTime.now().difference(widget.startTime);
    if (timeDifference.inSeconds >= widget.secondsUntilNextCode) {
      widget.onEndTime();
      return;
    }
    int remainderSeconds =
        widget.secondsUntilNextCode - timeDifference.inSeconds;
    _currentTimer = Duration(seconds: remainderSeconds);
    _countdownTimer =
        Timer.periodic(const Duration(seconds: 1), (_) => setCountDown());
  }

  void stopTimer() {
    _countdownTimer.cancel();
  }

  void resetTimer() {
    stopTimer();
  }

  void setCountDown() {
    final seconds = _currentTimer.inSeconds - 1;
    if (seconds == 0) {
      stopTimer();
      widget.onEndTime();
    }
    currentTimer = Duration(seconds: seconds);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Text(
          'NEW CODE IN:',
          style: TextStyle(
              letterSpacing: 1,
              color: Colors.white,
              fontFamily: 'Roboto',
              fontSize: 10.sp,
              fontWeight: FontWeight.bold),
        ),
        SizedBox(height: 0.1.h),
        Text(
          getCurrentTimer(),
          style: TextStyle(
              letterSpacing: 1,
              color: Colors.white,
              fontFamily: 'Roboto',
              fontSize: 14.sp,
              fontWeight: FontWeight.bold),
        ),
      ],
    );
  }
}
