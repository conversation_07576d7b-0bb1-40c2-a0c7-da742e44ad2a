import 'package:flutter/material.dart';
import 'package:quycky/app/widgets/avatar.dart';
import 'package:quycky/app/widgets/bouncing_dots.dart';
import 'package:quycky/core/utils/quycky_icons_icons.dart';
import 'package:sizer/sizer.dart';

class PlayersMatchPhotos extends StatefulWidget {
  final bool isLoading;
  final Color color;
  final String? playerPhoto;
  final String? friendPhoto;

  const PlayersMatchPhotos({
    super.key,
    this.isLoading = true,
    this.playerPhoto,
    this.friendPhoto,
    this.color = Colors.white,
  });

  @override
  _PlayersMatchPhotosState createState() => _PlayersMatchPhotosState();
}

class _PlayersMatchPhotosState extends State<PlayersMatchPhotos> {
  String? _playerPhoto;
  String? _friendPhoto;

  @override
  void initState() {
    super.initState();
    _playerPhoto = widget.playerPhoto;
    _friendPhoto = widget.friendPhoto;
  }

  @override
  void didUpdateWidget(covariant PlayersMatchPhotos oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Atualiza apenas se a URL mudar
    if (widget.playerPhoto != oldWidget.playerPhoto &&
        widget.playerPhoto != _playerPhoto) {
      _playerPhoto = widget.playerPhoto;
    }
    if (widget.friendPhoto != oldWidget.friendPhoto &&
        widget.friendPhoto != _friendPhoto) {
      _friendPhoto = widget.friendPhoto;
    }
  }

  Widget getBetweenPlayersAvatarsWidget() {
    if (widget.isLoading) {
      return const BouncingDots();
    }
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 3.w),
      child: Icon(
        QuyckyIcons.heart_search,
        color: Colors.white.withAlpha(120),
        size: 7.54.w,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 10.9.h,
      width: 61.53.w,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(148),
        color: widget.color,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          Avatar(
            size: 8.h,
            addPhotoButton: false,
            imagePath: _playerPhoto,
          ),
          getBetweenPlayersAvatarsWidget(),
          Avatar(
            size: 8.h,
            addPhotoButton: false,
            imagePath: _friendPhoto,
          ),
        ],
      ),
    );
  }
}
