import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:loading_indicator/loading_indicator.dart';
import 'package:qr_code_scanner_plus/qr_code_scanner_plus.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:quycky/analytics.dart';
import 'package:quycky/app/features/friendship/domain/entities/profile_temp_code_entity.dart';
import 'package:quycky/app/features/friendship/presenter/controllers/friendship_controller.dart';
import 'package:quycky/app/features/friendship/presenter/pages/widgets/qr_code_timer.dart';
import 'package:quycky/app/features/user/presenter/store/user_store.dart';
import 'package:quycky/app/services/remote_config/remote_config_store.dart';
import 'package:quycky/app/theme/colors.dart';
import 'package:quycky/app/widgets/app_header.dart';
import 'package:quycky/app/widgets/avatar.dart';
import 'package:quycky/app/widgets/button.dart';
import 'package:quycky/app/widgets/gradient_container.dart';
import 'package:quycky/app/widgets/quycky_modal_progress.dart';
import 'package:quycky/core/utils/app_routes.dart';
import 'package:quycky/core/utils/quycky_icons_icons.dart';
import 'package:quycky/core/utils/show_message.dart';
import 'package:share_plus/share_plus.dart';
import 'package:sizer/sizer.dart';

class ShareQrCodePage extends StatefulWidget {
  const ShareQrCodePage({super.key});

  @override
  State<ShareQrCodePage> createState() => _ShareQrCodePageState();
}

class _ShareQrCodePageState extends State<ShareQrCodePage>
    with SingleTickerProviderStateMixin {
  bool _isLoading = false;
  bool _isTryingToAddFriend = false;
  final _userStore = Modular.get<UserStore>();
  final _controller = Modular.get<FriendshipController>();
  final _remoteConfig = Modular.get<RemoteConfigStore>();
  int _currentTabIndex = 0;
  final GlobalKey qrKey = GlobalKey(debugLabel: 'QR');
  final _textEditingCodeFocusNode = FocusNode();
  final _textEditingCodeController = TextEditingController();
  QRViewController? _qrViewController;
  String _qrTextResult = '';
  int lastTimeWhenCardSlide = 0;
  ProfileTempCodeEntity _tempCodeData = ProfileTempCodeEntity(
      code: '1234',
      createdAt: DateTime.now(),
      friendId: '',
      id: '',
      message: '',
      status: '',
      userId: '');

  set qrTextResult(String val) {
    _textEditingCodeController.text = val;
    setState(() {
      _qrTextResult = val;
    });
  }

  set tempCodeData(ProfileTempCodeEntity val) {
    setState(() {
      _tempCodeData = val;
    });
  }

  void _onQRViewCreated(QRViewController controller) {
    setState(() {
      _qrViewController = controller;
    });
    _qrViewController!.scannedDataStream.listen((scanData) {
      Analytics.instance.logEvent(name: 'qr_code_readed');
      _qrViewController!.pauseCamera();
      qrTextResult = scanData.code!;
      _handleAddFriendByFriendTempCode();
    });
  }

  @override
  void dispose() {
    _textEditingCodeController.dispose();
    _textEditingCodeFocusNode.dispose();
    super.dispose();
  }

  FutureOr<Null> _handleRequisitionError(dynamic errorResult) {
    final errMessage = errorResult.toString().isEmpty
        ? 'An error occurred, please, verify your internet connection and try again later'
        : errorResult.toString().replaceAll('Exception: ', '');

    ShowMessage(
        message: errMessage,
        callback: () {
          qrTextResult = '';
          _textEditingCodeFocusNode.unfocus();
          _resumeCamera();
        },
        duration: Duration(seconds: 5));
  }

  void _handleRenewCode() async {
    isLoading = true;
    _controller
        .generateProfileTempCode()
        .then((data) {
          tempCodeData = data;
        })
        .catchError(_handleRequisitionError)
        .whenComplete(() {
          isLoading = false;
        });
  }

  set currentTabIndex(int index) {
    if (index < 0 || index > 1) {
      return;
    }
    setState(() {
      _currentTabIndex = index;
    });
  }

  set isLoading(bool val) {
    setState(() {
      _isLoading = val;
    });
  }

  set isTryingToAddFriend(bool val) {
    setState(() {
      _isTryingToAddFriend = val;
    });
  }

  @override
  void initState() {
    _configureTextEditingCodeFocusNode();
    _handleRenewCode();
    super.initState();
  }

  void handleBack() {
    Navigator.pop(context);
  }

  void _configureTextEditingCodeFocusNode() {
    _textEditingCodeFocusNode.addListener(() {
      if (_textEditingCodeFocusNode.hasFocus) {
        Analytics.instance.logEvent(name: 'typing_friend_temp_code');
        _pauseCamera();
      } else {
        _resumeCamera();
      }
    });
  }

  void _pauseCamera() {
    _qrViewController?.pauseCamera();
    setState(() => true);
  }

  void _resumeCamera() {
    _qrViewController?.resumeCamera();
    setState(() => true);
  }

  Widget _buildTabButton(
      int index, IconData icon, double iconSize, String label,
      {bool hasBadge = false}) {
    final iconWidget = Icon(
      icon,
      color: Colors.white,
      size: iconSize,
    );
    return SizedBox(
      width: 41.54.w,
      height: 4.5.h,
      child: Button(
        autoSized: true,
        color: CustomColors.primary
            .withAlpha(_currentTabIndex == index ? 255 : 115),
        borderColor: Colors.transparent,
        onPressed: () => currentTabIndex = index,
        noAnimation: true,
        text: label,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            iconWidget,
            SizedBox(
              width: 2.1.w,
            ),
            Text(
              label,
              style: TextStyle(
                  letterSpacing: 3,
                  fontFamily: "Roboto",
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                  fontSize: 14.sp),
            )
          ],
        ),
      ),
    );
  }

  Widget _buildInputCode() {
    return SizedBox(
      width: 57.43.w,
      height: 4.74.h,
      child: TextFormField(
        focusNode: _textEditingCodeFocusNode,
        controller: _textEditingCodeController,
        textInputAction: TextInputAction.send,
        textAlign: TextAlign.center,
        decoration: InputDecoration(
          hintText: 'TYPE CODE HERE',
          hintStyle: TextStyle(
              fontFamily: "Roboto",
              color: CustomColors.americanPurple,
              fontSize: 16.sp,
              fontWeight: FontWeight.bold),
          filled: true,
          fillColor: Colors.white,
          border: const OutlineInputBorder(
              borderSide: BorderSide(
                width: 0,
                style: BorderStyle.none,
              ),
              borderRadius: BorderRadius.all(Radius.circular(37))),
          contentPadding: const EdgeInsets.all(0),
          suffixIcon: IconButton(
            icon: Icon(QuyckyIcons.copy, size: 18.sp, color: Colors.deepOrange),
            onPressed: _handlePasteCodeFromClipboard,
          ),
        ),
        keyboardType: TextInputType.number,
        style: const TextStyle(
            fontFamily: "Roboto",
            color: CustomColors.americanPurple,
            fontSize: 16),
        onChanged: (text) {
          qrTextResult =
              text; // Atualiza _qrTextResult com o texto do TextFormField
        },
      ),
    );
  }

  Widget _buildQrView() {
    if (_qrTextResult.isNotEmpty || _textEditingCodeFocusNode.hasFocus) {
      return Container();
    }
    final qrCodeView = QRView(
      key: qrKey,
      onQRViewCreated: _onQRViewCreated,
      overlay: QrScannerOverlayShape(
        borderRadius: 10,
        borderLength: 30,
        borderWidth: 10,
        cutOutSize: (30.h > 256 ? 256 : 30.h),
      ),
    );

    return Container(
      constraints: BoxConstraints(maxWidth: 390, maxHeight: 356),
      margin: EdgeInsets.only(bottom: 3.55.h),
      width: 100.w,
      height: 42.18.h,
      child: qrCodeView,
      // Container(color: Colors.black),
    );
  }

  Widget _buildGeneratingNewCodeLoading() {
    return Column(
      children: [
        SizedBox(
          height: 7.7.h,
        ),
        SizedBox(
          height: 12.8.h,
          width: 12.8.h,
          child: LoadingIndicator(
            indicatorType: Indicator.circleStrokeSpin,
            strokeWidth: 4,
            colors: [Colors.white],
          ),
        ),
        SizedBox(
          height: 1.78.h,
        ),
        RichText(
          textAlign: TextAlign.center,
          text: TextSpan(
            text: 'Generating a new ',
            style: TextStyle(
                letterSpacing: 0.25,
                fontFamily: "Roboto",
                fontWeight: FontWeight.normal,
                fontSize: 14),
            children: <TextSpan>[
              TextSpan(
                  text: 'QR Code...',
                  style: TextStyle(
                      letterSpacing: 0.25,
                      fontFamily: "Roboto",
                      height: 1.2,
                      fontWeight: FontWeight.bold,
                      fontSize: 14)),
            ],
          ),
        )
      ],
    );
  }

  Widget _buildTabBar() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        _buildTabButton(0, Icons.qr_code_rounded, 18.sp, 'SHARE'),
        SizedBox(
          width: 4.1.w,
        ),
        _buildTabButton(1, Icons.fullscreen, 18.5.sp, 'SCAN', hasBadge: true)
      ],
    );
  }

  Widget _buildAvatarAndName() {
    return Column(
      children: [
        Avatar(
          size: 11.84.h,
          addPhotoButton: false,
          imagePath: _userStore.state.user.avatarUrl,
        ),
        SizedBox(
          height: 1.78.h,
        ),
        Text(
          _userStore.state.user.name,
          style: TextStyle(
              letterSpacing: 0.4,
              color: Colors.white,
              fontFamily: 'Roboto',
              fontSize: 16.sp,
              fontWeight: FontWeight.bold),
        ),
      ],
    );
  }

  Widget _buildQrCode() {
    return Container(
      height: 28.55.h,
      width: 61.w,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.all(Radius.circular(17)),
        border: Border(
          top: BorderSide(color: Colors.white, width: 1),
          bottom: BorderSide(color: Colors.white, width: 1),
          left: BorderSide(color: Colors.white, width: 1),
          right: BorderSide(color: Colors.white, width: 1),
        ),
      ),
      child: Center(
        child: Padding(
          padding: EdgeInsets.all(1.w),
          child: QrImageView(
            data: _tempCodeData.code,
            eyeStyle: QrEyeStyle(
              eyeShape: QrEyeShape.square,
              color: Colors.white,
            ),
            dataModuleStyle: QrDataModuleStyle(
              dataModuleShape: QrDataModuleShape.square,
              color: Colors.white,
            ),
            version: QrVersions.auto,
          ),
        ),
      ),
    );
  }

  void _handlePasteCodeFromClipboard() async {
    final result = await Clipboard.getData(Clipboard.kTextPlain);
    qrTextResult = result?.text ?? '';
  }

  void _handleCopyCode() {
    Clipboard.setData(ClipboardData(text: _tempCodeData.code));
    ShowMessage(
        message: 'Code copied to clipboard', duration: Duration(seconds: 1));
  }

  Widget _buildCurrentCodeCopy() {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 2.38.h),
      child: GestureDetector(
        onTap: _handleCopyCode,
        child: Container(
          width: 51.8.w,
          height: 4.15.h,
          decoration: BoxDecoration(
              color: CustomColors.salmon5,
              borderRadius: BorderRadius.circular(37)),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            mainAxisSize: MainAxisSize.max,
            children: [
              Container(
                width: 37.w,
                margin: EdgeInsets.only(left: 5.5.w),
                child: Text(
                  _tempCodeData.code,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                      letterSpacing: 0.5,
                      color: CustomColors.snow,
                      fontFamily: 'Roboto',
                      fontSize: 16.sp,
                      fontWeight: FontWeight.bold),
                ),
              ),
              Padding(
                padding: EdgeInsets.only(right: 2.82.w),
                child: Icon(
                  QuyckyIcons.copy,
                  color: Colors.white,
                  size: 18.sp,
                ),
              )
            ],
          ),
        ),
      ),
    );
  }

  void _handleShareInviteTicket() {
    _controller.generateFriendlyInvitationTempCode().then((data) {
      final message =
          'Ready to play? Tap this link to add me ${_remoteConfig.state.appUrl}/friendship/ticket/${data.code}';
      Share.share(message)
          .then((value) => ShowMessage(
              message: 'Link shared', duration: Duration(seconds: 1)))
          .catchError((err) => print('Log'));
    }).catchError(_handleRequisitionError);
  }

  Widget _buildQrCodeTimer() {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 1.h),
      child: QrCodeTimer(
        startTime: _tempCodeData.createdAt,
        secondsUntilNextCode: 60,
        onEndTime: _handleRenewCode,
      ),
    );
  }

  Widget _buildShareTabView() {
    final currentWidgets = [
      SizedBox(
        height: 2.5.h,
      ),
      _buildAvatarAndName(),
    ];
    if (_isLoading) {
      currentWidgets.add(_buildGeneratingNewCodeLoading());
    } else {
      currentWidgets.addAll(
          [_buildCurrentCodeCopy(), _buildQrCode(), _buildQrCodeTimer()]);
    }
    return Column(
      children: [
        Container(
            constraints: BoxConstraints(maxHeight: 62.91.h),
            height: 62.91.h,
            width: 87.18.w,
            decoration: BoxDecoration(
                color: CustomColors.primary,
                borderRadius: BorderRadius.circular(16)),
            child: Column(children: currentWidgets)),
        SizedBox(
          height: 2.48.h,
        ),
        SizedBox(
          height: 6.h,
          width: 87.18.w,
          child: Button(
              outlined: true,
              borderColor: Colors.white,
              text: 'SHARE PROFILE',
              autoSized: true,
              onPressed: _handleShareInviteTicket),
        ),
      ],
    );
  }

  void _handleGoToMatchPage(String friendId) {
    Modular.to.pushReplacementNamed(AppRoutes.checkFriendMatch,
        arguments: {'friendId': friendId, 'isNewFriend': true});
  }

  void _handleAddFriendByFriendTempCode() async {
    if (_qrTextResult.isEmpty) {
      ShowMessage(
          message: 'Please, insert a code to add a friend',
          duration: Duration(seconds: 5));
      return;
    }
    if (_qrTextResult == _tempCodeData.code) {
      ShowMessage(
          message: 'You cannot add yourself as a friend',
          duration: Duration(seconds: 5));
      return;
    }
    _pauseCamera();
    isTryingToAddFriend = true;
    await _controller.validateProfileTempCode(_qrTextResult).then((value) {
      ShowMessage(
          message: 'Friend added successfully',
          duration: Duration(seconds: 3),
          onPressed: () => _handleGoToMatchPage(value.userId),
          callback: () => _handleGoToMatchPage(value.userId));
    }).catchError((errorResult) {
      _handleRequisitionError(errorResult);
      isTryingToAddFriend = false;
    });
  }

  Widget _buildCodeSubmitArea() {
    Color buttonColor = CustomColors.primary.withAlpha(100);
    Color textColor = Colors.white.withAlpha(135);
    if (_qrTextResult.isNotEmpty) {
      buttonColor = CustomColors.primary;
      textColor = Colors.white;
    }
    return Column(
      children: [
        Text(
          'ENTER CODE',
          style: TextStyle(
              letterSpacing: 1,
              fontFamily: "Roboto",
              fontWeight: FontWeight.bold,
              color: Colors.white,
              fontSize: 16.sp),
        ),
        Padding(
          padding: EdgeInsets.symmetric(vertical: 2.8.h), //3.25.h),
          child: Text(
            'Add another player as friend by\ninserting a code provided to you here.',
            textAlign: TextAlign.center,
            style: TextStyle(
                height: 1.3,
                letterSpacing: 0.25,
                fontFamily: "Roboto",
                fontWeight: FontWeight.w400,
                color: Colors.white,
                fontSize: 14.sp),
          ),
        ),
        _buildInputCode(),
        SizedBox(height: 2.8.h),
        SizedBox(
          height: 6.h,
          width: 87.18.w,
          child: Button(
            borderColor: Colors.transparent,
            text: 'ADD FRIEND',
            autoSized: true,
            onPressed: _handleAddFriendByFriendTempCode,
            color: buttonColor,
            textColor: textColor,
          ),
        ),
      ],
    );
  }

  Widget _buildScanTabView() {
    return Column(
      children: [
        _buildQrView(),
        _buildCodeSubmitArea(),
      ],
    );
  }

  void _handleTapScreen() {
    if (_textEditingCodeFocusNode.hasFocus) {
      _textEditingCodeFocusNode.unfocus();
    }
  }

  Widget _getCurrentTabView() {
    return _currentTabIndex == 0 ? _buildShareTabView() : _buildScanTabView();
  }

  void _handleScreenSlide(DragUpdateDetails details) {
    final currentTime = DateTime.now().millisecondsSinceEpoch;
    if ((currentTime - lastTimeWhenCardSlide) < 500) {
      return;
    }
    lastTimeWhenCardSlide = currentTime;
    if (details.delta.dx > 0 && _currentTabIndex == 1) {
      currentTabIndex = 0;
      return;
    }
    if (details.delta.dx < 0 && _currentTabIndex == 0) {
      currentTabIndex = 1;
    }
  }

  Widget _buildBody() {
    return _isTryingToAddFriend
        ? QuyckyModalProgress(
            loadingIndex: 1,
            state: true,
            child: Container(),
          )
        : SafeArea(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  AppHeader(
                    noTitleSubtitleSpace: true,
                    logoSectionLeftWidget: IconButton(
                        onPressed: handleBack,
                        icon: Icon(
                          QuyckyIcons.arrow_left_circle,
                          color: Colors.white,
                          size: 23,
                        )),
                  ),
                  _buildTabBar(),
                  SizedBox(
                    height: 3.31.h,
                  ),
                  _getCurrentTabView(),
                ],
              ),
            ),
          );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: GestureDetector(
        onPanUpdate: _handleScreenSlide,
        onTap: _handleTapScreen,
        child: GradientContainer(useDefault: true, child: _buildBody()),
      ),
    );
  }
}
