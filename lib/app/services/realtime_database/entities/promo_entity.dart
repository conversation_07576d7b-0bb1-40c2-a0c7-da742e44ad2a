import 'dart:convert';

class PromoEntity {
  final int whenUserHasPlayed;
  final String url;
  final String imageUrl;
  final int minutesToRedeem;
  final int startTime;
  final String status;

  PromoEntity({
    required this.whenUserHasPlayed,
    required this.url,
    required this.imageUrl,
    required this.minutesToRedeem,
    required this.startTime,
    required this.status,
  });

  PromoEntity copyWith({
    int? whenUserHasPlayed,
    String? url,
    String? imageUrl,
    int? minutesToRedeem,
    int? startTime,
    String? status,
  }) =>
      PromoEntity(
        whenUserHasPlayed: whenUserHasPlayed ?? this.whenUserHasPlayed,
        url: url ?? this.url,
        imageUrl: imageUrl ?? this.imageUrl,
        minutesToRedeem: minutesToRedeem ?? this.minutesToRedeem,
        startTime: startTime ?? this.startTime,
        status: status ?? this.status,
      );

  factory PromoEntity.fromRawJson(String str) =>
      PromoEntity.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory PromoEntity.fromJson(Map<String, dynamic> json) => PromoEntity(
        whenUserHasPlayed: json["when_user_has_played"],
        url: json["url"],
        imageUrl: json["image_url"],
        minutesToRedeem: json["minutes_to_redeem"],
        startTime: json["start_time"],
        status: json["status"],
      );

  Map<String, dynamic> toJson() => {
        "when_user_has_played": whenUserHasPlayed,
        "url": url,
        "image_url": imageUrl,
        "minutes_to_redeem": minutesToRedeem,
        "start_time": startTime,
        "status": status,
      };
}
