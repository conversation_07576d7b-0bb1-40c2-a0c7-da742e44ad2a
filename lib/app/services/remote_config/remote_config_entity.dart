// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:convert';
import 'package:equatable/equatable.dart';
import 'package:quycky/core/utils/type_converters.dart';

class CurrentAppVersionInfoEntity {
  final String currentVersion;
  final String appleStore;
  final String androidStore;

  const CurrentAppVersionInfoEntity({
    required this.currentVersion,
    required this.appleStore,
    required this.androidStore,
  });

  CurrentAppVersionInfoEntity copyWith({
    String? currentVersion,
    String? appleStore,
    String? androidStore,
  }) =>
      CurrentAppVersionInfoEntity(
        currentVersion: currentVersion ?? this.currentVersion,
        appleStore: appleStore ?? this.appleStore,
        androidStore: androidStore ?? this.androidStore,
      );

  factory CurrentAppVersionInfoEntity.fromJson(String str) =>
      CurrentAppVersionInfoEntity.fromMap(json.decode(str));

  String toJson() => json.encode(toMap());

  factory CurrentAppVersionInfoEntity.fromMap(Map<String, dynamic> json) =>
      CurrentAppVersionInfoEntity(
        currentVersion: dynamicToString(json["current_version"]),
        appleStore: dynamicToString(json["apple_store"]),
        androidStore: dynamicToString(json["android_store"]),
      );

  Map<String, dynamic> toMap() => {
        "current_version": currentVersion,
        "apple_store": appleStore,
        "android_store": androidStore,
      };
}

class BannerPromoRuleAndData {
  final int whenUserHasPlayed;
  final String url;
  final String imageUrl;
  final int minutesToRedeem;

  BannerPromoRuleAndData({
    required this.whenUserHasPlayed,
    required this.url,
    required this.imageUrl,
    this.minutesToRedeem = 0,
  });

  BannerPromoRuleAndData copyWith({
    int? whenUserHasPlayed,
    String? url,
    String? imageUrl,
    int? minutesToRedeem,
  }) =>
      BannerPromoRuleAndData(
          whenUserHasPlayed: whenUserHasPlayed ?? this.whenUserHasPlayed,
          url: url ?? this.url,
          imageUrl: imageUrl ?? this.imageUrl,
          minutesToRedeem: minutesToRedeem ?? this.minutesToRedeem);

  factory BannerPromoRuleAndData.fromJson(String str) =>
      BannerPromoRuleAndData.fromMap(json.decode(str));

  String toJson() => json.encode(toMap());

  factory BannerPromoRuleAndData.fromMap(Map<String, dynamic> json) =>
      BannerPromoRuleAndData(
          whenUserHasPlayed: dynamicToInt(json["when_user_has_played"]),
          url: dynamicToString(json["url"]),
          imageUrl: dynamicToString(json["image_url"]),
          minutesToRedeem: dynamicToInt(json["minutes_to_redeem"]));

  Map<String, dynamic> toMap() => {
        "when_user_has_played": whenUserHasPlayed,
        "url": url,
        "image_url": imageUrl,
        "minutes_to_redeem": minutesToRedeem
      };
}

class PontuationConfigEntity extends Equatable {
  final int fire;
  final int ice;

  const PontuationConfigEntity({
    required this.fire,
    required this.ice,
  });

  PontuationConfigEntity copyWith({
    int? fire,
    int? ice,
  }) {
    return PontuationConfigEntity(
      fire: fire ?? this.fire,
      ice: ice ?? this.ice,
    );
  }

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'fire': fire,
      'ice': ice,
    };
  }

  factory PontuationConfigEntity.fromMap(Map<String, dynamic> map) {
    return PontuationConfigEntity(
      fire: map['fire'] as int,
      ice: map['ice'] as int,
    );
  }

  String toJson() => json.encode(toMap());

  factory PontuationConfigEntity.fromJson(String source) =>
      PontuationConfigEntity.fromMap(
          json.decode(source) as Map<String, dynamic>);

  @override
  bool get stringify => true;

  @override
  List<Object> get props => [fire, ice];
}

class RemoteConfigEntity extends Equatable {
  final int gameRounds;
  final int gameTimeRoundAnswer;
  final int gameTimeRoundQuestion;
  final String promoUrlEndgame;
  final String profileTitleChart;
  final int minimunRoundsToMatch;
  final bool iqActive;
  final PontuationConfigEntity pontuation;
  final List<BannerPromoRuleAndData> bannersPromoRulesAndData;
  final CurrentAppVersionInfoEntity currentAppVersionInfo;
  final int gameTimeWaitingStartGameMultiplayer;
  final String appUrl;
  final String popupDisallowedVersions;
  final String productionGameServerUrl;
  final String developmentGameServerUrl;

  const RemoteConfigEntity(
      {this.gameRounds = 1,
      this.gameTimeRoundAnswer = 1,
      this.gameTimeRoundQuestion = 1,
      this.promoUrlEndgame = "www.quycky.com",
      this.profileTitleChart = 'QUYCKYNESS CHARTS',
      this.gameTimeWaitingStartGameMultiplayer = 3,
      this.minimunRoundsToMatch = 4,
      this.iqActive = false,
      this.currentAppVersionInfo = const CurrentAppVersionInfoEntity(
          currentVersion: '', androidStore: '', appleStore: ''),
      this.pontuation = const PontuationConfigEntity(ice: 0, fire: 10),
      this.bannersPromoRulesAndData = const [],
      this.popupDisallowedVersions = '',
      this.appUrl = '',
      this.productionGameServerUrl = '',
      this.developmentGameServerUrl = ''});

  RemoteConfigEntity copyWith({
    int? gameRounds,
    int? gameTimeRoundAnswer,
    int? gameTimeRoundQuestion,
    String? promoUrlEndgame,
    String? profileTitleChart,
    bool? iqActive,
    int? minimunRoundsToMatch,
    PontuationConfigEntity? pontuation,
    List<BannerPromoRuleAndData>? bannersPromoRulesAndData,
    CurrentAppVersionInfoEntity? currentAppVersionInfo,
    int? gameTimeWaitingStartGameMultiplayer,
    String? appUrl,
    String? popupDisallowedVersions,
    String? productionGameServerUrl,
    String? developmentGameServerUrl,
  }) {
    return RemoteConfigEntity(
        gameTimeWaitingStartGameMultiplayer:
            gameTimeWaitingStartGameMultiplayer ??
                this.gameTimeWaitingStartGameMultiplayer,
        minimunRoundsToMatch: minimunRoundsToMatch ?? this.minimunRoundsToMatch,
        gameRounds: gameRounds ?? this.gameRounds,
        gameTimeRoundAnswer: gameTimeRoundAnswer ?? this.gameTimeRoundAnswer,
        gameTimeRoundQuestion:
            gameTimeRoundQuestion ?? this.gameTimeRoundQuestion,
        promoUrlEndgame: promoUrlEndgame ?? this.promoUrlEndgame,
        profileTitleChart: profileTitleChart ?? this.profileTitleChart,
        iqActive: iqActive ?? this.iqActive,
        pontuation: pontuation ?? this.pontuation,
        bannersPromoRulesAndData:
            bannersPromoRulesAndData ?? this.bannersPromoRulesAndData,
        currentAppVersionInfo:
            currentAppVersionInfo ?? this.currentAppVersionInfo,
        popupDisallowedVersions:
            popupDisallowedVersions ?? this.popupDisallowedVersions,
        appUrl: appUrl ?? this.appUrl,
        productionGameServerUrl:
            productionGameServerUrl ?? this.productionGameServerUrl,
        developmentGameServerUrl:
            developmentGameServerUrl ?? this.developmentGameServerUrl);
  }

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'MINIMUN_ROUNDS_TO_MATCH': minimunRoundsToMatch,
      'GAME_TIME_WAITING_START_GAME_MULTIPLAYER':
          gameTimeWaitingStartGameMultiplayer,
      'PROFILE_TITLE_CHART': profileTitleChart,
      'GAME_ROUNDS': gameRounds,
      'GAME_TIME_ROUND_ANSWER': gameTimeRoundAnswer,
      'GAME_TIME_ROUND_QUESTION': gameTimeRoundQuestion,
      'PROMO_URL_ENDGAME': promoUrlEndgame,
      'IQ_ACTIVE': iqActive,
      'PONTUATION': pontuation.toJson(),
      'BANNERS_RULES_AND_DATA': json.encode(
          List<dynamic>.from(bannersPromoRulesAndData.map((x) => x.toJson()))),
      'CURRENT_APP_VERSION_INFO': currentAppVersionInfo.toJson(),
      'APP_URL': appUrl,
      'POPUP_DISALLOWED_VERSIONS': popupDisallowedVersions,
      'PRODUCTION_GAME_SERVER_URL': productionGameServerUrl,
      'DEVELOPMENT_GAME_SERVER_URL': developmentGameServerUrl,
    };
  }

  factory RemoteConfigEntity.fromMap(Map<String, dynamic> map) {
    final bannersData = map['BANNERS_RULES_AND_DATA'].runtimeType == String &&
            map['BANNERS_RULES_AND_DATA'] != ''
        ? json.decode(map['BANNERS_RULES_AND_DATA'])
        : [];

    return RemoteConfigEntity(
        gameRounds: map['GAME_ROUNDS'] as int,
        gameTimeWaitingStartGameMultiplayer:
            map['GAME_TIME_WAITING_START_GAME_MULTIPLAYER'] as int,
        minimunRoundsToMatch: map['MINIMUN_ROUNDS_TO_MATCH'] as int,
        gameTimeRoundAnswer: map['GAME_TIME_ROUND_ANSWER'] as int,
        gameTimeRoundQuestion: map['GAME_TIME_ROUND_QUESTION'] as int,
        profileTitleChart: map['PROFILE_TITLE_CHART'] as String,
        promoUrlEndgame: map['PROMO_URL_ENDGAME'] as String,
        iqActive: map['IQ_ACTIVE'] as bool,
        pontuation: PontuationConfigEntity.fromMap(map['PONTUATION']),
        bannersPromoRulesAndData: List<BannerPromoRuleAndData>.from(
          (bannersData).map<BannerPromoRuleAndData>(
            (x) => BannerPromoRuleAndData.fromMap(x as Map<String, dynamic>),
          ),
        ),
        appUrl: dynamicToString(map['APP_URL']),
        popupDisallowedVersions:
            dynamicToString(map['POPUP_DISALLOWED_VERSIONS']),
        currentAppVersionInfo: CurrentAppVersionInfoEntity.fromMap(
            map['CURRENT_APP_VERSION_INFO']),
        productionGameServerUrl:
            dynamicToString(map['PRODUCTION_GAME_SERVER_URL']),
        developmentGameServerUrl:
            dynamicToString(map['DEVELOPMENT_GAME_SERVER_URL']));
  }

  String toJson() => json.encode(toMap());

  factory RemoteConfigEntity.fromJson(String source) =>
      RemoteConfigEntity.fromMap(json.decode(source) as Map<String, dynamic>);

  @override
  bool get stringify => true;

  @override
  List<Object> get props {
    return [
      gameRounds,
      gameTimeRoundAnswer,
      gameTimeRoundQuestion,
      promoUrlEndgame,
      profileTitleChart,
      iqActive,
      pontuation,
      currentAppVersionInfo,
      gameTimeWaitingStartGameMultiplayer,
      bannersPromoRulesAndData,
      appUrl,
      popupDisallowedVersions,
      productionGameServerUrl,
      developmentGameServerUrl,
    ];
  }
}
