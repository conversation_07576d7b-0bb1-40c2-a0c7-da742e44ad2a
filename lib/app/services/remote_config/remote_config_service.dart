// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:convert';

import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_remote_config/firebase_remote_config.dart';

import 'package:quycky/app/services/remote_config/remote_config_entity.dart';
import 'package:quycky/app/services/remote_config/remote_config_store.dart';
import 'package:quycky/core/utils/interval_utils.dart';
import 'package:quycky/core/utils/type_converters.dart';

class RemoteConfigServiceListenerEntity {
  final int id;
  final void Function() func;
  RemoteConfigServiceListenerEntity({
    required this.id,
    required this.func,
  });
}

class RemoteConfigService {
  final RemoteConfigStore _remoteConfigStore;
  final List<RemoteConfigServiceListenerEntity> _listeners = [];
  bool _isListenersExecuted = false;

  bool get isListenersExecuted {
    final bool res = _isListenersExecuted;
    _isListenersExecuted = false;
    return res;
  }

  int addListener(void Function() func) {
    int id = DateTime.now().millisecondsSinceEpoch;
    _listeners.add(RemoteConfigServiceListenerEntity(id: id, func: func));
    return id;
  }

  void removeListener(int index) {
    final id = _listeners.indexWhere((element) => element.id == index);
    if (id == -1) return;
    _listeners.removeAt(id);
  }

  void _executeListeners() {
    _isListenersExecuted = true;
    for (var element in _listeners) {
      element.func();
    }
  }

  RemoteConfigService(this._remoteConfigStore) {
    initialize();
  }

  Future<void> updateData() async {
    final remoteConfig = FirebaseRemoteConfig.instance;
    await remoteConfig.activate();
    final bannersData = remoteConfig.getString('BANNERS_RULES_AND_DATA') != ''
        ? json.decode(remoteConfig.getString('BANNERS_RULES_AND_DATA'))
        : [];
    final currentAppVersionInfo = !['', null]
            .contains(remoteConfig.getString('CURRENT_APP_VERSION_INFO'))
        ? CurrentAppVersionInfoEntity.fromMap(
            dynamicToMap(remoteConfig.getString('CURRENT_APP_VERSION_INFO')))
        : const CurrentAppVersionInfoEntity(
            androidStore: '', appleStore: '', currentVersion: '');
    final pontuation =
        !['', null].contains(remoteConfig.getString('PONTUATION'))
            ? PontuationConfigEntity.fromMap(
                dynamicToMap(remoteConfig.getString('PONTUATION')))
            : const PontuationConfigEntity(fire: 10, ice: 0);
    RemoteConfigEntity newRemoteConfig = RemoteConfigEntity(
        gameRounds: remoteConfig.getInt('GAME_ROUNDS'),
        gameTimeRoundAnswer: remoteConfig.getInt('GAME_TIME_ROUND_ANSWER'),
        gameTimeRoundQuestion: remoteConfig.getInt('GAME_TIME_ROUND_QUESTION'),
        promoUrlEndgame: remoteConfig.getString('PROMO_URL_ENDGAME'),
        iqActive: remoteConfig.getBool('IQ_ACTIVE'),
        profileTitleChart: remoteConfig.getString('PROFILE_TITLE_CHART'),
        minimunRoundsToMatch: remoteConfig.getInt('MINIMUN_ROUNDS_TO_MATCH'),
        gameTimeWaitingStartGameMultiplayer:
            remoteConfig.getInt('GAME_TIME_WAITING_START_GAME_MULTIPLAYER'),
        bannersPromoRulesAndData: List<BannerPromoRuleAndData>.from(
            (bannersData).map<BannerPromoRuleAndData>(
          (x) => BannerPromoRuleAndData.fromMap(x as Map<String, dynamic>),
        )),
        pontuation: pontuation,
        currentAppVersionInfo: currentAppVersionInfo,
        popupDisallowedVersions:
            remoteConfig.getString('POPUP_DISALLOWED_VERSIONS'),
        appUrl: remoteConfig.getString('APP_URL'),
        productionGameServerUrl:
            remoteConfig.getString('PRODUCTION_GAME_SERVER_URL'),
        developmentGameServerUrl:
            remoteConfig.getString('DEVELOPMENT_GAME_SERVER_URL'));
    _remoteConfigStore.setData(newRemoteConfig);
    _executeListeners();
  }

  Future<void> initialize({int n = 0}) async {
    if (Firebase.apps.isEmpty) {
      setTimeout(
          callback: () => initialize(n: n + 1),
          duration: const Duration(seconds: 3));
      return;
    }

    final remoteConfig = FirebaseRemoteConfig.instance;
    await remoteConfig.setConfigSettings(RemoteConfigSettings(
      fetchTimeout: const Duration(minutes: 1),
      minimumFetchInterval: const Duration(hours: 1),
    ));
    final newRemoteConfig = const RemoteConfigEntity().toMap();
    await remoteConfig.setDefaults(newRemoteConfig);

    await remoteConfig.fetchAndActivate();
    updateData();

    remoteConfig.onConfigUpdated.listen((event) => updateData());
  }
}
