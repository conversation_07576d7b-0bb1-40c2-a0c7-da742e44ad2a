import 'package:flutter_modular/flutter_modular.dart';
import 'package:quycky/app/features/user/domain/usecases/get_user_iq_usecase.dart';
import 'package:quycky/app/features/user/presenter/controllers/user_profile_controller.dart';
import 'package:quycky/app/features/user/presenter/pages/user_profile_page.dart';
import 'package:quycky/app/features/user/presenter/pages/user_register_page.dart';
import 'package:quycky/app/features/user/presenter/store/user_iq_show_more_store.dart';
import 'package:quycky/app/features/user/presenter/store/user_iq_store.dart';
import 'package:quycky/app/features/user/presenter/store/user_profile_store.dart';
import 'package:quycky/app/modules/guards/auth_guard.dart';
import 'package:quycky/core/utils/app_routes.dart';

class UserModule extends Module {
  @override
  final List<Bind> binds = [
    Bind.lazySingleton((i) => UserIQStore()),
    Bind.lazySingleton((i) => UserProfileStore()),
    Bind.lazySingleton((i) => UserIQPersonalityShowMoreStore()),
    Bind.factory((i) => GetUserIQUseCase(i())),
    Bind.factory((i) => UserProfileController(i(), i(), i(), i()))
  ];

  @override
  final List<ModularRoute> routes = [
    ChildRoute(AppRoutes.userRegister(complete: false),
        child: (_, args) => const UserRegisterPage(),
        transition: TransitionType.upToDown),
    ChildRoute(AppRoutes.userProfile(complete: false),
        child: (_, args) => UserProfilePage(),
        // guards: [AuthGuard()],
        transition: TransitionType.upToDown),
  ];
}
