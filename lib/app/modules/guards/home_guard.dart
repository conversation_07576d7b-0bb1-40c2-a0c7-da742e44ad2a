import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:quycky/core/services/notification_service/local_notifications_service.dart';
import 'package:quycky/core/services/push_service/abstract_push_service.dart';

class HomeGuard extends RouteGuard {
  @override
  Future<bool> canActivate(String url, ModularRoute route) async {
    try {
      final pushService = Modular.get<AbstractPushService>();
      pushService.getToken();

      if (!kIsWeb) {
        // await LocalNotificationService.initialize();
      }
    } catch (e) {
      print('err=:push service instantiate=>$e');
    }
    print('hgErr');
    return true;
  }
}
