import 'dart:async';

import 'package:flutter_modular/flutter_modular.dart';
import 'package:quycky/app/features/game/presenter/storage/game_storage.dart';
import 'package:quycky/app/features/user/presenter/store/user_store.dart';
import 'package:quycky/core/utils/app_routes.dart';
import 'package:quycky/core/utils/show_message.dart';

class AuthGuard extends RouteGuard {
  AuthGuard() : super(redirectTo: AppRoutes.initialRoute);

  void _goToInitialRoute() {
    Modular.to.pushNamedAndRemoveUntil(AppRoutes.initialRoute, (p0) => false);
  }

  @override
  Future<bool> canActivate(String url, ModularRoute route) async {
    final UserStore userStore = Modular.get<UserStore>();
    bool res = userStore.isUserLogged;

    if (!res) {
      GameStorage gameStorage = Modular.get<GameStorage>();
      gameStorage.setGameRoomWaiting();
      ShowMessage(
        noAvatar: true,
        message: 'User is not logged in',
        duration: const Duration(seconds: 3),
        onPressed: _goToInitialRoute,
      );
    }
    return res;
  }
}
