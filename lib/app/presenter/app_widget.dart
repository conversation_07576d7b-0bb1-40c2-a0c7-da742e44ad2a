import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:sizer/sizer.dart';

class AppWidget extends StatelessWidget {
  const AppWidget({super.key});

  static GlobalKey<NavigatorState> globalKey = GlobalKey<NavigatorState>();

  @override
  Widget build(BuildContext context) {
    Modular.setNavigatorKey(AppWidget.globalKey);
    return Sizer(builder: (context, orientation, deviceType) {
      return MaterialApp.router(
        title: 'Quycky',
        routeInformationParser: Modular.routeInformationParser,
        routerDelegate: Modular.routerDelegate,
        theme: ThemeData(primarySwatch: Colors.blue),
        // theme: Provider.of<ThemeStore>(context).currentTheme,
      );
    });
  }
}
