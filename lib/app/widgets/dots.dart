import 'package:flutter/material.dart';
import 'package:quycky/app/theme/colors.dart';
import 'package:quycky/core/utils/color_utils.dart';
import 'package:sizer/sizer.dart';

class Dots extends StatelessWidget {
  final int itemsCount;
  final Color selectedColor;
  final int value;
  late final Color normalColor;

  Dots(
      {super.key,
      this.selectedColor = CustomColors.orangeSoda,
      required this.value,
      required this.itemsCount}) {
    normalColor = generateLighterColorHSL(selectedColor).withAlpha(120);
  }

  List<Widget> getListItems() {
    List<Widget> res = [];
    final lastItemIndex = itemsCount - 1;
    for (int i = 0; i < itemsCount; i++) {
      final padding = i == lastItemIndex ? null : EdgeInsets.only(right: 10);
      res.add(
        Container(
          margin: padding,
          width: 3.w,
          decoration: BoxDecoration(
              color: i == value ? selectedColor : normalColor,
              shape: BoxShape.circle),
        ),
      );
    }

    return res;
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 33,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: getListItems(),
      ),
    );
  }
}
