import 'package:flutter/material.dart';
// import 'package:loading_animation_widget/loading_animation_widget.dart';
import 'package:loading_indicator/loading_indicator.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'package:quycky/app/theme/colors.dart';
import 'package:quycky/app/widgets/quycky_loading.dart';

class QuyckyModalProgress extends StatefulWidget {
  final Widget child;
  final bool state;
  final int loadingIndex;

  const QuyckyModalProgress(
      {super.key,
      required this.child,
      required this.state,
      this.loadingIndex = 1});

  @override
  _QuyckyModalProgress createState() => _QuyckyModalProgress();
}

class _QuyckyModalProgress extends State<QuyckyModalProgress> {
  // late final AnimationController _controller = AnimationController(
  //   duration: const Duration(seconds: 1),
  //   vsync: this,
  // )..repeat(reverse: true);
  // late final Animation<double> _animation = CurvedAnimation(
  //   parent: _controller,
  //   curve: Curves.fastOutSlowIn,
  // );
  final List<Widget> loadings = [
    const QuyckyLoading(),
    // LoadingAnimationWidget.beat(color: CustomColors.primary, size: 125),
    Container(
        constraints: const BoxConstraints(maxWidth: 190, maxHeight: 190),
        child: const LoadingIndicator(
          indicatorType: Indicator.ballScale,
          colors: [Colors.white],
          strokeWidth: 2,
        )),
  ];

  @override
  Widget build(BuildContext context) {
    return ModalProgressHUD(
        inAsyncCall: widget.state,
        opacity: 0.35,
        blur: 1,
        progressIndicator: loadings[widget.loadingIndex],
        child: widget.child);
  }
}
