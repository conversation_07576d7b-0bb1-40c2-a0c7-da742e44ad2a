import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:quycky/app/theme/colors.dart';
import 'package:quycky/core/utils/get_image_from.dart';
import 'package:quycky/app/widgets/button.dart';
import 'package:quycky/core/utils/quycky_icons_icons.dart';

enum DialogGetImageFromAlignment { top, center, bottom }

class DialogGetImageFrom extends StatefulWidget {
  final DialogGetImageFromAlignment alignment;
  final String title;
  final void Function(XFile? file) onOk;

  const DialogGetImageFrom({
    super.key,
    this.title = 'Pick Image',
    this.alignment = DialogGetImageFromAlignment.center,
    required this.onOk,
  });

  @override
  State<DialogGetImageFrom> createState() => _DialogGetImageFrom();
}

class _DialogGetImageFrom extends State<DialogGetImageFrom> {
  final PickImage pickImage = PickImage();
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
  }

  void close() => Navigator.pop(context);

  MainAxisAlignment getAlignment() {
    switch (widget.alignment) {
      case DialogGetImageFromAlignment.top:
        return MainAxisAlignment.start;
      case DialogGetImageFromAlignment.center:
        return MainAxisAlignment.center;
      case DialogGetImageFromAlignment.bottom:
        return MainAxisAlignment.end;
    }
  }

  getImageFromGallery() async {
    XFile? image = await pickImage.getImageFrom(
        camera: false, imageQuality: 50, maxHeight: 300, maxWidth: 300);
    widget.onOk(image);
    close();
  }

  getImageFromCamera() async {
    XFile? image = await pickImage.getImageFrom(
        camera: true, imageQuality: 50, maxHeight: 300, maxWidth: 300);
    widget.onOk(image);
    close();
  }

  Widget getWidget() {
    return Container(
      constraints: const BoxConstraints(minHeight: 70, maxWidth: 500),
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
      decoration: const BoxDecoration(
          borderRadius: BorderRadius.all(Radius.circular(20)),
          color: Colors.white),
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.only(top: 15, right: 15.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Material(
                  color: Colors.transparent,
                  child: IconButton(
                      onPressed: close,
                      color: CustomColors.americanPurple2,
                      icon: const Icon(QuyckyIcons.close_circle)),
                )
              ],
            ),
          ),
          const Padding(
            padding: EdgeInsets.only(top: 0, right: 15.0),
            child: Center(
              child: Text(
                'CHANGE PICTURE',
                style: TextStyle(
                    fontFamily: 'Roboto',
                    fontSize: 12,
                    color: CustomColors.americanPurple2,
                    letterSpacing: 3,
                    fontWeight: FontWeight.bold,
                    decoration: TextDecoration.none),
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 40),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              mainAxisSize: MainAxisSize.max,
              children: [
                Column(children: [
                  Button(
                    circleButton: true,
                    assetImageUrl: "assets/img/svg/camera.svg",
                    circleButtonSize: 74,
                    assetImageHeight: 30,
                    assetImageWidth: 38,
                    circleButtonColor: CustomColors.orangeSoda,
                    onPressed: getImageFromCamera,
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'CAMERA',
                    style: TextStyle(
                        fontFamily: 'Roboto',
                        fontSize: 10,
                        color: CustomColors.americanPurple2,
                        letterSpacing: 3,
                        fontWeight: FontWeight.bold,
                        decoration: TextDecoration.none),
                  )
                ]),
                Column(children: [
                  Button(
                    circleButton: true,
                    assetImageUrl: "assets/img/svg/image.svg",
                    circleButtonSize: 74,
                    assetImageHeight: 31,
                    assetImageWidth: 31,
                    circleButtonColor: CustomColors.orangeSoda,
                    onPressed: getImageFromGallery,
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'GALLERY',
                    style: TextStyle(
                        fontFamily: 'Roboto',
                        fontSize: 10,
                        color: CustomColors.americanPurple2,
                        letterSpacing: 3,
                        fontWeight: FontWeight.bold,
                        decoration: TextDecoration.none),
                  )
                ])
              ],
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: getAlignment(),
      children: [
        getWidget(),
      ],
    );
  }
}
