import 'dart:io';
import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_svg/svg.dart';
import 'package:quycky/app/theme/colors.dart';
import 'package:quycky/core/utils/assets_strings.dart';
import 'package:quycky/core/utils/quycky_icons_icons.dart';
import 'package:quycky/core/utils/use_cases/get_image_from_url_uint8list_usecase.dart';

const defaultImagePath = "assets/img/svg/avatar.svg"; //avatar.png";

class Avatar extends StatefulWidget {
  final bool addPhotoButton;
  final double size;
  final String? imagePath;
  final Uint8List? imageMemory;
  final void Function()? onPressed;
  final bool outlined;
  final double borderWidth;
  final Color borderColor;
  late final String defaultAvatarPath;

  Avatar({
    super.key,
    this.onPressed,
    this.size = 28,
    this.addPhotoButton = true,
    this.imagePath,
    this.outlined = true,
    this.imageMemory,
    this.borderWidth = 2,
    this.borderColor = Colors.white,
    String? defaultAvatarPath,
  }) {
    this.defaultAvatarPath = defaultAvatarPath ?? Assets.svg.avatar;
  }

  @override
  State<Avatar> createState() => _Avatar();
}

class _Avatar extends State<Avatar> with AutomaticKeepAliveClientMixin {
  bool isClicked = false;
  late final GetImageUint8ListFromUrlUseCase _getImageUint8ListFromUrlUseCase;
  Future<Widget>? _networkImageFuture; // Variável para cache do Future

  _Avatar() {
    _getImageUint8ListFromUrlUseCase =
        Modular.get<GetImageUint8ListFromUrlUseCase>();
  }

  @override
  void initState() {
    super.initState();
    // Se a imagePath indicar uma URL de rede, cria o Future apenas uma vez
    if (widget.imagePath != null &&
        widget.imagePath!.isNotEmpty &&
        (widget.imagePath!.startsWith('http://') ||
            widget.imagePath!.startsWith('https://'))) {
      _networkImageFuture = getImageFromNetworkToUin8List();
    }
  }

  @override
  void didUpdateWidget(covariant Avatar oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Se a URL mudar, atualize o Future
    if (widget.imagePath != oldWidget.imagePath) {
      if (widget.imagePath != null &&
          widget.imagePath!.isNotEmpty &&
          (widget.imagePath!.startsWith('http://') ||
              widget.imagePath!.startsWith('https://'))) {
        _networkImageFuture = getImageFromNetworkToUin8List();
      } else {
        _networkImageFuture = null;
      }
    }
  }

  Future<Uint8List?> getUint8ListImageFromUrl() async {
    Uint8List? result;
    var res = await _getImageUint8ListFromUrlUseCase(widget.imagePath ?? '');
    res.fold((left) => print(left), (right) {
      result = right;
    });
    return result;
  }

  Widget getImageFromMemory({dynamic imageMemory}) => Image.memory(
        widget.imageMemory ?? imageMemory,
        width: widget.size,
        height: widget.size,
        fit: BoxFit.cover,
      );

  Widget getImageFromAssets({String? imagePath}) =>
      SvgPicture.asset(imagePath ?? widget.imagePath!,
          width: widget.size, height: widget.size, fit: BoxFit.cover);

  Widget getDefaultAvatarImageFromAsset() =>
      getImageFromAssets(imagePath: widget.defaultAvatarPath);

  Widget getImageFromFile() => Image.file(
        File(widget.imagePath!),
        width: widget.size,
        height: widget.size,
        fit: BoxFit.cover,
      );

  // Método para carregar a imagem da rede convertida para Uint8List
  Future<Widget> getImageFromNetworkToUin8List() async {
    Uint8List? res = await getUint8ListImageFromUrl();
    if (res != null) {
      return getImageFromMemory(imageMemory: res);
    }
    return getDefaultAvatarImageFromAsset();
  }

  Widget getCorrectImageWidget() {
    if (widget.imageMemory != null) {
      return getImageFromMemory();
    }
    if (widget.imagePath == null || widget.imagePath!.isEmpty) {
      return getDefaultAvatarImageFromAsset();
    }
    if (widget.imagePath!.startsWith('assets/')) {
      return getImageFromAssets();
    }
    if (widget.imagePath!.startsWith('http://') ||
        widget.imagePath!.startsWith('https://')) {
      // Usa o Future cacheado para evitar recriações
      return FutureBuilder<Widget>(
        future: _networkImageFuture,
        initialData: getDefaultAvatarImageFromAsset(),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.done &&
              snapshot.hasData) {
            return snapshot.data!;
          }
          return getDefaultAvatarImageFromAsset();
        },
      );
    }
    return getImageFromFile();
  }

  Widget getOutlinedImage(Widget child) {
    return Container(
      height: widget.size,
      decoration: BoxDecoration(
          border: Border.all(
            width: widget.borderWidth,
            color: widget.borderColor,
          ),
          borderRadius: const BorderRadius.all(Radius.circular(100))),
      child: child,
    );
  }

  Widget getImageWidget() {
    Widget image = getCorrectImageWidget();
    Widget correctImage = ClipRRect(
      borderRadius: BorderRadius.circular(60),
      child: image,
    );
    return widget.outlined ? getOutlinedImage(correctImage) : correctImage;
  }

  Widget getStack() {
    double addButtonSize = widget.size * 0.3154;
    if (addButtonSize < 17) {
      addButtonSize = 17;
    }
    return SizedBox(
      width: widget.size,
      height: widget.size,
      child: Stack(
        children: [
          getImageWidget(),
          Align(
            alignment: Alignment.bottomRight,
            child: Container(
              width: addButtonSize,
              height: addButtonSize,
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(30),
                  border: Border.all(width: 1, color: Colors.white)),
              child: CircleAvatar(
                backgroundColor: CustomColors.cultured,
                child: Icon(
                  QuyckyIcons.add_1,
                  color: CustomColors.primary,
                  size: widget.size * 0.18,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget getWidget() {
    return SizedBox(
      height: widget.size + 3,
      child: widget.addPhotoButton ? getStack() : getImageWidget(),
    );
  }

  void onPressed() {
    if (widget.onPressed != null) {
      widget.onPressed!();
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return GestureDetector(
      onTap: onPressed,
      child: getWidget(),
    );
  }

  @override
  bool get wantKeepAlive => true;
}
