import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

class QuyckyLoading extends StatefulWidget {
  const QuyckyLoading({super.key});

  @override
  _QuyckyLoading createState() => _QuyckyLoading();
}

class _QuyckyLoading extends State<QuyckyLoading>
    with TickerProviderStateMixin {
  late final AnimationController _controller = AnimationController(
    duration: const Duration(seconds: 1),
    vsync: this,
  )..repeat(reverse: true);
  late final Animation<double> _animation = CurvedAnimation(
    parent: _controller,
    curve: Curves.fastOutSlowIn,
  );
  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ScaleTransition(
      scale: _animation,
      child: Center(
        child: SizedBox(
          height: 214,
          child: SvgPicture.asset(
            'assets/img/svg/quycky_pin_fire.svg',
          ),
        ),
      ),
    );
  }
}
