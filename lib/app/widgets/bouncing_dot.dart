import 'package:flutter/material.dart';
import 'package:quycky/app/theme/colors.dart';

class BouncingDot extends StatefulWidget {
  final int index;
  final Color normalColor;
  final Color whenTopColor;

  const BouncingDot(
      {super.key,
      required this.index,
      required this.normalColor,
      required this.whenTopColor});

  @override
  _BouncingDotState createState() => _BouncingDotState();
}

class _BouncingDotState extends State<BouncingDot>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: Duration(milliseconds: 500),
      vsync: this,
    );

    _animation = Tween<double>(begin: 0, end: -10).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    )..addListener(() {
        setState(() {});
      });

    Future.delayed(Duration(milliseconds: 100 * widget.index), () {
      if (mounted) {
        _controller.repeat(reverse: true);
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    bool isAtTop = _animation.value <= -9.5; // Ajuste para detectar topo
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, _animation.value),
          child: Container(
            margin: EdgeInsets.symmetric(horizontal: 4),
            width: 10,
            height: 10,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: isAtTop ? widget.whenTopColor : widget.normalColor,
            ),
          ),
        );
      },
    );
  }
}
