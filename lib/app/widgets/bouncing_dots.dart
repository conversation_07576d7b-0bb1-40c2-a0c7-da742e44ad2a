import 'package:flutter/material.dart';
import 'package:quycky/app/theme/colors.dart';
import 'package:quycky/app/widgets/bouncing_dot.dart';

class BouncingDots extends StatelessWidget {
  final int dots;
  final Color normalColor;
  final Color whenTopColor;

  const BouncingDots({
    super.key,
    this.dots = 3,
    this.normalColor = CustomColors.melon_2,
    this.whenTopColor = CustomColors.primary,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(
          dots,
          (index) => BouncingDot(
              index: index,
              normalColor: normalColor,
              whenTopColor: whenTopColor)),
    );
  }
}
