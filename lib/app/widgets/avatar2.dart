import 'package:flutter/material.dart';
import 'package:quycky/app/theme/colors.dart';
import 'dart:math';

import 'package:quycky/core/utils/quycky_icons_icons.dart';

Iterable<Rect> getBounds(Rect rect, int length) sync* {
  final s = Size.square(rect.shortestSide / 6.5);
  final radius = (rect.shortestSide - s.shortestSide) * 0.40;
  for (var i = 0; i < length; i++) {
    /// distance +
    final angle = i * pi / 6 + pi * .01;
    final center = rect.center + Offset(cos(angle), sin(angle)) * radius;
    yield Rect.fromCenter(center: center, width: s.width, height: s.height);
  }
}

class AvatarBtnMultiChildLayoutDelegate extends MultiChildLayoutDelegate {
  final double iconSize;
  AvatarBtnMultiChildLayoutDelegate(this.iconSize);

  @override
  void performLayout(Size size) {
    int id = 1;
    getBounds(Offset(-size.width * 0.1, size.height * 0.1) & size, 1).forEach(
      (rect) {
        layoutChild(id, BoxConstraints.tight(Size(iconSize, iconSize)));
        positionChild(id, rect.centerRight);
        id++;
      },
    );
  }

  @override
  bool shouldRelayout(covariant AvatarBtnMultiChildLayoutDelegate oldDelegate) {
    return true;
  }
}

class Avatar2 extends StatefulWidget {
  final bool addPhotoButton;
  final double size;
  final String imagePath;
  final void Function()? onPressed;

  const Avatar2(
      {super.key,
      this.onPressed,
      this.size = 28,
      this.addPhotoButton = true,
      this.imagePath = "assets/img/png/avatar.png"});

  @override
  State<StatefulWidget> createState() => _Avatar2();
}

class _Avatar2 extends State<Avatar2> {
  double value = 300;
  final iconSize = 16.0;

  Widget getImageWidget() => ClipOval(
        child: Image.asset(
          widget.imagePath,
          width: widget.size,
          height: widget.size,
          fit: BoxFit.cover,
        ),
      );

  void onPressed() {
    widget.onPressed!();
  }

  Widget getWidget() {
    return CircleAvatar(
      radius: widget.size / 2,
      backgroundImage: AssetImage(widget.imagePath),
      child: CustomMultiChildLayout(
        delegate: AvatarBtnMultiChildLayoutDelegate(iconSize),
        children: widget.addPhotoButton
            ? [
                LayoutId(
                  id: 1,
                  child: const CircleAvatar(
                    backgroundColor: CustomColors.charmPink,
                    child: Icon(
                      QuyckyIcons.add,
                      color: Colors.white,
                      size: 16,
                    ),
                  ),
                )
              ]
            : [],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(onTap: onPressed, child: Center(child: getWidget()));
  }
}
