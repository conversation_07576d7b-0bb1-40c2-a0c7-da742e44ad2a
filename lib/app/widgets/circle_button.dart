import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

class CircleButton extends StatelessWidget {
  final void Function()? onTap;
  final String? imageUrl;
  final Color borderColor;
  final Color backgroundColor;
  final Color iconColor;
  final double iconSize;
  final IconData icon;
  final double? imageWidth;
  final double? imageHeight;
  final Widget? child;
  final double size;

  const CircleButton({
    super.key,
    this.size = 30,
    this.onTap,
    this.borderColor = Colors.transparent,
    this.imageUrl,
    this.imageWidth = 21,
    this.imageHeight = 21,
    this.backgroundColor = Colors.transparent,
    this.iconColor = Colors.black,
    this.iconSize = 38,
    this.icon = Icons.abc,
    this.child,
  });

  Widget getImageWidget() {
    String url = imageUrl ?? '';
    return SizedBox(
        height: 4,
        width: 4,
        child: url.contains('.svg') ? SvgPicture.asset(url) : Image.asset(url));
  }

  Widget getChild() {
    if (imageUrl != null) {
      return getImageWidget();
    }
    if (child != null) {
      return child!;
    }
    return Icon(
      icon,
      color: iconColor,
      size: iconSize,
    );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Align(
          alignment: Alignment.bottomRight,
          child: Container(
            padding: const EdgeInsets.all(4),
            margin: const EdgeInsets.all(4),
            width: size,
            height: size,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(100),
                color: backgroundColor,
                border: Border.all(width: 2, color: borderColor)),
            child: getChild(),
          )),
    );
  }
}
