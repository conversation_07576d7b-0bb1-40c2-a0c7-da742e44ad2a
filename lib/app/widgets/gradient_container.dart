import 'package:flutter/material.dart';
import 'package:quycky/app/theme/colors.dart';
import 'package:quycky/core/utils/box_decoration.dart';

class GradientContainer extends StatefulWidget {
  final Widget? child;
  final bool useDefault;
  // final bool useCustom;
  final double hotOpacity;
  final double coldOpacity;
  final double normalOpacity;
  final Color startColor;
  final Color endColor;
  final Widget? customBackground;

  const GradientContainer({
    super.key,
    this.child,
    // this.useCustom = false,
    this.useDefault = false,
    this.hotOpacity = 0,
    this.coldOpacity = 0,
    this.normalOpacity = 1,
    this.customBackground,
    this.startColor = CustomColors.orangeSoda,
    this.endColor = CustomColors.melon,
  });

  @override
  State<GradientContainer> createState() => _GradientContainer();
}

class _GradientContainer extends State<GradientContainer> {
  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Opacity(
          opacity: widget.hotOpacity,
          child: Container(
              width: double.infinity,
              height: double.infinity,
              decoration: getAppHot()),
        ),
        Opacity(
          opacity: widget.coldOpacity,
          child: Container(
              width: double.infinity,
              height: double.infinity,
              decoration: getAppCold()),
        ),
        Opacity(
          opacity: widget.normalOpacity,
          child: Container(
              width: double.infinity,
              height: double.infinity,
              decoration: getAppNormal()),
        ),
        // Opacity(
        //   opacity: widget.useDefault ? 1 : 0,
        //   child: Container(
        //       width: double.infinity,
        //       height: double.infinity,
        //       decoration: getAppDefault()),
        // ),
        Opacity(
          opacity: widget.useDefault ? 1 : 0,
          child: Container(
              width: double.infinity,
              height: double.infinity,
              decoration:
                  getAppCustomGradient(widget.startColor, widget.endColor)),
        ),
        Opacity(
          opacity: widget.customBackground != null ? 1 : 0,
          child: widget.customBackground ?? Container(),
        ),
        widget.child ?? Container()
      ],
    );
  }
}
