import 'package:flutter/services.dart';
// import 'package:flutter_asa_attribution/flutter_asa_attribution.dart';

class AppleSearchAdsAttribution {
  static Map<String, dynamic>? data;
  static String? token;
  // Platform messages are asynchronous, so we initialize in an async method.
  static Future<void> requestAppleAttributionDetails() async {
    // token = await FlutterAsaAttribution.instance.attributionToken();

    // try {
    //   data = await FlutterAsaAttribution.instance.requestAttributionDetails();
    // } on PlatformException {}
  }
}
