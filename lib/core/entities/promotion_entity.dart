// ignore_for_file: public_member_api_docs, sort_constructors_first

import 'dart:convert';

import 'package:equatable/equatable.dart';

class PromotionEntity extends Equatable {
  final String identifier;
  final String name;
  final String url;
  final String status;
  final hasAlreadyBeenOpened;
  final DateTime startTime;
  final int minutesToClose;

  const PromotionEntity({
    required this.identifier,
    required this.name,
    required this.url,
    this.status = 'waiting',
    this.minutesToClose = 0,
    required this.startTime,
    this.hasAlreadyBeenOpened = false,
  });

  @override
  List<Object> get props {
    return [
      identifier,
      name,
      url,
      status,
      startTime,
      minutesToClose,
    ];
  }

  PromotionEntity copyWith({
    String? identifier,
    String? name,
    String? url,
    String? status,
    bool? hasAlreadyBeenOpened,
    DateTime? startTime,
    int? minutesToClose,
  }) {
    return PromotionEntity(
        identifier: identifier ?? this.identifier,
        name: name ?? this.name,
        url: url ?? this.url,
        status: status ?? this.status,
        hasAlreadyBeenOpened: hasAlreadyBeenOpened ?? this.hasAlreadyBeenOpened,
        startTime: startTime ?? this.startTime,
        minutesToClose: minutesToClose ?? this.minutesToClose);
  }

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'identifier': identifier,
      'name': name,
      'url': url,
      'status': status,
      'startTime': startTime.millisecondsSinceEpoch,
      'hasAlreadyBeenOpened': hasAlreadyBeenOpened,
      'minutesToClose': minutesToClose
    };
  }

  factory PromotionEntity.fromMap(Map<String, dynamic> map) {
    return PromotionEntity(
      identifier: map['identifier'] as String,
      name: map['name'] as String,
      url: map['url'] as String,
      status: map['status'] as String,
      hasAlreadyBeenOpened: map['hasAlreadyBeenOpened'] as bool,
      startTime: DateTime.fromMillisecondsSinceEpoch(map['startTime'] as int),
      minutesToClose: map['minutesToClose'] as int,
    );
  }

  String toJson() => json.encode(toMap());

  factory PromotionEntity.fromJson(String source) =>
      PromotionEntity.fromMap(json.decode(source) as Map<String, dynamic>);

  @override
  bool get stringify => true;
}
