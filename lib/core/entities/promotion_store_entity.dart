// ignore_for_file: public_member_api_docs, sort_constructors_first
// To parse this JSON data, do
//
//     final promotionStoreEntity = promotionStoreEntityFromJson(jsonString);

import 'dart:convert';

import 'package:equatable/equatable.dart';

import 'package:quycky/core/entities/promotion_entity.dart';

class PromotionStoreEntity extends Equatable {
  final List<PromotionEntity> promotions;
  final bool isThereAnOpenModal;
  const PromotionStoreEntity(
      {this.promotions = const [], this.isThereAnOpenModal = false});

  @override
  List<Object> get props => [promotions];

  PromotionStoreEntity copyWith({
    List<PromotionEntity>? promotions,
    bool? isThereAnOpenModal,
  }) {
    return PromotionStoreEntity(
        promotions: promotions ?? this.promotions,
        isThereAnOpenModal: isThereAnOpenModal ?? this.isThereAnOpenModal);
  }

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'promotions': promotions.map((x) => x.toMap()).toList(),
    };
  }

  factory PromotionStoreEntity.fromMap(Map<String, dynamic> map) {
    return PromotionStoreEntity(
      promotions: List<PromotionEntity>.from(
        (map['promotions'] as List<dynamic>).map<PromotionEntity>(
          (x) => PromotionEntity.fromMap(x as Map<String, dynamic>),
        ),
      ),
    );
  }

  String toJson() => json.encode(toMap());

  factory PromotionStoreEntity.fromJson(String? source) =>
      source != null && source.isNotEmpty
          ? PromotionStoreEntity.fromMap(
              json.decode(source) as Map<String, dynamic>)
          : const PromotionStoreEntity();
}
