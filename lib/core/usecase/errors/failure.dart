import 'package:equatable/equatable.dart';

const defaultFailureMessage =
    'There was a failure, please check your connection and try again.';

abstract class Failure extends Equatable {
  final String message;
  const Failure({this.message = ''});
}

class ServerFailure extends Failure {
  const ServerFailure({message = ''}) : super(message: message);
  @override
  List<Object> get props => [];
}

class UnauthorizedFailure extends Failure {
  @override
  List<Object> get props => [];
}

class GenericFailure extends Failure {
  const GenericFailure({message = ''}) : super(message: message);
  @override
  List<Object> get props => [];
}
