import 'package:equatable/equatable.dart';

class ServerException extends Equatable implements Exception {
  final String? message;

  const ServerException([this.message]);

  @override
  List<Object?> get props => [message];

  @override
  String toString() {
    return message ?? 'An unknown server error occurred.';
  }
}

class UnauthorizedException extends Equatable implements Exception {
  final String? message;
  const UnauthorizedException([this.message]);

  @override
  List<Object?> get props => [message];

  @override
  String toString() {
    return message ?? 'An unknown server error occurred.';
  }
}
