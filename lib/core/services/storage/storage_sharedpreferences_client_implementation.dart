
import 'package:quycky/core/services/storage/storage_client.dart';
import 'package:shared_preferences/shared_preferences.dart';

const container = 'quycky_data';

class StorageSharedPreferencesClientImplementation implements StorageClient {
  late SharedPreferences instance;

  StorageSharedPreferencesClientImplementation() : super() {
    init();
  }

  void init() async {
    instance = await SharedPreferences.getInstance();
  }

  @override
  Future<String> read(String key, {String? container}) async {
    // final instance = await SharedPreferences.getInstance();
    return (await SharedPreferences.getInstance()).getString(key) ?? '';
    //  GetStorage(container ?? AppStorageKeys.defaultContainer).read<T>(key);
  }

  @override
  Future<bool> write(String key, String data) async {
    return (await SharedPreferences.getInstance()).setString(key, data);
    // return GetStorage(container ?? AppStorageKeys.defaultContainer).write(key, data);
  }

  @override
  Future<bool> erase() async {
    return (await SharedPreferences.getInstance()).clear();
  }
}
