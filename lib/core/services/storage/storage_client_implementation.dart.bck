import 'package:localstorage/localstorage.dart';
import 'package:quycky/app/utils/app_storage_keys.dart';

const container = 'quycky_data';

class StorageClientImplementation {
  static T? read<T>(String key, {String? container}) {
    return LocalStorage(container ?? AppStorageKeys.defaultContainer)
        .getItem(key);
  }

  static Future<void> write(String key, dynamic data, {String? container}) {
    return LocalStorage(container ?? AppStorageKeys.defaultContainer)
        .setItem(key, data);
  }

  static Future<void> eraseContainer({String? container}) {
    return LocalStorage(container ?? AppStorageKeys.defaultContainer).clear();
  }
}
