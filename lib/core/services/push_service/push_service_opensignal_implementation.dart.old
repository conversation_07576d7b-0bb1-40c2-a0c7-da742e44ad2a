import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:onesignal_flutter/onesignal_flutter.dart';
import 'package:quycky/app/features/friendship/domain/usecases/accept_invite_usecase.dart';
import 'package:quycky/app/features/game/presenter/storage/game_storage.dart';
import 'package:quycky/app/features/user/presenter/controllers/user_controller.dart';
import 'package:quycky/app/features/user/presenter/storage/user_storage.dart';
import 'package:quycky/core/services/push_service/push_service.dart';
import 'package:quycky/core/utils/app_env.dart';
import 'package:quycky/core/utils/interval_utils.dart';
import 'package:quycky/core/utils/use_cases/open_to_invited_game_room_usecase.dart';

class PushServiceOpensignalImplementation extends PushService {
  PushServiceOpensignalImplementation(
    GameStorage gameStorage,
    UserStorage userStorage,
    AcceptInviteUseCase acceptInviteUseCase,
    OpenToInvitedGameRoomUseCase openToInvitedGameRoomUseCaseUseCase,
    UserController userController,
  ) : super(gameStorage, userStorage, acceptInviteUseCase,
            openToInvitedGameRoomUseCaseUseCase, userController) {
    startPushService();
  }

  void startPushService() {
    if (kIsWeb) {
      return;
    }
    setListeners();
    _getToken();
  }

  Future<bool> getPermissions() async {
    bool res = await OneSignal.shared
        .promptUserForPushNotificationPermission(fallbackToSettings: true);
    print("Accepted permission: $res");
    return res;
    /**.then((accepted) {
      print("Accepted permission: $accepted");
    });**/
  }

  setListeners() async {
    if (AppEnv.isDevMode) {
      OneSignal.shared.setLogLevel(OSLogLevel.verbose, OSLogLevel.none);
    }

    OneSignal.shared.setAppId(AppEnv.onesignalKey);

    getPermissions();

    OneSignal.shared.setNotificationWillShowInForegroundHandler(
        (OSNotificationReceivedEvent event) {
      // event.complete(event.notification);
      onNotificationEvent(event.notification, onReceiveNotificationForeground);
    });

    OneSignal.shared
        .setNotificationOpenedHandler((OSNotificationOpenedResult event) {
      onNotificationEvent(event.notification, onTapNotificationBackground);
    });

    OneSignal.shared.setPermissionObserver((OSPermissionStateChanges changes) {
      print("Changes Permissions==>${changes.jsonRepresentation()}");
    });

    OneSignal.shared
        .setSubscriptionObserver((OSSubscriptionStateChanges changes) {
      // Will be called whenever the subscription changes
      // (ie. user gets registered with OneSignal and gets a user ID)
      Map<String, dynamic> data = json.decode(changes.jsonRepresentation());
      if (data.containsKey('to') && data['to'].containsKey('userId')) {
        pushServiceKey = data['to']['userId'] ?? '';
      }
    });
  }

  onNotificationEvent(
      OSNotification event,
      void Function(
        String type,
        String message,
        Map<String, dynamic> data,
      ) func) {
    try {
      String type = event.additionalData?["notification_type"] ?? '';
      String message = event.body ?? '';
      Map<String, dynamic> data = event.additionalData!.containsKey('data') &&
              event.additionalData!['data'] != null
          ? json.decode(event.additionalData?['data'])
          : {};
      func(type, message, data);
    } catch (e) {
      print('err==>$e');
    }
  }

  void _getToken() async {
    if (pushServiceKey != '') return;

    OneSignal.shared.getDeviceState().then((v) {
      if (v?.userId == null || v?.userId == '') {
        _callGetToken();
        return;
      }

      pushServiceKey = v?.userId ?? '';
    }).catchError((onError) {
      print("err=>$onError");
      _callGetToken();
    });
  }

  void _callGetToken() {
    setTimeout(callback: _getToken, duration: const Duration(seconds: 2));
  }
}
