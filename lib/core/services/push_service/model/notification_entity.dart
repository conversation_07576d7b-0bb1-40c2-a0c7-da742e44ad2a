class NotificationEntity {
  final String type;
  final String title;
  final String message;
  final Map<String, dynamic> data;

  NotificationEntity({required this.type, required this.title, required this.message, required this.data});

  factory NotificationEntity.fromMap(Map<String, dynamic> data) {
    return NotificationEntity(type: data['type']??'', title: data['title']??'',message: data['message']??'', data: data['data']??{});
  }
}