import 'dart:convert';

class AppNotification {
  final String title;
  final String message;
  final Content content;

  AppNotification({
    required this.title,
    required this.message,
    required this.content,
  });

  AppNotification copyWith({
    String? title,
    String? message,
    Content? content,
  }) =>
      AppNotification(
        title: title ?? this.title,
        message: message ?? this.message,
        content: content ?? this.content,
      );

  factory AppNotification.fromJson(String str) =>
      AppNotification.fromMap(json.decode(str));

  String toJson() => json.encode(toMap());

  factory AppNotification.fromMap(Map<String, dynamic> json) => AppNotification(
        title: json["title"],
        message: json["message"],
        content: Content.fromMap(json["content"]),
      );

  Map<String, dynamic> toMap() => {
        "title": title,
        "message": message,
        "content": content.toMap(),
      };
}

class Content {
  final String goToPage;
  final String openUrl;
  final String extraData;

  Content({
    required this.goToPage,
    required this.openUrl,
    this.extraData = '',
  });

  Content copyWith({
    String? goToPage,
    String? openUrl,
    String? extraData,
  }) =>
      Content(
        goToPage: goToPage ?? this.goToPage,
        openUrl: openUrl ?? this.openUrl,
        extraData: extraData ?? this.extraData,
      );

  factory Content.fromJson(String str) => Content.fromMap(json.decode(str));

  String toJson() => json.encode(toMap());

  factory Content.fromMap(Map<String, dynamic> json) {
    return Content(
      goToPage: json["go_to_page"] ?? '',
      openUrl: json["open_url"] ?? '',
      extraData: json["extra_data"] ?? '',
    );
  }

  Map<String, dynamic> toMap() => {
        "go_to_page": goToPage,
        "open_url": openUrl,
        "extra_data": extraData,
      };
}
