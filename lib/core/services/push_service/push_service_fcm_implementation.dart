import 'dart:convert';

import 'package:quycky/core/services/notification_service/local_notifications_service.dart';
import 'package:quycky/core/services/push_service/abstract_push_service.dart';
import 'package:quycky/core/services/push_service/model/notification_entity.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:quycky/core/services/push_service/push_service.dart';

class PushServiceFCMImplementation extends PushService
    implements AbstractPushService {
  PushServiceFCMImplementation(
    super.gameStorage,
    super.userStorage,
    super.acceptInviteUseCase,
    super.openToInvitedGameRoomUseCaseUseCase,
    super.userController,
  ) {
    startPushService();
  }

  void startPushService() {
    getPermissions();
    setListeners();
    getToken();
    verifyForInitialMessage();
    startNotificationListeners();
  }

  void updateToken(String newToken) {
    pushServiceKey = newToken;
  }

  void startTokenRefreshListener() {
    FirebaseMessaging.instance.onTokenRefresh.listen((event) {
      updateToken(event);
    });
  }

  verifyForInitialMessage() async {
    final event = await FirebaseMessaging.instance.getInitialMessage();
    final receivedEvent = FCMBackgroundMessageHandle.remoteMessage ??
        (event != null && event.data.containsKey('type') ? event : null);
    if (receivedEvent != null) {
      onNotificationEvent(receivedEvent, onReceiveNotificationForeground,
          openingApp: true);
    }
    FCMBackgroundMessageHandle.remoteMessage = null;
  }

  setListeners() async {
    FirebaseMessaging.onBackgroundMessage(firebaseMessagingBackgroundHandler);

    FirebaseMessaging.onMessageOpenedApp.listen((event) {
      onNotificationEvent(event, onReceiveNotificationForeground,
          openingApp: true);
    });

    FirebaseMessaging.onMessage.listen((event) {
      onNotificationEvent(event, onReceiveNotificationForeground);
    });
  }

  onNotificationEvent(
      RemoteMessage event,
      void Function(String type, String? message, Map<String, dynamic> data,
              {bool openingApp})
          func,
      {openingApp = false}) {
    String type = event.data.containsKey('type') ? event.data['type'] : '_';
    String? message = event.notification?.body;
    Map<String, dynamic> data =
        event.data.containsKey('data') ? json.decode(event.data['data']) : {};
    func(type, message, data, openingApp: openingApp);
  }

  getPermissions() async {
    FirebaseMessaging messaging = FirebaseMessaging.instance;
    await messaging.setForegroundNotificationPresentationOptions(
      alert: false, // Não mostra o banner/alerta em primeiro plano.
      badge: true, // Permite atualizar o badge do app.
      sound: true, // Permite tocar o som.
    );

    NotificationSettings settings = await messaging.requestPermission(
      alert: true,
      announcement: false,
      badge: true,
      carPlay: false,
      criticalAlert: false,
      provisional: false,
      sound: true,
    );
  }

  @override
  Future<void> deleteToken() async {
    return FirebaseMessaging.instance.deleteToken();
  }

  @override
  Future<String> getToken() async {
    String? res = await FirebaseMessaging.instance.getToken();
    updateToken(res ?? '');
    return pushServiceKey;
  }

  onTapBackgroundNotification() {}
}

class FCMBackgroundMessageHandle {
  static NotificationEntity? notificationEntity;
  static RemoteMessage? remoteMessage;
}
