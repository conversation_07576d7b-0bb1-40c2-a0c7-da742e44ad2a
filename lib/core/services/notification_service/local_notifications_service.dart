import 'dart:convert';

import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:quycky/core/services/push_service/model/notification_entity.dart';
import 'package:quycky/core/services/push_service/push_service.dart';
import 'package:quycky/core/utils/interval_utils.dart';
import 'package:quycky/firebase_options.dart';

Future<void> onDidReceiveNotificationResponse(
    NotificationResponse notificationResponse) async {
  final String? payload = notificationResponse.payload;
  if (payload != null) {
    var data = json.decode(payload);
    NotificationEntity notificationData = NotificationEntity.fromMap(data);
    PushService.notificationState.setNotification(notificationData);
    print("--->${DateTime.now()}==.1");
  }
}

@pragma('vm:entry-point')
Future<void> firebaseMessagingBackgroundHandler(RemoteMessage event) async {
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);

  try {
    // await LocalNotificationService.initialize();
    String type = event.data.containsKey('type') ? event.data['type'] : '_';
    String message = event.notification?.body ?? '';
    Map<String, dynamic> data =
        event.data.containsKey('data') ? json.decode(event.data['data']) : {};
    PushService.onBackgroundMessage(event.hashCode, type, message, data);
  } catch (err) {
    print('>Err:=>$err');
  }
}

@pragma('vm:entry-point')
void didReceiveBackgroundNotificationResponseHandler(
    NotificationResponse notificationResponse) async {
  final String? payload = notificationResponse.payload;
  if (payload != null) {
    var data = json.decode(payload);
    NotificationEntity notificationData = NotificationEntity.fromMap(data);
    setTimeout(
        duration: const Duration(seconds: 1),
        callback: () =>
            PushService.notificationState.setNotification(notificationData));
  }
}

class LocalNotificationService {
  static bool isFlutterLocalNotificationsInitialized = false;
  static late FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin;
  static const AndroidNotificationChannel channel = AndroidNotificationChannel(
    'quycky_high_importance_channel', // id
    'Quycky High Importance Notifications', // title
    description:
        'This channel is used for quycky app important notifications.', // description
    importance: Importance.high,
  );

  static Future<void> initialize() async {
    if (isFlutterLocalNotificationsInitialized) {
      return;
    }

    _flutterLocalNotificationsPlugin =
        FlutterLocalNotificationsPlugin(); //await getFlutterLocalNotificationsPlugin();

    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');
    final DarwinInitializationSettings iniTest = DarwinInitializationSettings();
    final DarwinInitializationSettings initializationSettingsDarwin =
        DarwinInitializationSettings();
    /**
            onDidReceiveLocalNotification:
                (int id, String? title, String? message, String? payload) =>
                    onDidReceiveNotificationResponse(NotificationResponse(
                        notificationResponseType:
                            NotificationResponseType.selectedNotification,
                        id: id,
                        actionId: title,
                        input: message,
                        payload: payload)) */
    final InitializationSettings initializationSettings =
        InitializationSettings(
            android: initializationSettingsAndroid,
            iOS: initializationSettingsDarwin);
    await _flutterLocalNotificationsPlugin.initialize(initializationSettings,
        onDidReceiveBackgroundNotificationResponse:
            didReceiveBackgroundNotificationResponseHandler,
        onDidReceiveNotificationResponse: onDidReceiveNotificationResponse);

    _flutterLocalNotificationsPlugin
        .getNotificationAppLaunchDetails()
        .then((value) async {
      if (!value!.didNotificationLaunchApp) return;
      if (value.notificationResponse!.payload != null) {
        didReceiveBackgroundNotificationResponseHandler(
            value.notificationResponse!);
      }
    });
    await _flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(channel);

    await FirebaseMessaging.instance
        .setForegroundNotificationPresentationOptions(
      alert: true,
      badge: true,
      sound: true,
    );

    isFlutterLocalNotificationsInitialized = true;
  }

  void onDidReceiveLocalNotification(
      int id, String? title, String? body, RemoteMessage? payload) async {}

  static NotificationDetails getNotificationDetails() {
    AndroidNotificationDetails androidNotificationDetails =
        AndroidNotificationDetails(
      channel.id,
      channel.name,
      channelDescription: channel.description,
      importance: Importance.max,
      priority: Priority.high,
    );
    const DarwinNotificationDetails darwinNotificationDetails =
        DarwinNotificationDetails(
            threadIdentifier: 'quycky_thread_notifications');
    return NotificationDetails(
        iOS: darwinNotificationDetails, android: androidNotificationDetails);
  }

  static Future<void> showNotification(
      int id, String title, String message, String? payload) async {
    try {
      NotificationDetails notificationDetails = getNotificationDetails();
      await _flutterLocalNotificationsPlugin
          .show(id, title, message, notificationDetails, payload: payload);
    } catch (err) {}
  }
}
