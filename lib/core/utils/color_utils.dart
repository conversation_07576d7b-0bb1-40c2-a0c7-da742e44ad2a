import 'dart:math';

import 'package:flutter/material.dart';

HSLColor rgbToHsl(Color color) {
  double r = color.r;
  double g = color.g;
  double b = color.b;

  double maxColor = max(r, max(g, b));
  double minColor = min(r, min(g, b));
  double delta = maxColor - minColor;

  double h = 0;
  double s = 0;
  double l = (maxColor + minColor) / 2;

  if (delta != 0) {
    if (maxColor == r) {
      h = ((g - b) / delta + (g < b ? 6 : 0)) * 60;
    } else if (maxColor == g) {
      h = ((b - r) / delta + 2) * 60;
    } else {
      h = ((r - g) / delta + 4) * 60;
    }

    s = delta / (1 - (2 * l - 1).abs());
  }

  return HSLColor.fromAHSL(1.0, h, s, l);
}

Color hslToRgb(HSLColor hslColor) {
  double h = hslColor.hue / 360;
  double s = hslColor.saturation;
  double l = hslColor.lightness;

  double r, g, b;

  if (s == 0) {
    r = g = b = l;
  } else {
    double hue2rgb(double p, double q, double t) {
      if (t < 0) t += 1;
      if (t > 1) t -= 1;
      if (t < 1 / 6) return p + (q - p) * 6 * t;
      if (t < 1 / 2) return q;
      if (t < 2 / 3) return p + (q - p) * (2 / 3 - t) * 6;
      return p;
    }

    double q = l < 0.5 ? l * (1 + s) : l + s - l * s;
    double p = 2 * l - q;
    r = hue2rgb(p, q, h + 1 / 3);
    g = hue2rgb(p, q, h);
    b = hue2rgb(p, q, h - 1 / 3);
  }

  return Color.fromARGB(
      255, (r * 255).round(), (g * 255).round(), (b * 255).round());
}

Color generateLighterColorHSL(Color baseColor) {
  HSLColor hslBaseColor = rgbToHsl(baseColor);

  double lightnessIncrement = 0.3;
  double newLightness =
      (hslBaseColor.lightness + lightnessIncrement).clamp(0.0, 1.0);

  HSLColor hslLighterColor = hslBaseColor.withLightness(newLightness);
  return hslToRgb(hslLighterColor);
}
