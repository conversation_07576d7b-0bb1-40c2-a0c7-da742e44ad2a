import 'package:flutter/material.dart';

class AnimatedOpacityWidget extends StatefulWidget {
  final Widget child;
  final bool showAnimation;

  const AnimatedOpacityWidget({super.key, this.child = const SizedBox(), this.showAnimation = true});
  @override
  _AnimatedOpacityWidgetState createState() => _AnimatedOpacityWidgetState();
}

class _AnimatedOpacityWidgetState extends State<AnimatedOpacityWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();

    _controller = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    )..repeat(reverse: true);

    _animation = Tween<double>(begin: 0.3, end: 0.6).animate(_controller);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.showAnimation) {
      _controller.stop(canceled: true);
    }
    return widget.showAnimation ? AnimatedBuilder(
      animation: _animation,
      builder: (BuildContext context, Widget? child) {
        return Container(
          child: Opacity(
            opacity: _animation.value,
            child: widget.child,
          ),
        );
      },
    ) : widget.child;
  }
}
