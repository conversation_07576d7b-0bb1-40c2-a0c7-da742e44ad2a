import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:quycky/app/presenter/app_widget.dart';
import 'package:quycky/app/widgets/dialog_get_image_from.dart';

class ShowDialogGetImage {
  ShowDialogGetImage({
    required void Function(XFile? file) onOk,
  }) {
    BuildContext context = AppWidget.globalKey.currentState!.context;
    showGeneralDialog(
      context: context,
      barrierDismissible: true,
      transitionDuration: const Duration(milliseconds: 300),
      barrierLabel: MaterialLocalizations.of(context).dialogLabel,
      barrierColor: Colors.black.withOpacity(0.5),
      pageBuilder: (context, _, __) => getDialogGetImageFrom(onOk),
      transitionBuilder: getTransitionBuilder,
    );
  }

  Widget getDialogGetImageFrom(void Function(XFile? file) onOk) {
    Map<Symbol, dynamic> args = {#onOk: onOk};
    return Function.apply(DialogGetImageFrom.new, [], args);
  }

  Widget getTransitionBuilder(BuildContext context, Animation<double> animation,
      Animation<double> secondaryAnimation, Widget child) {
    Offset begin = const Offset(0, -1);
    begin = (const Offset(0, 1));

    return SlideTransition(
      position: CurvedAnimation(
        parent: animation,
        curve: Curves.easeOut,
      ).drive(Tween<Offset>(
        begin: begin,
        end: Offset.zero,
      )),
      child: child,
    );
  }
}
