import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:quycky/core/utils/assets_strings.dart';

enum EAppRunMode {
  development('DEVELOPMENT'),
  production('PRODUCTION'),
  staging('STAGING');

  final String label;
  const EAppRunMode(this.label);

  static EAppRunMode fromString(String label) {
    return values.firstWhere(
      (v) => v.label == label,
      orElse: () => EAppRunMode.development,
    );
  }
}

class _EnvStrings {
  static String mode = 'MODE';
  static String appVersion() => 'APP_VERSION';
  static String apiUrl(EAppRunMode mode) => 'API_URL_${mode.label}';
  static String apiPort(EAppRunMode mode) => 'API_PORT_${mode.label}';
  static String apiVersion(EAppRunMode mode) => 'API_VERSION_${mode.label}';
  static String socketUrl(EAppRunMode mode) => 'SOCKET_URL_${mode.label}';
  static String socketPort(EAppRunMode mode) => 'SOCKET_PORT_${mode.label}';
  static String oneSignalKey(EAppRunMode mode) => 'ONESIGNAL_KEY_${mode.label}';
}

class AppEnv {
  static Future<void> initialize() async {
    return dotenv.load(fileName: Assets.envFile);
  }

  static String _apiUrl = '';
  static set apiUrl(String value) => _apiUrl = value;
  static String _socketUrl = '';
  static set socketUrl(String value) => _socketUrl = value;

  static String _get(String envName) => dotenv.env[envName] ?? '';
  static double get minimumLayoutHeight => 685;
  static EAppRunMode get mode =>
      EAppRunMode.fromString(AppEnv._get(_EnvStrings.mode));
  static String get apiUrl => _apiUrl.isNotEmpty
      ? _apiUrl
      : AppEnv._get(_EnvStrings.apiUrl(AppEnv.mode));
  static String get appVersion => AppEnv._get(_EnvStrings.appVersion());
  static String get apiPort => AppEnv._get(_EnvStrings.apiPort(AppEnv.mode));
  static String get apiVersion =>
      AppEnv._get(_EnvStrings.apiVersion(AppEnv.mode));
  static String get socketUrl => _socketUrl.isNotEmpty
      ? _socketUrl
      : AppEnv._get(_EnvStrings.socketUrl(AppEnv.mode));
  static String get socketPort =>
      AppEnv._get(_EnvStrings.socketPort(AppEnv.mode));
  static String get onesignalKey =>
      AppEnv._get(_EnvStrings.oneSignalKey(AppEnv.mode));
  static bool get isProdMode => AppEnv.mode == EAppRunMode.production;
  static bool get isStagingMode => AppEnv.mode == EAppRunMode.staging;
  static bool get isDevMode => AppEnv.mode == EAppRunMode.development;
  static String get formattedApiUrl =>
      '${AppEnv.apiUrl}:${AppEnv.apiPort}/${AppEnv.apiVersion}/';
  static String get formattedSocketUrl =>
      '${AppEnv.socketUrl}:${AppEnv.socketPort}';
}
