import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:quycky/app/presenter/app_widget.dart';
import 'package:quycky/app/features/user/domain/entities/user_entity.dart';
import 'package:quycky/app/widgets/dialog_friend_menu.dart';

class ShowDialogFriendMenu {
  ShowDialogFriendMenu({
    required UserEntity user,
    Uint8List? playerImageMemoryData,
    void Function()? onPressed,
  }) {
    BuildContext context = AppWidget.globalKey.currentState!.context;
    showGeneralDialog(
      context: context,
      barrierDismissible: true,
      transitionDuration: const Duration(milliseconds: 500),
      barrierLabel: MaterialLocalizations.of(context).dialogLabel,
      barrierColor: Colors.black.withOpacity(0.5),
      pageBuilder: (context, _, __) =>
          getAppMessage(user, playerImageMemoryData),
      transitionBuilder: getTransitionBuilder,
    );
  }

  Widget getAppMessage(UserEntity user, Uint8List? playerImageMemoryData) {
    Map<Symbol, dynamic> args = {
      #user: user,
      #playerImageMemoryData: playerImageMemoryData
    };
    return Function.apply(DialogFriendMenu.new, [], args);
  }

  Widget getTransitionBuilder(BuildContext context, Animation<double> animation,
      Animation<double> secondaryAnimation, Widget child) {
    Offset begin = const Offset(0, -1);
    begin = (const Offset(0, 1));

    return SlideTransition(
      position: CurvedAnimation(
        parent: animation,
        curve: Curves.easeOut,
      ).drive(Tween<Offset>(
        begin: begin,
        end: Offset.zero,
      )),
      child: child,
    );
  }
}
