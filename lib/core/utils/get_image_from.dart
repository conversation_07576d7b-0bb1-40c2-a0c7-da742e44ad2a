import 'dart:typed_data';
import 'package:image/image.dart' as imageLib;
import 'package:image_picker/image_picker.dart';

Future<Uint8List> getUint8ListFromImage(String imagePath) async {
  final image = await imageLib.decodeImageFile(imagePath);
  final bytes = image!.toUint8List();
  return bytes.buffer.asUint8List();
}

class PickImage {
  XFile? _image;

  final ImagePicker _picker = ImagePicker();

  PickImage();

  Future<XFile?> getImageFrom(
      {bool camera = true,
      imageQuality = 45,
      double maxWidth = 500.0,
      double maxHeight = 500.0}) async {
    XFile? res;
    try {
      final XFile? pickedFile = await _picker.pickImage(
          source: camera ? ImageSource.camera : ImageSource.gallery,
          imageQuality: imageQuality,
          maxHeight: maxHeight,
          maxWidth: maxWidth);
      _image = pickedFile;
      res = pickedFile;
    } catch (e) {
      print('ImageErr:==>$e');
    }
    return res;
  }
}
