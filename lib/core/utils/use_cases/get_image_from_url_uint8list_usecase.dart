// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:typed_data';
import 'package:http/http.dart' as http;
import 'package:dartz/dartz.dart';
import 'package:quycky/core/usecase/errors/failure.dart';
import 'package:quycky/core/usecase/usecase.dart';

class GetImageUint8ListFromUrlUseCase implements UseCase<Uint8List, String> {
  Future<Either<Failure, Uint8List>> doGet(String url) async {
    Uint8List res = Uint8List(0);
    try {
      var response = await http.get(Uri.parse(url));
      if (response.statusCode == 200) {
        res = response.bodyBytes;
        return Right(res);
      }
      print('Image download fail. Status Code: ${response.statusCode}');
      return const Left(ServerFailure());
    } catch (e) {
      print('Fail on image download: $e');
      return const Left(ServerFailure());
    }
  }

  @override
  Future<Either<Failure, Uint8List>> call(String url) async {
    return await doGet(url);
  }
}
