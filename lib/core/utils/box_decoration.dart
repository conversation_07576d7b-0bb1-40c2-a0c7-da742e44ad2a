import 'package:flutter/material.dart';
import 'package:quycky/app/theme/colors.dart';

BoxDecoration getDefault() {
  return const BoxDecoration(
    gradient: LinearGradient(
        begin: FractionalOffset(0.0, 0.01),
        end: FractionalOffset(0.5, 1),
        colors: [
          CustomColors.americanPurple,
          CustomColors.cinnamonSatin,
          CustomColors.chineseWhite,
        ],
        stops: [
          0,
          0.5,
          1.0,
        ]),
  );
}

BoxDecoration getDefaultBck() {
  return const BoxDecoration(
    gradient: LinearGradient(
        begin: FractionalOffset(0.0, 0),
        end: FractionalOffset(0, 1),
        colors: [
          CustomColors.americanPurple,
          CustomColors.cinnamonSatin,
          CustomColors.chineseWhite,
        ],
        stops: [
          0,
          0.5,
          1.0,
        ]),
  );
}

BoxDecoration getAppNormal() {
  //pg2
  return const BoxDecoration(
    gradient: LinearGradient(
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
        colors: [
          CustomColors.cinnamonSatin,
          CustomColors.americanPurple,
          CustomColors.americanPurple,
          CustomColors.cinnamonSatin,
          CustomColors.chineseWhite,
          CustomColors.chineseWhite,
          CustomColors.chineseWhite,
        ],
        stops: [
          0,
          0,
          0,
          0,
          0.99,
          1,
          1
        ]),
  );
}

BoxDecoration getAppCold2() {
  //pg1
  return const BoxDecoration(
    gradient: LinearGradient(
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
        colors: [
          CustomColors.americanPurple,
          CustomColors.cinnamonSatin,
          CustomColors.chineseWhite
        ],
        stops: [
          0.04,
          0.4,
          0.8
        ]),
  );
}

// BoxDecoration getAppDefault() {
//   //pg1
//   return const BoxDecoration(
//     gradient: LinearGradient(
//         begin: Alignment.topCenter,
//         end: Alignment.bottomCenter,
//         colors: [
//           CustomColors.americanPurple2,
//           CustomColors.charmPink,
//         ],
//         stops: [
//           0.08,
//           0.8,
//         ]),
//   );
// }

BoxDecoration getAppCold() {
  //pg1
  return const BoxDecoration(
    gradient: LinearGradient(
        begin: FractionalOffset(0.1, 0),
        end: FractionalOffset(0, 1),
        colors: [
          CustomColors.americanPurple,
          CustomColors.charmPink,
          CustomColors.chineseWhite,
          CustomColors.chineseWhite
        ],
        stops: [
          0,
          0.5104,
          0.99,
          1
        ]),
  );
}

/// /gradient(180deg, # 0%, # 51.04%, # 99.99%, # 100%
BoxDecoration getAppHot() {
  //pg3
  return const BoxDecoration(
    gradient: LinearGradient(
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
        colors: [
          CustomColors.orangeSoda,
          CustomColors.orangeSoda,
          CustomColors.orangeSoda,
          CustomColors.orangeSoda,
          CustomColors.chineseWhite
        ],
        stops: [
          0,
          0.01,
          0.02,
          0.03,
          1
        ]),
  );
}

BoxDecoration getAppDefaultTemp() {
  //pg3
  return const BoxDecoration(
    gradient: LinearGradient(
      colors: [CustomColors.orangeSoda, CustomColors.cultured],
      stops: [0.5, 1],
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
    ),
  );
}

BoxDecoration getAppDefault() {
  return const BoxDecoration(
    gradient: LinearGradient(
      colors: [
        CustomColors.orangeSoda, // Adjust colors as needed
        CustomColors.melon
      ],
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
    ),
  );
}

BoxDecoration getAppCustomGradient(Color color1, Color color2) {
  return BoxDecoration(
    gradient: LinearGradient(
      colors: [
        color1, // Adjust colors as needed
        color2
      ],
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
    ),
  );
}
