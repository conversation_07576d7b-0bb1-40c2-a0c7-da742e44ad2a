import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:quycky/analytics.dart';
import 'package:quycky/app/features/home/<USER>/pages/widgets/promotion_gift_notice.dart';
import 'package:quycky/app/features/home/<USER>/store/promotion_store.dart';
import 'package:quycky/app/presenter/app_widget.dart';
import 'package:quycky/app/theme/colors.dart';
import 'package:quycky/app/widgets/button.dart';
import 'package:quycky/core/entities/promotion_entity.dart';
import 'package:url_launcher/url_launcher.dart';

class ShowPromotionDialog {
  ShowPromotionDialog(PromotionEntity promotionData) {
    var promotionStore = Modular.get<PromotionStore>();
    if (promotionStore.state.isThereAnOpenModal) {
      return;
    }
    promotionStore.setIsOpenedModal(true);

    BuildContext context = AppWidget.globalKey.currentState!.context;

    try {
      showGeneralDialog(
        context: context,
        barrierDismissible: true,
        transitionDuration: const Duration(milliseconds: 500),
        barrierLabel: MaterialLocalizations.of(context).dialogLabel,
        barrierColor: Colors.black.withOpacity(0.5),
        pageBuilder: (context, _, __) => getBody(promotionData),
        transitionBuilder: getTransitionBuilder,
      ).whenComplete(() => promotionStore.setIsOpenedModal(false));
      final temp = promotionData.copyWith(hasAlreadyBeenOpened: true);
      promotionStore.changePromotionData(temp);
    } catch (e) {
      print(e);
    }
  }

  Widget getBody(PromotionEntity promotionData) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          width: 326,
          constraints: const BoxConstraints(maxWidth: 326, minHeight: 359),
          margin: const EdgeInsets.symmetric(horizontal: 16),
          decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.all(Radius.circular(30))),
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 35),
            child: Column(children: [
              const Text('LEVEL UP\nYOUR SEX LIFE!',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                      letterSpacing: 1.2,
                      fontFamily: "Roboto",
                      fontWeight: FontWeight.bold,
                      color: CustomColors.americanPurple,
                      decoration: TextDecoration.none,
                      fontSize: 20)),
              Padding(
                padding: const EdgeInsets.only(top: 20.0, bottom: 20),
                child: PromotionGiftNotice(
                    showLabelEndTime: true, promotion: promotionData),
              ),
              SizedBox(
                width: 222,
                child: Button(
                  autoSized: true,
                  onPressed: () => _launchURL(promotionData.url),
                  text: 'CLAIM PRIZE',
                ),
              )
            ]),
          ),
        ),
      ],
    );
  }

  Widget getTransitionBuilder(BuildContext context, Animation<double> animation,
      Animation<double> secondaryAnimation, Widget child) {
    Offset begin = const Offset(0, -1);

    return SlideTransition(
      position: CurvedAnimation(
        parent: animation,
        curve: Curves.easeOut,
      ).drive(Tween<Offset>(
        begin: begin,
        end: Offset.zero,
      )),
      child: child,
    );
  }

  _launchURL(String url) async {
    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url));
      await Analytics.instance.logEvent(name: 'promo_endgame0_launch');
    } else {
      throw 'Could not launch $url';
    }
  }
}
