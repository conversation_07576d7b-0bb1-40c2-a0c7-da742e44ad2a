import 'dart:convert';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:flutter/foundation.dart';

bool stringToBoolean(String str, [bool strict = false]) {
  if (strict == true) {
    return str == '1' || str == 'true';
  }
  return str != '0' && str != 'false' && str != '';
}

double dynamicToDouble(dynamic value) {
  double? res = double.tryParse(value.toString());
  return res ?? 0;
}

int dynamicToInt(dynamic value) {
  int? res = int.tryParse(value.toString());
  return res ?? 0;
}

String decimalToPercentageConverter(double value, {fractionDigits = 0}) {
  var val = (value * 100).toStringAsFixed(fractionDigits);
  return val;
}

Future<File> uint8ListToFile(Uint8List data) async {
  Directory tempDir = await getTemporaryDirectory();
  String tempPath = tempDir.path;

  String fileName = '${DateTime.now().millisecondsSinceEpoch}.jpg';
  File file = File('$tempPath/$fileName');

  await file.writeAsBytes(data);

  return file;
}

String dynamicToString(dynamic value) {
  if (value == null) return '';
  String res = value.toString();
  return res;
}

Map<String, dynamic> dynamicToMap(dynamic value) {
  if (value == null || value == 'null') return ({});
  try {
    return json.decode(value) as Map<String, dynamic>;
  } catch (err) {
    return {};
  }
}
