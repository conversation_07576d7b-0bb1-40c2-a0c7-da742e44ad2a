buildscript {
    ext.kotlin_version = '1.9.23'
//    ext.kotlin_version = '1.9.23' // 6.10'
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:7.1.2'
        // START: FlutterFire Configuration
        classpath 'com.google.gms:google-services:4.3.10'
        // END: FlutterFire Configuration
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:2.1.0"
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
    }
    subprojects {
    afterEvaluate { project ->
        // check only for "com.android.library" to not modify
        // your "app" subproject. All plugins will have "com.android.library" plugin, and only your app "com.android.application"
        // Change your application's namespace in main build.gradle and in main android block.

        if (project.plugins.hasPlugin("com.android.library")) {
            project.android {
                if (namespace == null) {
                    namespace project.group
                }
            }
        }
    }
    }
}

rootProject.buildDir = '../build'
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(':app')
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
