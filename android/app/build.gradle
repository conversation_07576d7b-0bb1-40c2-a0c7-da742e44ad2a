plugins {
    id "com.android.application"
    // START: FlutterFire Configuration
    id 'com.google.gms.google-services'
    // END: FlutterFire Configuration
    id "kotlin-android"
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id "dev.flutter.flutter-gradle-plugin"
}

allprojects {
    subprojects {
        afterEvaluate { project ->
            if (project.hasProperty('android')) {
                project.android {
                    if (namespace == null) {
                        namespace = "${project.group}.${project.name}"
                    }
                }
            }
        }
    }
}
android {
    namespace "com.playfultech.quycky.app"
    signingConfigs {
        debug {
            storeFile file('../../certifications/Quycky.keystore')
            storePassword '120120'
            keyAlias 'Quycky'
            keyPassword '120120'
        }
        release {
            storeFile file('../../certifications/Quycky.keystore')
            storePassword '120120'
            keyAlias 'Quycky'
            keyPassword '120120'
        }
    }
    compileSdkVersion 35 //flutter.compileSdkVersion
    compileSdk = 35
    ndkVersion "25.1.8937393" // flutter.ndkVersion

    // compileOptions {        coreLibraryDesugaringEnabled true
    //     sourceCompatibility JavaVersion.VERSION_1_8
    //     targetCompatibility JavaVersion.VERSION_1_8
    // }

    compileOptions {coreLibraryDesugaringEnabled true
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = "17" // Alterado de 21 para 17
    }

    // kotlinOptions {
    //     jvmTarget = '1.8'
    // }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId "com.playfultech.quycky.app"
        // You can update the following values to match your application needs.
        // For more information, see: https://docs.flutter.dev/deployment/android#reviewing-the-build-configuration.
        minSdkVersion 23 //flutter.minSdkVersion
        versionCode flutter.versionCode
        versionName flutter.versionName
        multiDexEnabled true
    }

    buildTypes {
        release {
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
            signingConfig signingConfigs.release
        }
    }
}

flutter {
    source '../..'
}

dependencies {
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.0.3'
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
    implementation 'com.facebook.android:facebook-android-sdk:[4,5)' //Added Custom
}
