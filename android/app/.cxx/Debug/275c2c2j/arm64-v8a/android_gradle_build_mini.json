{"buildFiles": ["/opt/development/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Projects/flutter/quycky/neo-frontend/android/app/.cxx/Debug/275c2c2j/arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Projects/flutter/quycky/neo-frontend/android/app/.cxx/Debug/275c2c2j/arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}