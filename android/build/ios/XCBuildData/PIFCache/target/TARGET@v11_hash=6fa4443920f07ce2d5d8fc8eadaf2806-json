{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9876b6f1ed94386c0d433699d03d7a1893", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/SwiftyGif/SwiftyGif-prefix.pch", "INFOPLIST_FILE": "Target Support Files/SwiftyGif/SwiftyGif-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/SwiftyGif/SwiftyGif.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "SwiftyGif", "PRODUCT_NAME": "SwiftyGif", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9841d5b5f584355bf5389fee14b0ea4dcf", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986d93d1b4d049e58efaccedcad07cd6aa", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/SwiftyGif/SwiftyGif-prefix.pch", "INFOPLIST_FILE": "Target Support Files/SwiftyGif/SwiftyGif-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/SwiftyGif/SwiftyGif.modulemap", "PRODUCT_MODULE_NAME": "SwiftyGif", "PRODUCT_NAME": "SwiftyGif", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f4601c3b2ec2732ecf9e3176697370e7", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986d93d1b4d049e58efaccedcad07cd6aa", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/SwiftyGif/SwiftyGif-prefix.pch", "INFOPLIST_FILE": "Target Support Files/SwiftyGif/SwiftyGif-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/SwiftyGif/SwiftyGif.modulemap", "PRODUCT_MODULE_NAME": "SwiftyGif", "PRODUCT_NAME": "SwiftyGif", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9802f45dbf04103758d500714d2198b45b", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9810b1911df8caf319d07d0de35b348287", "guid": "bfdfe7dc352907fc980b868725387e98b8e4c9a415d42c94c784d8679a3076ca", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9830566a27722040d497f30e0d48fbc237", "guid": "bfdfe7dc352907fc980b868725387e98e0bb3ff9cc5c587ddb373d1ff76c4bfa", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98279428a039955dbf90c4ed599ce1e2f6", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98066f9a3db08192ef5914b8ee632e23e1", "guid": "bfdfe7dc352907fc980b868725387e98958e156f92cd549133fbe356ae177318"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2abed87ff09c46cbf81ccd484dba2d9", "guid": "bfdfe7dc352907fc980b868725387e98c2ac03608f7d974aeb4a7f25ca561aed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a3e6d302f1e5a0b53a4f72bdaf469c2", "guid": "bfdfe7dc352907fc980b868725387e985dc0581f9b251a113a115ba9c43f0c69"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d2d007861eabdb387c2e62f8d65b1b5", "guid": "bfdfe7dc352907fc980b868725387e989b6c60fa8c3a5af0bf718a4afcd8495e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e123a333f8a5076d3ececd4b17b0a37", "guid": "bfdfe7dc352907fc980b868725387e9825efe9d9d58506fa9b44201f7a5c39d5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dddf21ae0c2834428e99a5d3d8ab9d8a", "guid": "bfdfe7dc352907fc980b868725387e983e2429aab27066668ddd2af9168239a1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c62c755260544ab171923b68a1dfd88", "guid": "bfdfe7dc352907fc980b868725387e98c80281b64bd9a4f12a3af98403028fb8"}], "guid": "bfdfe7dc352907fc980b868725387e98e9517d4224550a148b83446d6023aec7", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98374e3ec4dc398dcbdb656bf148272974", "guid": "bfdfe7dc352907fc980b868725387e98b88612c9fb33b30e36f7392ecc75542b"}], "guid": "bfdfe7dc352907fc980b868725387e9843b2d8c2d1c0b00e2cef2d936eb3a489", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e984e29ecfd741643ae88b767e22f13d72a", "targetReference": "bfdfe7dc352907fc980b868725387e98f5cd644fc2aeb8654450a2168f52697c"}], "guid": "bfdfe7dc352907fc980b868725387e98c7a69b042f9441d9b20612c54f7cc571", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98f5cd644fc2aeb8654450a2168f52697c", "name": "SwiftyGif-SwiftyGif"}], "guid": "bfdfe7dc352907fc980b868725387e9872eabefc63c14dfe52fb0c95ad90294e", "name": "SwiftyGif", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98290968646de0d07c6f6e2ed9e146ea78", "name": "SwiftyGif.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}