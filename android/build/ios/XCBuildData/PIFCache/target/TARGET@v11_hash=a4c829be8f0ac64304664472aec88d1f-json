{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9824e44aa5bd9b36cfaa3c7d3de6454508", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/GoogleSignIn/GoogleSignIn-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleSignIn/GoogleSignIn.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleSignIn", "PRODUCT_NAME": "GoogleSignIn", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "4.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984a9186989285bd998fb972ce277f4838", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981c6011158bf13e25ad4f8d64bc194506", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/GoogleSignIn/GoogleSignIn-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleSignIn/GoogleSignIn.modulemap", "PRODUCT_MODULE_NAME": "GoogleSignIn", "PRODUCT_NAME": "GoogleSignIn", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "4.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b37a1f6412452e7499975f0eaf21275e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981c6011158bf13e25ad4f8d64bc194506", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/GoogleSignIn/GoogleSignIn-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleSignIn/GoogleSignIn.modulemap", "PRODUCT_MODULE_NAME": "GoogleSignIn", "PRODUCT_NAME": "GoogleSignIn", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "4.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ac2174ea8e92e04cafb435524caf8e4e", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98cd2bef1b747a71cc82b42b9ef381694f", "guid": "bfdfe7dc352907fc980b868725387e98408e096957263a5dd2d57544ac81f90a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c43cdac0cea9106ecd840d49464d62de", "guid": "bfdfe7dc352907fc980b868725387e98689e8f5d9d65ad21e5b61ec30f1f39dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982df102d79d042c728ead41d735168734", "guid": "bfdfe7dc352907fc980b868725387e98e496c19c18612182233ceac967d55125"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd3a450ce450e90cd9046296dc746fb6", "guid": "bfdfe7dc352907fc980b868725387e98e88385533b65402ab909295f8d8bf483", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98544a86ab2e46ba3d6b35aa2d8441f7ba", "guid": "bfdfe7dc352907fc980b868725387e9804394cb5e1d579817d12669031158fcc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf2398ad686ad4a8a92d39271041c15f", "guid": "bfdfe7dc352907fc980b868725387e98534ce7ae30363a6aeb511bc43f68b82d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e69e8908819be66a47acaf5e0664e8c", "guid": "bfdfe7dc352907fc980b868725387e98e193f510114a0ed25adbdb086812d328", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c16882d05eaf18494641f4399cf6e6a1", "guid": "bfdfe7dc352907fc980b868725387e98806918b911a08c1e453bb6f8ec6b8e5f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98449ea8a1cfc36ab7cb675d386ec5f7ca", "guid": "bfdfe7dc352907fc980b868725387e984a99f8973bf06fd7ecd48f47ea4d7b5c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981982039bc85440d297a1661412fa3a37", "guid": "bfdfe7dc352907fc980b868725387e9865ab1f93c3e5dd8b94e8bedd8d50d57c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98134a553939b4d2369b69a7bc0100f1d5", "guid": "bfdfe7dc352907fc980b868725387e988b2ae869c841d48f7621a75452f333a6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987bd94a1922f3af690da8f01b5f213672", "guid": "bfdfe7dc352907fc980b868725387e988f34148a1805194b0e49f7beb7274ccd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a23b818cf58e5d1c1f734e0a5b35d870", "guid": "bfdfe7dc352907fc980b868725387e98ce08dc527a765043bcb0e25778b28a66"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db2188774f4b428525808bc6eaea75ff", "guid": "bfdfe7dc352907fc980b868725387e98a842eade1e03a1eacccbb6112e601f97"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876488701701533a16ede2b805622fe05", "guid": "bfdfe7dc352907fc980b868725387e98ec400b99e867ead36fb85a9ddc0dea3f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9839aad74f2dfdb2dc4a6f309c33a4034c", "guid": "bfdfe7dc352907fc980b868725387e98670eb1e671f51e4c05821918eb010481"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a02992067c02d52df8a1b7512cc8c3b9", "guid": "bfdfe7dc352907fc980b868725387e98381389c6c65e012bc3f84136e47d6551", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98992469652a4b1ddd25d375d26040c854", "guid": "bfdfe7dc352907fc980b868725387e98890af04b86b386ff1c6ed75d7a84b324"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf65f03113dcecf9502a24193536e249", "guid": "bfdfe7dc352907fc980b868725387e98eafe250e158a9866c042425cfe724ad5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce3e3bc49d2e1955b7907dbad8031f27", "guid": "bfdfe7dc352907fc980b868725387e98461b232e8ab9cb8320153ac40cf18b4e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f8b14ace77367ae7b67c0489117274e", "guid": "bfdfe7dc352907fc980b868725387e9898ff37f713170e9552f08fa30531b46c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98575cf4c35ccfdb6650832937adca24de", "guid": "bfdfe7dc352907fc980b868725387e986835b8796a4c055252a4fffbf1d9c89e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db4367a63ab48bd285577da39cc5cb68", "guid": "bfdfe7dc352907fc980b868725387e988cfb0d6febc566948737750f5dd9d5d3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db2849418f62c7322069fbf4449b42a9", "guid": "bfdfe7dc352907fc980b868725387e98d0ed8d0f8eb8a337aaf65c9f90d461a7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b423fa8296d02286f3f986a05e3fdf6", "guid": "bfdfe7dc352907fc980b868725387e98138d4f541ae5519074e993023016073f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9829155918c3774053580a53c0c2bc1d36", "guid": "bfdfe7dc352907fc980b868725387e98776136c9ef87dff6a0c1e949f8c38ea8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b7e3a4787331373cd42ba1fd0fa7164", "guid": "bfdfe7dc352907fc980b868725387e98f8eb522b67fbb3d7943718519bbd4bde", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980fa4fe456de748938fb9d268dfaf848b", "guid": "bfdfe7dc352907fc980b868725387e98d13bb27cd7ed169779d20b8c5f0b74bc"}], "guid": "bfdfe7dc352907fc980b868725387e98808e660503e89e55ed3cf0726d416c64", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981fb49933655cfe9f118a67e4065bae19", "guid": "bfdfe7dc352907fc980b868725387e9813289d7416f058b8bc83a87b0c859a7f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df79da2e9984d8a609dfbdce2d04f7ce", "guid": "bfdfe7dc352907fc980b868725387e98a9ad811653e6da9c8d58d9707852e492"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980bb74a8ee4756cc4610d645d992f582f", "guid": "bfdfe7dc352907fc980b868725387e98b9d8f09917866faa3437e3f849511ae6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98948d99f8f507030c5fca3cebccd4d719", "guid": "bfdfe7dc352907fc980b868725387e988a4806d5c4d243010b2a2ca456edc3e4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983579244ecc1b84e4f49d94eb020a49d3", "guid": "bfdfe7dc352907fc980b868725387e9875c77e2f0223b6a6cb60562c80152dd2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98186d3e368f366990f78cae817e8e6d0e", "guid": "bfdfe7dc352907fc980b868725387e98f41dec7891271341fc51ca9cd9b3de7f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a836ca74b4ffdea4901b695580284fc", "guid": "bfdfe7dc352907fc980b868725387e981158f7a402e3cf0e924b5ad2ccf21cbe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835e19aabeda370ae67dcd91f838199c5", "guid": "bfdfe7dc352907fc980b868725387e988b25285695893c413044e3634fd52702"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ea4abe0c04a44eecea655642187c8b5", "guid": "bfdfe7dc352907fc980b868725387e98d4824ec9d4112954acb01a6b3f2608f6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a66dcceee2cb1d90be106ec971efe94d", "guid": "bfdfe7dc352907fc980b868725387e98eeb59f21666a8287935a608091e475ff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec609f5f1ac7a4ba92aa7f452a0f49f7", "guid": "bfdfe7dc352907fc980b868725387e98dd7346778ce40a9d9eff6785a27f08c2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e43fb5708e26496ada18826ab9b7acc", "guid": "bfdfe7dc352907fc980b868725387e980de7382e22de2367c8a8ff9283195972"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836367b5d6f7244fdd111977e1e0de8d7", "guid": "bfdfe7dc352907fc980b868725387e98eb9aab8515725e70bf47b290f2ffbf9b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814924b61d136818c0ab03e9d06300c4d", "guid": "bfdfe7dc352907fc980b868725387e9887170e2e2f0e1231e8f66fc27d5d75a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852fa669c30a275ea57e44041aa2e3712", "guid": "bfdfe7dc352907fc980b868725387e9895f19af6f6285e65ba1a6fc39d8d33fd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9891394a02d3d3e540970848407f97b918", "guid": "bfdfe7dc352907fc980b868725387e98151e8f2dcd937577d9c166ff24e38103"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe352fa00d60e6c610a3f2a1a3fe1afc", "guid": "bfdfe7dc352907fc980b868725387e98adfcc487798a61542fe811b996202b8c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1123ace0d1b8d08e4707a8fad2d3b13", "guid": "bfdfe7dc352907fc980b868725387e98bfc667348db266d8236ddf09fe3f687c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f7fa3497645d793bb4c9352ac7d34ef", "guid": "bfdfe7dc352907fc980b868725387e989bf3642b2c1c77a0defaabcbb23f9bd1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98568ba9bd2d4666b503b14ad472b3407b", "guid": "bfdfe7dc352907fc980b868725387e9814ca939d7ef14a75c492b565d9ff81d4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983992bc8a5a8b006d19287c0e1c364f85", "guid": "bfdfe7dc352907fc980b868725387e98bcea5a5e9b0795d87ad828228aae02c4"}], "guid": "bfdfe7dc352907fc980b868725387e98af7c298017aa361478f3e0825042a7ea", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984e89532af4959bc02744f6df272de777", "guid": "bfdfe7dc352907fc980b868725387e98eaea5eaf3300cd8b30cd03886bb61628"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ce99edcae3e9a72a19e294b7de12c9f", "guid": "bfdfe7dc352907fc980b868725387e989f4403d826ead7e7553d2be609e28404"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98374e3ec4dc398dcbdb656bf148272974", "guid": "bfdfe7dc352907fc980b868725387e98e4ac94164d6b5fc457d5f175a5efda42"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7508ffcadf84a42c3966e29f5754acf", "guid": "bfdfe7dc352907fc980b868725387e98342fc37c026beffd967da85b92328ff1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98974e1232357fe39e8cbe1022d2c8bc9f", "guid": "bfdfe7dc352907fc980b868725387e9877f340a3cd66ec724d38f2560ed29b57"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d63ad33f357e9346c3b5060dfe50403", "guid": "bfdfe7dc352907fc980b868725387e98d6420868497ac30ec74b165bada2ea66"}], "guid": "bfdfe7dc352907fc980b868725387e98fef37a92c516d72d02a173ef676fb740", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98fbb22bb0f5869f65ac700b94ebefa304", "targetReference": "bfdfe7dc352907fc980b868725387e9832c61b747d3949a8e639c0653b6048d5"}], "guid": "bfdfe7dc352907fc980b868725387e98a8d455f06110aff21c29e94460d34072", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98758cc842172da540ffb591e63e38dc1e", "name": "AppAuth"}, {"guid": "bfdfe7dc352907fc980b868725387e980be6c76e7b3dde057d7e3e6ad61f30d4", "name": "GTMAppAuth"}, {"guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher"}, {"guid": "bfdfe7dc352907fc980b868725387e9832c61b747d3949a8e639c0653b6048d5", "name": "GoogleSignIn-GoogleSignIn"}], "guid": "bfdfe7dc352907fc980b868725387e989b0ee9a6d93c0cfa024bbc34a88b2122", "name": "GoogleSignIn", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9815509a5aa54606eda7171e744ada7414", "name": "GoogleSignIn.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}