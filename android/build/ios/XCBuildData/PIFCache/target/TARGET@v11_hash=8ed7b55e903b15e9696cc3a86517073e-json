{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a76a969135c9ca0ea2be96a6b191e782", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/opt/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/opt/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_local_notifications/flutter_local_notifications-prefix.pch", "INFOPLIST_FILE": "Target Support Files/flutter_local_notifications/flutter_local_notifications-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_local_notifications/flutter_local_notifications.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_local_notifications", "PRODUCT_NAME": "flutter_local_notifications", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c5a683f5d3fe3a9b7c2a6271968f4395", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ebf040f985f939838002cca79a40b716", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/opt/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/opt/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_local_notifications/flutter_local_notifications-prefix.pch", "INFOPLIST_FILE": "Target Support Files/flutter_local_notifications/flutter_local_notifications-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_local_notifications/flutter_local_notifications.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_local_notifications", "PRODUCT_NAME": "flutter_local_notifications", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987e3341e62a265f02e36e7cf019908381", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ebf040f985f939838002cca79a40b716", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/opt/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/opt/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_local_notifications/flutter_local_notifications-prefix.pch", "INFOPLIST_FILE": "Target Support Files/flutter_local_notifications/flutter_local_notifications-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_local_notifications/flutter_local_notifications.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_local_notifications", "PRODUCT_NAME": "flutter_local_notifications", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98bd6cc9e90eb346b6dde6a77e9ba664d7", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c11663689c2f8b3b3f4231dfa526a8d0", "guid": "bfdfe7dc352907fc980b868725387e98293b666ea007e6dfac8605abfb04bcae", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855383ae0f179202e759b42abc179123e", "guid": "bfdfe7dc352907fc980b868725387e983ee4935a5830d436729bf9333cafe951", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d0c6823308bfe91b8dc788d0dd0181da", "guid": "bfdfe7dc352907fc980b868725387e98fd7e065a57fe261583985ff8a28441b2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b8a8f9be0a5c44d6185df874d912ccdc", "guid": "bfdfe7dc352907fc980b868725387e989226ef652dc2c9f79cef30cf49903714", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98866502b759dda1eea9b1e9a4e2d5a882", "guid": "bfdfe7dc352907fc980b868725387e9856d42ca3b9fa6c749f7a48b2afc8a41f", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9830363fd3ed62d942fefc75fc5c47d316", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b2307ca3e1afbaad3a214baa2b8540aa", "guid": "bfdfe7dc352907fc980b868725387e987208cdc05dd2c05236ff2ab97fc8cad5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988007102106d8db5d65a47a138adf6fcc", "guid": "bfdfe7dc352907fc980b868725387e986ea48f3b4c370a652686199d9e8c7ae2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ac740fa0f83c09e9373bf80e07b109d", "guid": "bfdfe7dc352907fc980b868725387e98efb5d007405c5dace1d7c4ee26a94956"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819046c26503e4485270c8d1311aeafd2", "guid": "bfdfe7dc352907fc980b868725387e9891968f81e227d87f746bbfd3655d5dc7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d055bfc75ad34d441a65371907f39fe", "guid": "bfdfe7dc352907fc980b868725387e98270d4d13552b4ac6fade824b18f99c10"}], "guid": "bfdfe7dc352907fc980b868725387e987cb96b0da5a32f552867df7410477e8a", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98374e3ec4dc398dcbdb656bf148272974", "guid": "bfdfe7dc352907fc980b868725387e98f9e9bfe4edc54a6bef5735d77758f1b4"}], "guid": "bfdfe7dc352907fc980b868725387e9881842722d0f78140c7135d9f9a7bceeb", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e984243cfb2cf2763469a82f71ffead81d5", "targetReference": "bfdfe7dc352907fc980b868725387e987434bc9491ab71790f372f1bf966d056"}], "guid": "bfdfe7dc352907fc980b868725387e982f2d6bf16e0ea060b87821ba5246f2a8", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e987434bc9491ab71790f372f1bf966d056", "name": "flutter_local_notifications-flutter_local_notifications_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f49b18868e69e442795477541dae1d9f", "name": "flutter_local_notifications", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b85276e53a4583cee7a183c9d2fc893d", "name": "flutter_local_notifications.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}