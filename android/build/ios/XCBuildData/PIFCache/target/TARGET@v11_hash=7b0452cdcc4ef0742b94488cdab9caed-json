{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98db3f69efdd42d6177723ece8025cd37f", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/shared_preferences_foundation", "DEVELOPMENT_TEAM": "3H8G5N4B57", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "shared_preferences_foundation", "INFOPLIST_FILE": "Target Support Files/shared_preferences_foundation/ResourceBundle-shared_preferences_foundation_privacy-shared_preferences_foundation-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "shared_preferences_foundation_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98dd671857127c8d0fe8a7fdb385642176", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986c9f975da35980c8f7c8f59aac38124d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/shared_preferences_foundation", "DEVELOPMENT_TEAM": "3H8G5N4B57", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "shared_preferences_foundation", "INFOPLIST_FILE": "Target Support Files/shared_preferences_foundation/ResourceBundle-shared_preferences_foundation_privacy-shared_preferences_foundation-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "PRODUCT_NAME": "shared_preferences_foundation_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98fc66d559ea1a3f1e3daece9ae2fe0bcd", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986c9f975da35980c8f7c8f59aac38124d", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/shared_preferences_foundation", "DEVELOPMENT_TEAM": "3H8G5N4B57", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "shared_preferences_foundation", "INFOPLIST_FILE": "Target Support Files/shared_preferences_foundation/ResourceBundle-shared_preferences_foundation_privacy-shared_preferences_foundation-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "PRODUCT_NAME": "shared_preferences_foundation_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e989d69c7ba8395992925c1453c751c5045", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e985cecf0a4491e1e02a6a520fa8bdf1621", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e982c88d96fd06130844f4c5db2ead1eee5", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982478030de7e5df3f7cbead841e180dd4", "guid": "bfdfe7dc352907fc980b868725387e983d13630b86d719c9806843b46585e310"}], "guid": "bfdfe7dc352907fc980b868725387e9833bbad8d9d5cc764081b2b2a19619f8e", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e98e0be3b0d5ad56f1985578b1f97431765", "name": "shared_preferences_foundation-shared_preferences_foundation_privacy", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ad625504a4c1e61077bbfd33bd1d1785", "name": "shared_preferences_foundation_privacy.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}