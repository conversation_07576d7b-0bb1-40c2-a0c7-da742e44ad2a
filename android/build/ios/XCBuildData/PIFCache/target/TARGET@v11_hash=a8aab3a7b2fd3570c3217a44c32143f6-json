{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988292da609066450f7db18a036815cb48", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989acc1e18c299b7d033f994f6a61aa69d", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9871aac01320c383886c976e343f26044a", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9820760191f67ac2d223addaf7bdd43aa6", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9871aac01320c383886c976e343f26044a", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98888aada59c7a388625f9b46fb1caa635", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9858a9b84d2457d7a32eaec1e90d9fa668", "guid": "bfdfe7dc352907fc980b868725387e98b8e074cf36c125d9994049b3afe05c94", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f2da2a4dba6785e3aecbcb4c5bfbd97", "guid": "bfdfe7dc352907fc980b868725387e98e439f63fa703038e8d57163da3162ec0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0fd9940391619355055492c6849676c", "guid": "bfdfe7dc352907fc980b868725387e98c68fc45ba978123cf27ca24959b7bf64", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c543a89eb792bf54835f860501249b0f", "guid": "bfdfe7dc352907fc980b868725387e987ce4833163c630a87a332c68235ef815", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a99f70cab57fa047db9d1756fb6873b4", "guid": "bfdfe7dc352907fc980b868725387e98dcda28212e5192637f27dafbe2719d9a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b486844cf901d27a27cf427e55ef4681", "guid": "bfdfe7dc352907fc980b868725387e988b3d28010c57c0b6b148371249f5d445", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9881c32ea1233906cd4cf83e2ffccd3bf0", "guid": "bfdfe7dc352907fc980b868725387e98d8c08c28b96c13513be5550a3156db2e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d974a664e752e383e5e6a9e5e200b50a", "guid": "bfdfe7dc352907fc980b868725387e98862bdef1d52078110bc6614eb174fc32", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d96cc94ae6bbd5918c461fd2274667cd", "guid": "bfdfe7dc352907fc980b868725387e98a262ff6446e1439b16414cb2989c1f68", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c7b3d240a057a5e8ea8ec032932b60d", "guid": "bfdfe7dc352907fc980b868725387e98616b851d346df8f625f02470b8048000", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9853b638980d0d17e0f81838cad39093c6", "guid": "bfdfe7dc352907fc980b868725387e9833a68458fdd6fc0305a12e67d95a6f13", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9845f3832bae04c4273b51caf0b5a0f2af", "guid": "bfdfe7dc352907fc980b868725387e98376988cfdfaaa05b64ea8b4624843d67", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ce2d2cae5cd7af48cdb0b0997eae1c2", "guid": "bfdfe7dc352907fc980b868725387e98ff423ec593b1f89f544b078d5999e070", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c7350839d0df28ac96d0f9be0fdc67e", "guid": "bfdfe7dc352907fc980b868725387e98cbc42f711df5bd5a59068799a978f47f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98712232c967a35a4412f07ee53670f873", "guid": "bfdfe7dc352907fc980b868725387e98197aba107ec2d58c709b00ec7cd805ba", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984c3164a029c6f803b64bb29fb405e8ae", "guid": "bfdfe7dc352907fc980b868725387e98dbdd34c83633522a64fe6839b999305b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9887775c861e2715d0f30dab50032bad05", "guid": "bfdfe7dc352907fc980b868725387e98232b37cce96f429ffc4511828fc1537d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981011c045513957695868a1e200ad1ac0", "guid": "bfdfe7dc352907fc980b868725387e98828e4d39884e35b81fde28e8a4d7c2fb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988505e219fdd497cc4c18ce6c81514a1e", "guid": "bfdfe7dc352907fc980b868725387e984635825f95ae71a5fd81ee0d36f8178d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98073b6dffcc400dcd85063a3c2a08d112", "guid": "bfdfe7dc352907fc980b868725387e98b72378e19eb81b02bc5675c89e781b59", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9e7cc14f607b9987a3c8884bd0ec64d", "guid": "bfdfe7dc352907fc980b868725387e98fc0245b7e89ba749b8f3ee7b7ececd4d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d79c857a9e1f83ad49951d7d342f280", "guid": "bfdfe7dc352907fc980b868725387e985aa158006ca741d1d6026502d4e96f91", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e986e45d729320706498879f0cb71bb65d1", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9856d3f3db317c164d673b1e6b22694958", "guid": "bfdfe7dc352907fc980b868725387e9878845b01968418de9fea5b088dd61dbf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888bf685a3b3b2bdfaf7b8a667a0c524c", "guid": "bfdfe7dc352907fc980b868725387e9845e4d26d7cf6f6caa39af94721644e70"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f46ddfed18a2e0a33c377f3cf1eb2864", "guid": "bfdfe7dc352907fc980b868725387e9822ccfa0f7df13936c431bbc247be4fb2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0e025d27577ac6570a58d3143adf97d", "guid": "bfdfe7dc352907fc980b868725387e984d36fcd8a1291b60b1f0ac8592c90454"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a46a6c11f65e08f0a7ccc11d7d8664a9", "guid": "bfdfe7dc352907fc980b868725387e986cc36df08bb3a52aa91a55f5646e3039"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b56b6c36136405b0ac79f37a2781fd07", "guid": "bfdfe7dc352907fc980b868725387e98ef710f609a83f4fd5599f6a732dc88f9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855273b0cd5523eb8d288223d09d64308", "guid": "bfdfe7dc352907fc980b868725387e9872044ac0711f24b7103a40768cb76bd9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9867477e0e3d84fefbb6c13550c0be32ae", "guid": "bfdfe7dc352907fc980b868725387e98f9e02df8cf350b6ca8c396dfa724716f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9892452dbbd1bd684e785e427dc655119d", "guid": "bfdfe7dc352907fc980b868725387e989fea1fd626c4bf392591a3fdc013c512"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9839c05a56e1b7f452be40ccc0ec38ff8a", "guid": "bfdfe7dc352907fc980b868725387e98e154ffa4350f1467c2f494003c9238d9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f866659d797426a1d470fd00659f7d2", "guid": "bfdfe7dc352907fc980b868725387e9831c393b98bedba1e2fd0dafebc537cb9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9883a151ad4af0de77b070f482095184d1", "guid": "bfdfe7dc352907fc980b868725387e980a31158c364292ddcd2f51de966700d3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988bec6b344e70a867679b908a8550fd6b", "guid": "bfdfe7dc352907fc980b868725387e98ddd224fb7ecb15f0e3f9332b45bc3860"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9865ceb41dcb2ab80f49f1abcc5956e889", "guid": "bfdfe7dc352907fc980b868725387e98ba8fec983b54d25dd9b8f971ffdeb715"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98945b08e74224319e0796faaf40be5f2c", "guid": "bfdfe7dc352907fc980b868725387e98d1b68b3614759e181291322f9972addd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a56d0bdba08e9a73bb823e14b372a5d8", "guid": "bfdfe7dc352907fc980b868725387e9807cdcd619c76a25651c10473c42c3832"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9899e1a9f8bec2e83fac3ca6d1dec75ff0", "guid": "bfdfe7dc352907fc980b868725387e98309c517d58f2c7b2712553cc4f0c0909"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9808302aa5146222b935679b81de316614", "guid": "bfdfe7dc352907fc980b868725387e98e65f90fe382feeb7d95b4b9a4d8ae334"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a736ed9f0a1a233d74d5778ed65a9856", "guid": "bfdfe7dc352907fc980b868725387e98bfbef63605cfcdc2d7ada5867a84114b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98083780c4eaf8db601d76cae26d1991e5", "guid": "bfdfe7dc352907fc980b868725387e98bd7a3473cc6c0f1bf5048978fa7e1393"}], "guid": "bfdfe7dc352907fc980b868725387e981d00d7af739ad869d57af2dd49015d4f", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98374e3ec4dc398dcbdb656bf148272974", "guid": "bfdfe7dc352907fc980b868725387e98fc228180b32bb76486eb26582bf71ff3"}], "guid": "bfdfe7dc352907fc980b868725387e98c12f4cbebe789bc7fd7eaf2527b96163", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9878b322a8d1e10e029227dac591d1b690", "targetReference": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823"}], "guid": "bfdfe7dc352907fc980b868725387e98cc9037f20702461a5abb4e957461c9c3", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823", "name": "PromisesObjC-FBLPromises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981c795e45f8d875aac88217c6a2a95faa", "name": "FBLPromises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}