{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98dd33f3a9d5ce3fc13952c0af47cb165d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/opt/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/opt/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/rive_common/rive_common-prefix.pch", "INFOPLIST_FILE": "Target Support Files/rive_common/rive_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/rive_common/rive_common.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "rive_common", "PRODUCT_NAME": "rive_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980f57814422974f91e585364d1742b90a", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988192a0e1d2c190678037b4c466dc491c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/opt/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/opt/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/rive_common/rive_common-prefix.pch", "INFOPLIST_FILE": "Target Support Files/rive_common/rive_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/rive_common/rive_common.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "rive_common", "PRODUCT_NAME": "rive_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e787b141a69225caa8c542a84d2b72e7", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988192a0e1d2c190678037b4c466dc491c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/opt/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/opt/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/rive_common/rive_common-prefix.pch", "INFOPLIST_FILE": "Target Support Files/rive_common/rive_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/rive_common/rive_common.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "rive_common", "PRODUCT_NAME": "rive_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e7bc26421fced38567f9322cb6b85a39", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98654127f5809850f9379188e2b4158a99", "guid": "bfdfe7dc352907fc980b868725387e98942ef7389556f6464895d3ace6c6e8a3", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e983c9bf989eef972567145e1cd59e9ba29", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ed198d8d88880ee77c2cff59c3400773", "guid": "bfdfe7dc352907fc980b868725387e9838e1162dd67f51d279833941ff80d167"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9812d65607717f11865e167381921d9c92", "guid": "bfdfe7dc352907fc980b868725387e988b7101307f4115ba8e6c4b8f32286030"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca13e233127feab5d94d622beb126511", "guid": "bfdfe7dc352907fc980b868725387e98efced4c623e9ec891e5ef8ac83c20846"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e95d58373d4984d39005def65dd3a85c", "guid": "bfdfe7dc352907fc980b868725387e980a5eff123e0ef4589f008e9746ef72ef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989535b5133746161c62baa612508f1398", "guid": "bfdfe7dc352907fc980b868725387e98260905c2220bf9b7f5c9babfb4ace718"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6a5e509928eda44a4da93f200ec3e46", "guid": "bfdfe7dc352907fc980b868725387e9840a2f7ca0601e3960c194df6e2379493"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f87f85b63d7ce8904aab0712b133a3f6", "guid": "bfdfe7dc352907fc980b868725387e982a00c61b52bfb83d486d913f04437cef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9890632e2115c6dce9807689580839b993", "guid": "bfdfe7dc352907fc980b868725387e98d9a691aecf198eea75abca9aee54ce88"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5b71d1a6c88ba7767aa350ad84ff40b", "guid": "bfdfe7dc352907fc980b868725387e980093cfd22a8801e41a19b7e09a85ae33"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e45d700f9d6f5b00cecf573e71ebe68", "guid": "bfdfe7dc352907fc980b868725387e98f62c9d030cf94c74482e66514bd27cf9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c768aa6da402479f9827a91fea534c4", "guid": "bfdfe7dc352907fc980b868725387e980ad08cab2518ee0e6a99ec334650490f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f296b395aa210a7e1870bbb5ee3e75c5", "guid": "bfdfe7dc352907fc980b868725387e986e826f9b80f468d20e5a6d5da70d2612"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982fcd4345682b1e49d871ea2798b9c42c", "guid": "bfdfe7dc352907fc980b868725387e98f13c8b28d857e0d726464ff048eaa5d9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e1921a89236c4b4e08b2e6d49b1346f", "guid": "bfdfe7dc352907fc980b868725387e98221642b6205f5b0487f0fe894bbae57f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be1f552bdb7cc6f112a38a5c75ddf9ea", "guid": "bfdfe7dc352907fc980b868725387e98aa965c24aeb57b6fe9d1d0316a34fa11"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d18fb0a2da185b698c7509ce16fd46c", "guid": "bfdfe7dc352907fc980b868725387e98edfc8fdfac361047d99003ce3bec9fe6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98351e68a5563343b78f290669b2488e8f", "guid": "bfdfe7dc352907fc980b868725387e981cd32b162a626f69cc2b1f44f94df15d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d5cb05ae7633d22225fe5a4d016f702", "guid": "bfdfe7dc352907fc980b868725387e98a53bd707607205881a3d7e44503184c4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b6b53ecbed9a2a25ce687a63ad1eaa9", "guid": "bfdfe7dc352907fc980b868725387e980fed0b07b84c27f0311b7e1d1e3bdb9b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1ddb95ae1a56f941c9014fb4ca45928", "guid": "bfdfe7dc352907fc980b868725387e98d69ed4601458b9d9890d8705d602592e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eacb6bee4f0b639858d8ea62d5f4d4ce", "guid": "bfdfe7dc352907fc980b868725387e98a0d833bb4e6f960d1e44937494f4cadf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985af1684861626af671821182e8cc8879", "guid": "bfdfe7dc352907fc980b868725387e98ea8f4762090fc86527c22a720e002ea2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b30f8699031512e3de39e81c2328c61", "guid": "bfdfe7dc352907fc980b868725387e9890c681c8d0aefe0baf2fda2a00d62f36"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f640268bbc603cb8858abad084faac59", "guid": "bfdfe7dc352907fc980b868725387e982d8a3e0f26a09b8df302771a667fe358"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985222dcf3382f86effdd86e78955dcb49", "guid": "bfdfe7dc352907fc980b868725387e981c696d3d797cc889283dd2bc761d8c90"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a453a81d617d88439d5c500dcdb3c7f8", "guid": "bfdfe7dc352907fc980b868725387e989be8b35b5455a1e59737d8b4214b75e9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad314d474b487a642641113d045dece4", "guid": "bfdfe7dc352907fc980b868725387e9844888e5465165c43028e747fc906493c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e365754b0e48d78ee86f63b744c16986", "guid": "bfdfe7dc352907fc980b868725387e98d95b28d5272e9aea0d6386dfa95235bf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d5b72c3b037355ea9c8f34477a7fec9", "guid": "bfdfe7dc352907fc980b868725387e9834c050ac4a4c7ee4f9a3e4e4254ad97a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bbdfa7bdcfe64bfc11783b12cc8bf7ae", "guid": "bfdfe7dc352907fc980b868725387e980f02a3fe6bbea5430ba2e3e7b84550bf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98319bd6aab45b78e536209add48d07f39", "guid": "bfdfe7dc352907fc980b868725387e98c8183d7c7e10f638ddfe97a4d4ab11f0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2c3ad9ee0de203c54a4e0f1cc80cc33", "guid": "bfdfe7dc352907fc980b868725387e984c334ffbf449f2b4f95f7cf6bce87699"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b44ce2da53650bfb31696b1036ec2e31", "guid": "bfdfe7dc352907fc980b868725387e9876911c529679c57808f0292f2a26792e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dea45d9f33dc6edb654280264d4f0e57", "guid": "bfdfe7dc352907fc980b868725387e9848d874cd191301703bea6c47f95f39bb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983db0ae4ff06f219f1b72c8425ba0212d", "guid": "bfdfe7dc352907fc980b868725387e987dbb37d6b9ea11ee33d5c5016e307a9e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983206b4d6313c410524a00e94d205ad7f", "guid": "bfdfe7dc352907fc980b868725387e98b9812a3684439df54b2683c3d7c8fe1b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987fb01e1b5bdff2d34b8453a0d9052cd2", "guid": "bfdfe7dc352907fc980b868725387e9831861cac64a60be7be9c208ef4aa871d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9877d714741215b31ca1528705067f6b83", "guid": "bfdfe7dc352907fc980b868725387e98ceda81f425a49383b41a1e3d5735cf47"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980cbe03f9a917e832e079b1437eab482c", "guid": "bfdfe7dc352907fc980b868725387e9851518481a71ff30967049f217ff47e1e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836ac0ab217804419e0b49a0a28660789", "guid": "bfdfe7dc352907fc980b868725387e98165e9b9610a04d54f7fa35c75d01617f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df27766e2e074a384ddac6f874831c6e", "guid": "bfdfe7dc352907fc980b868725387e986c758e7c23a0e5577926484d39bebf0a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988fea19a446686a1c90cc1fff156f5422", "guid": "bfdfe7dc352907fc980b868725387e987b6fa1d7480b28130b3d154cbd486918"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e0e82e4dd66689bde5776782fcd6990", "guid": "bfdfe7dc352907fc980b868725387e98f03400114616d626b2ebea8255750058"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3dec1b94bd9742c23fbb9614faa0fe9", "guid": "bfdfe7dc352907fc980b868725387e98f8ca011982d2b37d035d01cc0a781cb7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d35acbe148c1569f604a0d560978888", "guid": "bfdfe7dc352907fc980b868725387e9817c43b9d8d82365bee6d0a044575c447"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98561f5f2701d9078a58ccc5c3432311d7", "guid": "bfdfe7dc352907fc980b868725387e982a4936d61b01d0925fa8a3c4c76cf69d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98900c194ecf238ba3f4019f38841bbd1e", "guid": "bfdfe7dc352907fc980b868725387e98cb45d203cef6c66d41ed303e4c228159"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e3fae78a181eea5f2718d8c2d0631b3", "guid": "bfdfe7dc352907fc980b868725387e9822076234c0fc00f08566bb0b8efa07af"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862ce1dfe85bc00b0910af96b0dc76db2", "guid": "bfdfe7dc352907fc980b868725387e98397091576b2714bac297267d569fc3c9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98346e3de742cf538dae1d98b4381559a1", "guid": "bfdfe7dc352907fc980b868725387e980c50e8411cbed6f0aeca06c229271150"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989aba41dc28d8eafde41f9df4831ea5e0", "guid": "bfdfe7dc352907fc980b868725387e98887c6e31be51df2c95194fd5f6617e6b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98064a5f087a364c065dcf0ab0ecf35553", "guid": "bfdfe7dc352907fc980b868725387e98dd17a048067c7d20f9f451577875c105"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9845e8d5d0dd82e885253065da954b9107", "guid": "bfdfe7dc352907fc980b868725387e982b3c5960871c3caace17887c3ad320cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe91401b53f6e769065799ca59758152", "guid": "bfdfe7dc352907fc980b868725387e983050cdc94239d4012c828d40bd07b19e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cfbbc23ae5532d366c653b3bed2b8a4e", "guid": "bfdfe7dc352907fc980b868725387e983947fbc4f7e1426a707884970505cb35"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dbfd0ed3bb9eb7c46f05a32e55d58a5d", "guid": "bfdfe7dc352907fc980b868725387e9828819d73ccd58ea1e6e7175cf03c39a4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ddcae226ee5d0103cb739b0013234908", "guid": "bfdfe7dc352907fc980b868725387e98825b584095ebc599ba7f3fab26dd1e2f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855881756355652945069fa117225519a", "guid": "bfdfe7dc352907fc980b868725387e98d478dce2372658dd5e218395da0d8d0b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2d2371f07dbede7dd438c4e5799014d", "guid": "bfdfe7dc352907fc980b868725387e98604d67c0c8e920c9f43c74b761b5ff6b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ebbfbdc3efe42e34c19b85c44b4463af", "guid": "bfdfe7dc352907fc980b868725387e98ddbcd949965c7f0ae35589691a5a9846"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b4f3dcbba0fefcca4b75fae8a7769197", "guid": "bfdfe7dc352907fc980b868725387e98e3f07419c6836fb1332f88cbbedd24a7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98464988f4da5c404e4b886e854cd8ba22", "guid": "bfdfe7dc352907fc980b868725387e989025aad18341f10340a2221e09d4db15"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9811eaef9c52261d478af70652306f2830", "guid": "bfdfe7dc352907fc980b868725387e98ce45890ed140f2a0ee7e52eb04b1b500"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d8a92b5a7d3415661f4f565cc849e946", "guid": "bfdfe7dc352907fc980b868725387e98ccb9df813753e87147a290a7f6a7b415"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873975e6f2a3ae5b3f5ea261b69b28ab7", "guid": "bfdfe7dc352907fc980b868725387e98ea77a667eead9453c785c04129fa9be3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a15cb02f1b5a2afc0f68ecd8263bf149", "guid": "bfdfe7dc352907fc980b868725387e989f99312543e93a29e61ebde98998db23"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5b50d961607d21433532fde7a11a61f", "guid": "bfdfe7dc352907fc980b868725387e98431eb41c53dba018c73c480a026e8d8e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987455dfdfc559eb11fecb1ee1345c9e16", "guid": "bfdfe7dc352907fc980b868725387e982803d4a82a1430ca388b950f51477448"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980fd2af9b71ae930ed0c5fedda3e5ebcb", "guid": "bfdfe7dc352907fc980b868725387e981735975101e4052d768e72bb596a1c37"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878d7ac208d92d2b33bc2eee4e6253674", "guid": "bfdfe7dc352907fc980b868725387e9836d99ca86642cc6677269781f0498bfb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd78ca0a81178fb49ca6e93a81ec06ab", "guid": "bfdfe7dc352907fc980b868725387e98ad4830ae41e64608f701b06b42d4dba3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad1e7837942490849594c1da742e9ff8", "guid": "bfdfe7dc352907fc980b868725387e982ddf409c0d56e7c6ad7defea12259463"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9843304df79951c57d6c74959ed04d9a28", "guid": "bfdfe7dc352907fc980b868725387e98a07d30204b8476fc49308f43e3dc5399"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9882967c58eb6c896829c4f053a060a0f9", "guid": "bfdfe7dc352907fc980b868725387e987635cd4dba73326ec55526305a9f3956"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b86287dfe4534ed03895ab333233835", "guid": "bfdfe7dc352907fc980b868725387e98b98707da73425d2c17d16ce1a4e9ff58"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809637ba0ca9a2f69b37a80a6fbe1929b", "guid": "bfdfe7dc352907fc980b868725387e9861269efec75bc3f510a2e6cc67f96fd4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea8aedd1ea62abc81f7e30f6b621886f", "guid": "bfdfe7dc352907fc980b868725387e98536572008c7eb1668377ec5c153ce98f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b0da02eb1a55cd7c55aa806ce340cc3", "guid": "bfdfe7dc352907fc980b868725387e983dad1b8928bba1bf448d923c66e0b144"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c9605ec8e98e9ca696a6526479b38e7", "guid": "bfdfe7dc352907fc980b868725387e98c3e514624c40651a5e08530565541b73"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff1e7aa7a8f7f90cdedb3501d8cac648", "guid": "bfdfe7dc352907fc980b868725387e988b2382f8ed21710942b4074956359a21"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dbf5047f5e111376e2cd9a38a815aacb", "guid": "bfdfe7dc352907fc980b868725387e9887c1914f609abac8c79f8d42b823fc0a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984029250a14521ffc8635559d75f291da", "guid": "bfdfe7dc352907fc980b868725387e98f50739b8b50d5e9b26db8b944c690594"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98117477afe416e13ef56ee16ce0bc5d78", "guid": "bfdfe7dc352907fc980b868725387e98241dca5a217c6e983ce614fc52d3b179"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9839c1235ef9b31decac9941e62a042bee", "guid": "bfdfe7dc352907fc980b868725387e988eb1d69b8b5930ac7537f9446d1e2bfb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e85b872d74fb515f94ff5498169abc64", "guid": "bfdfe7dc352907fc980b868725387e989c544234007d821aa86d5e82a631ee0d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9896fadab5abcfa8a009303bd876386183", "guid": "bfdfe7dc352907fc980b868725387e98aeb0b494a82442370d4ec460f3048843"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822f7417776ddee922e413b6021752970", "guid": "bfdfe7dc352907fc980b868725387e98cd1002e66643e5cbe544fa6c7d826bb6"}], "guid": "bfdfe7dc352907fc980b868725387e9882e976b2df5febec451208736c05c030", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98374e3ec4dc398dcbdb656bf148272974", "guid": "bfdfe7dc352907fc980b868725387e980bd8c341f3e09e63ccebbbbe3caf7f86"}], "guid": "bfdfe7dc352907fc980b868725387e984c1782d670ed41dd52f886a3a5669bc5", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98efd2d3c8c90fc4f3d99c6bf8afdb08b9", "targetReference": "bfdfe7dc352907fc980b868725387e9890f568fc9b811cd56b08e401eec0f35e"}], "guid": "bfdfe7dc352907fc980b868725387e98e36619406ab3e0809dcc4dc4d27b7328", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9890f568fc9b811cd56b08e401eec0f35e", "name": "rive_common-rive_common_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e9849c5bdbe31e3b466d37bab9271baf60a", "name": "rive_common", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9884d4dff26bf6b3e66f738069c7476896", "name": "rive_common.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}