{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98675173073987900beb6a704fc4f75306", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984c648864e2ed62b6e3baf85b84c1bad5", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982139182adc39ee4174e47ab58906a28f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9862db300e39e652f175c69630a20f1147", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982139182adc39ee4174e47ab58906a28f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9808bcd6817ad5da2d09d03c6a64e72803", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981022bb41eb943a32a2b00acf55a7c8f0", "guid": "bfdfe7dc352907fc980b868725387e98578106f8463de48f6c6937dfe84dc5fb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984908e48bb7a3a72ede5f72d0a325d217", "guid": "bfdfe7dc352907fc980b868725387e98fad29a01b15de6967c3b61b290ab9549", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b903c8a971f1d1a3d3502ae2d735671", "guid": "bfdfe7dc352907fc980b868725387e98ceacaa2b785ce29c09b5ce37340a30e2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98850fab10021e7e91c2bb94f7afc9fd0f", "guid": "bfdfe7dc352907fc980b868725387e984d7ef2889e10a4bfb5f975a129eb8e0c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9594155627cd2a68cefa95be5d07f1a", "guid": "bfdfe7dc352907fc980b868725387e98a9a54974c788c3862adf5847a5a0c7bd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d1cd48c03dad90255c17d9806a0c49e", "guid": "bfdfe7dc352907fc980b868725387e98a629be733484f1128f063102cd968fc3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9892d5bc8dc5e02cff0b70f4c65b2aaaeb", "guid": "bfdfe7dc352907fc980b868725387e98af99459a32262bd551af8897b4fc9731", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c46beba11cd8aa7e4ceae834a1a26f33", "guid": "bfdfe7dc352907fc980b868725387e98698969192b5948a9b59b62b5fe2b1b1f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e3aff604206c39b7a0147c54255da926", "guid": "bfdfe7dc352907fc980b868725387e9869802219ae109100ce61ef7c2216c8e4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0cf557a2c0f5d80c7a699ec43909a11", "guid": "bfdfe7dc352907fc980b868725387e985d78f384bd304315af64cb603b91d450", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eea03d484ce0936a665f74e24b6563c8", "guid": "bfdfe7dc352907fc980b868725387e98571b7536ca4ec0d3ee11cf542821fe55", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ff807227be6ecb7a4dcc356272f7a76", "guid": "bfdfe7dc352907fc980b868725387e981c71409ebd05dacde12f9818313b150a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dfee9d3e06a5cbd679e9d4d19a010ec7", "guid": "bfdfe7dc352907fc980b868725387e98d0b3002ff3c777f57e8d9fc5aff5c8e3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98220b7dbe56b0b0422dd0adcdf742ba28", "guid": "bfdfe7dc352907fc980b868725387e982f3c43633e202c11d25c4ba069d2855d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835f5e7391f357c6dc41fb53480e8445d", "guid": "bfdfe7dc352907fc980b868725387e98075f2b7fe77133e7f7d4ff0a6444a1c7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1e0d6e613f0464e8f6281562ab5af41", "guid": "bfdfe7dc352907fc980b868725387e982e6b195d6c0b1d8a11260c3d9fcbc357", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ed4dbf8206bd955b190a7d33bb0551a", "guid": "bfdfe7dc352907fc980b868725387e9885010abab1c18624ba7c0566fea20837", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98701822031c4980fea483797a83b8b38d", "guid": "bfdfe7dc352907fc980b868725387e98fe7d4380401000d34f6987c33c2cc727", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98183a0868a9820a493ed3eb2c32861d44", "guid": "bfdfe7dc352907fc980b868725387e9880096225ae8ff4f6a45888944e84b93d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98089b676467ee8e7b58430850e1777836", "guid": "bfdfe7dc352907fc980b868725387e98facacf3c10b1d8e1b2e8e42ee2b7f276", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a40ae4b2392da4623c4fdce314812dfa", "guid": "bfdfe7dc352907fc980b868725387e98cd1836221e0d2bf0e1a50267e4794826", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe38a176f271fecbffe85f73c65c83f9", "guid": "bfdfe7dc352907fc980b868725387e984ebb57123c8a1b45e08e4adc8c3800f1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98586dfcd32f6208f9d15d01d243e79435", "guid": "bfdfe7dc352907fc980b868725387e989c6293912c8baf9afbab50ac45938509"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98462e3590ccc30e2b58573cd1200a39c7", "guid": "bfdfe7dc352907fc980b868725387e98731e64fbfbf97bf5855f2edbeec4c671", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989692251d2404c7e4a05fcf5e4b06a72c", "guid": "bfdfe7dc352907fc980b868725387e988d74d73372f7080e469011beb28b6eb8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c9d90e3097534b527039ec697c08898", "guid": "bfdfe7dc352907fc980b868725387e986f09d1c8302db956723d67b5c8f4ad49", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7370ad6190cee56f6ea3c7833307af0", "guid": "bfdfe7dc352907fc980b868725387e98d6facc701dff9b1acd610f7d06f59001", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ca0739f2c6601baf2dbb6e24a8849ff", "guid": "bfdfe7dc352907fc980b868725387e981cb6d68d7a2d801fb86fc805aa21fd7c"}], "guid": "bfdfe7dc352907fc980b868725387e987878b9f79d92cc9eab90ee74fae60a48", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98301a71c38b469796e30af8da88bc80d6", "guid": "bfdfe7dc352907fc980b868725387e9811e06adf2d18dad220ab5f0fea918b2e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d242b0fabb8787ffa64270e060f69900", "guid": "bfdfe7dc352907fc980b868725387e989b2366a1be70c0747d98a587f66954d4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9882b9303d299dfeb715ccbfb5abfc86e4", "guid": "bfdfe7dc352907fc980b868725387e98bedf6e7277185f513cf5f1f1169df966"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840074adb53f357172805d59eaf7c432f", "guid": "bfdfe7dc352907fc980b868725387e98337f22a8554af8ba98da8ea685adfdb7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a401a4ee31bce6a67c65e7055fd184a", "guid": "bfdfe7dc352907fc980b868725387e983655db96ca3b60c3066760d062b25bad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873e9f95f5c0aa1834ea02662132e6d7f", "guid": "bfdfe7dc352907fc980b868725387e98488d58b6071dcb958275d6e7b608dba2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e16660052343d9f6499a54e25671fe89", "guid": "bfdfe7dc352907fc980b868725387e98dcdb2370083552414e90054f158521d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ab1db82d086adb4e3dc4bc5d9938c32", "guid": "bfdfe7dc352907fc980b868725387e98c934d49261111b62017a008a7e86fb49"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aeba8836a4812d6f1b4d85db7875fb6d", "guid": "bfdfe7dc352907fc980b868725387e988a111d3f7e8f6b42a3681c4b5ef39127"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98045a99b969bbcbd73dbc38f0cc0f3f21", "guid": "bfdfe7dc352907fc980b868725387e98a3b45fa05d0fbd6e33ef93edd4934188"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983aa1a0ae291c6330e605ef298c0d6265", "guid": "bfdfe7dc352907fc980b868725387e98ca8ba046a43e4b657f7d6aed47a3f674"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e861ef28cef0362b24687f7412f06a4", "guid": "bfdfe7dc352907fc980b868725387e98a05e93343f6a380cb4fb123621c59923"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985724cc7226987d639e4ebfef53518495", "guid": "bfdfe7dc352907fc980b868725387e9821101ce0c16e34fe72010391b5dbec36"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f33d8ffee6154fd0dc1f55817553b1f", "guid": "bfdfe7dc352907fc980b868725387e98c3900859f30dd2fd5e32ad9c38512ef3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980dc64956c639929007744a5763432b98", "guid": "bfdfe7dc352907fc980b868725387e989fe4cba93e5470a92c4766d6acf8acd5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98616984b13d2800143781e717c07517ef", "guid": "bfdfe7dc352907fc980b868725387e98a1d7e8ccef0c183c87d262bbafff8c18"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a297b0949edb682ea251d48fbd0d4bff", "guid": "bfdfe7dc352907fc980b868725387e9883b2b61948200d540feb4ca69e58cb50"}], "guid": "bfdfe7dc352907fc980b868725387e98db79b9f40cba928bf0b427c877077b4f", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98374e3ec4dc398dcbdb656bf148272974", "guid": "bfdfe7dc352907fc980b868725387e982a9941d24697a245c68d49ecf799979d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98974e1232357fe39e8cbe1022d2c8bc9f", "guid": "bfdfe7dc352907fc980b868725387e9899b5e8c4a0b0042029555ba8d2f4a791"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864d5fbf613d571389caf1bb9b6ca5010", "guid": "bfdfe7dc352907fc980b868725387e984a4b528fbb0f566492f7b84d6d042fd8"}], "guid": "bfdfe7dc352907fc980b868725387e98fa50eb73e239c34c4746653d3963beae", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98a4e828fb9bae6606115df29893f903a8", "targetReference": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4"}], "guid": "bfdfe7dc352907fc980b868725387e9879f2a022c93c3ee62762636b3a5b9fcd", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4", "name": "GoogleUtilities-GoogleUtilities_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ca49ca851f2777b997a3e74ccb860358", "name": "GoogleUtilities.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}