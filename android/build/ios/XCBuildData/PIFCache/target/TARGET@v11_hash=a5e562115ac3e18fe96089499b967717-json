{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986a18fa41532e546aaa739a2a91b66b2c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9869e67bed6c5e0243690e3820bb9c5bc7", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98fd3949df67820e8560692f79b02b7103", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98bd9daf99c6e378241f2d50dc857abc41", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98fd3949df67820e8560692f79b02b7103", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9840b6a27e48b860034886e1916b2f3411", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982bfb30643c759dda6958b02b24bc49cd", "guid": "bfdfe7dc352907fc980b868725387e980ce197c8bdddfaadebb8197adf9b55ce", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e989e4cb31a95898d55c0dd3ab7851c9ec1", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981d5b7e3597e7a4af2bcaedf305d38111", "guid": "bfdfe7dc352907fc980b868725387e98a3651886756f8c8c7557a9f62f9ba033"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2062d4265adf176d35227b215661415", "guid": "bfdfe7dc352907fc980b868725387e980d572601e47c76994cc5d8e4162fa427"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b681b05417cb3d22825cbae934b09724", "guid": "bfdfe7dc352907fc980b868725387e98a33a06ced49c02c37449cc3250506554"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d9f787e73110d8cf1f5c5cb7aba7527", "guid": "bfdfe7dc352907fc980b868725387e98d58e74cf1c1a127c0a6e0fcf3a5e8e5c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be8ace2b2d46583f04526ffabe0c3a37", "guid": "bfdfe7dc352907fc980b868725387e98ad32a84b963d0c6ca44ac6de7060c2a4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819f0673c31e41bf38f4899ee46d16ba8", "guid": "bfdfe7dc352907fc980b868725387e9826b03b8a1deb5e0568bde9e76b392e30"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f45557f464f04e80a1131d06f257c64", "guid": "bfdfe7dc352907fc980b868725387e981eab57c21b47c6f48bff31a77894680f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821fe9783c8ec2688e2183f60f06d5b0e", "guid": "bfdfe7dc352907fc980b868725387e981ffa27b26d60b768cf5b5288f0101249"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b91004b5aeb02ef462e15190ff0f77ac", "guid": "bfdfe7dc352907fc980b868725387e98a666a3cdc0b178e7fbd1fb136086a866"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e36836aefd80751dcc3ec28c97b6e2a", "guid": "bfdfe7dc352907fc980b868725387e988fb0e30e6be10d0c1f85e579d96bb070"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981195403d4445f8bfe6bd90d928e888b3", "guid": "bfdfe7dc352907fc980b868725387e987c6cc083bb96c60574fac8cc3c68b3e8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98789c0755607efbd03cd42bb6819cf768", "guid": "bfdfe7dc352907fc980b868725387e981f811c0d94d44dc402d70e21c75036ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf2a685561cc4787fc20c9322aa6c43d", "guid": "bfdfe7dc352907fc980b868725387e989b6e02a464d972083b4fb54a4f463eae"}], "guid": "bfdfe7dc352907fc980b868725387e98a5428ad2d36088dbb39b592f68d6cf32", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98374e3ec4dc398dcbdb656bf148272974", "guid": "bfdfe7dc352907fc980b868725387e981fcbe4eadd5137b756d6ebf053a1ac74"}], "guid": "bfdfe7dc352907fc980b868725387e9849cc30ea66ec3bdab64e7c56e513e6d3", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98b23153a8b71ee622bededb0df5c580c5", "targetReference": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f"}], "guid": "bfdfe7dc352907fc980b868725387e98a18ad75ab385e30ad5c13bdc1d5a4309", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f", "name": "FirebaseCoreInternal-FirebaseCoreInternal_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983d86e87924acfad2934921ce7ad9fbea", "name": "FirebaseCoreInternal.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}