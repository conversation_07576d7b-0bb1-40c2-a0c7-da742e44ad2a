{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980aa40e00d73462d0a544d4cd756e01c0", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/DKImagePickerController/DKImagePickerController-prefix.pch", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/DKImagePickerController-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKImagePickerController/DKImagePickerController.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "DKImagePickerController", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984cec2ec1f80a7da89a4fdac18efc6203", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9803c19e9474ce8df39c191bc8e17b38da", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/DKImagePickerController/DKImagePickerController-prefix.pch", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/DKImagePickerController-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKImagePickerController/DKImagePickerController.modulemap", "PRODUCT_MODULE_NAME": "DKImagePickerController", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ea7be7f0530eb05523950bb1077f4a39", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9803c19e9474ce8df39c191bc8e17b38da", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/DKImagePickerController/DKImagePickerController-prefix.pch", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/DKImagePickerController-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKImagePickerController/DKImagePickerController.modulemap", "PRODUCT_MODULE_NAME": "DKImagePickerController", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981f36e7195baa9f7e8f5a409e75d2fa8a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b2360e14ffddfd86f7079e3ea48e4862", "guid": "bfdfe7dc352907fc980b868725387e9861bfbb6a236bfafe99d24647fb0a648c", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98fc56ed640800de61cfb79a991736621c", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b3dcfbc2fba3ed75258efbbb9c77bb27", "guid": "bfdfe7dc352907fc980b868725387e98bb4fdcc38b0c6de8a84737aad1b2ea91"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5ef0f0181ad007b262bbdffd028076e", "guid": "bfdfe7dc352907fc980b868725387e98bd404fab034d291ec40154ea90aef255"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b374267e3617bbd2c9d560bde06974c", "guid": "bfdfe7dc352907fc980b868725387e9855cf4237feae7d230b5b9f5835b27865"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9812eb7cd13282fce65a960a1a7227be02", "guid": "bfdfe7dc352907fc980b868725387e98fe96ca541e805510716f76e39959b092"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c833136fc09092632ece97b7ba3458a8", "guid": "bfdfe7dc352907fc980b868725387e98c77eeb5a39dcdd516cf5a5e59147a1e5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb0758e6f18bb5d9cc2f6ee760bc1ab6", "guid": "bfdfe7dc352907fc980b868725387e98dc10d91995175f85a161b906f4df6c75"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9847f507eb22aa303d93a02ab70be818bf", "guid": "bfdfe7dc352907fc980b868725387e98024b4b959ad8c3b801cf226d53d4cfcd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a081ee438aa3c221e9ca5e0decc8e2c", "guid": "bfdfe7dc352907fc980b868725387e9803adff81be901d1ae24761baa2eb7059"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d732264352eccd45c1cca7524f7f588", "guid": "bfdfe7dc352907fc980b868725387e9800782d6d178d0f4ef6a7599725678f9b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d46c97093a20b8f05aadc9b11693658", "guid": "bfdfe7dc352907fc980b868725387e984e5f8239733b87e173492ead11e18d2f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98521e38d44a38d1035db6d040fbb42fda", "guid": "bfdfe7dc352907fc980b868725387e989a0dcecfad2408dcb45a15a20942c7b4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f6e5228b320771fe5ac113ba551009b2", "guid": "bfdfe7dc352907fc980b868725387e98dcb29b32065be49fe2d45e7c1a384350"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f19dacc2d7c7f36db15105c4f694a28", "guid": "bfdfe7dc352907fc980b868725387e98b3be41c4d4cb57acd97dcbf9056a2034"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878e1d7f82d20c2d122ce57123682358c", "guid": "bfdfe7dc352907fc980b868725387e98c44ed1c7ef2e335edf03a8669e500384"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b0594871d63a3a8783ae781dc28abb2", "guid": "bfdfe7dc352907fc980b868725387e980b6bf23598179d1b460c191ca0bdfd2f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c361508721fc484b56b2151c19d10cc", "guid": "bfdfe7dc352907fc980b868725387e98987c9a52fd9f1cc6753bcb32a03fd2ad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98238f45350d73059fd7782da65a90e216", "guid": "bfdfe7dc352907fc980b868725387e9805ca1257218fc06bf362dfc8d328f733"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98965f03d5f55e027312dee86bef0c0ec0", "guid": "bfdfe7dc352907fc980b868725387e98a242a252d282ca671d43e04129c0b161"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981461f6e5cc0735e9db308a719d3e760d", "guid": "bfdfe7dc352907fc980b868725387e986ebeab8c8272f8dbe0e8c1a26eb6361f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98164059fbe48696d3cfda05cf7e2126b8", "guid": "bfdfe7dc352907fc980b868725387e9894ada70d33f11d628f5aa19102cc4e4d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9894e41fe6aa4f5f1c678d8ec81c3fbca3", "guid": "bfdfe7dc352907fc980b868725387e98612d965d7a7fce750c679b8ccd3786dc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef559df555e976d15da69e9cace7e3cb", "guid": "bfdfe7dc352907fc980b868725387e98b8df2e2d67946993fb61209d54b1649c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c4f59d21737d46fca81f7ea0905f996", "guid": "bfdfe7dc352907fc980b868725387e98a82bf5a58c3069f0b7cca6767ba71341"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98023cb87097b25bd10a3a6784493bf2f3", "guid": "bfdfe7dc352907fc980b868725387e986b453f530cac7b3ebdcc574b6c60fdbc"}], "guid": "bfdfe7dc352907fc980b868725387e98212737d6db54fb0517c3224029e3afb5", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98374e3ec4dc398dcbdb656bf148272974", "guid": "bfdfe7dc352907fc980b868725387e98038249339b7dff72c7c598ebba6b91b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ecfcd2f8da23e3b68f8b9272a708c37", "guid": "bfdfe7dc352907fc980b868725387e98cb5ca1666ae9dae6138b4306c79a51ac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d63ad33f357e9346c3b5060dfe50403", "guid": "bfdfe7dc352907fc980b868725387e986e39e0bbfbf7b24fece993779bb585ab"}], "guid": "bfdfe7dc352907fc980b868725387e98130c36ab2480349ba5ff0262c3b45df3", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98e6fff4f6cfc8d12ebc57c4bf8981d65d", "targetReference": "bfdfe7dc352907fc980b868725387e9898fccba7a2febdedb43dddbf2e949fc3"}], "guid": "bfdfe7dc352907fc980b868725387e98b4b5611bdd6ff9f5661a6dca82b1072e", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e9898fccba7a2febdedb43dddbf2e949fc3", "name": "DKImagePickerController-DKImagePickerController"}, {"guid": "bfdfe7dc352907fc980b868725387e989d0a1858a86fd6e6731ed20f88a1e515", "name": "DKPhotoGallery"}], "guid": "bfdfe7dc352907fc980b868725387e985fd5cdb9993b1816141f0c012ffa62bd", "name": "DKImagePickerController", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f752ba05adf197c3696519e901961310", "name": "DKImagePickerController.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}