{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98eac6a69e266cce080bfa9eee70f8d145", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988f3e5c8b8e9598a155afeef6cfa62877", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982fb13f5fcbe5c55433ea3ce025713f07", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c2bd0106b94a28ec95e23deb897df821", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982fb13f5fcbe5c55433ea3ce025713f07", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98bef7b438ca66719cdac6066d30f2e89e", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98fa1dbca4ec3f1dd068a760332b592dce", "guid": "bfdfe7dc352907fc980b868725387e983ca68336eb208ae7b7dcee10da788ede", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985605cae42a60dd43a64510fb4dce0969", "guid": "bfdfe7dc352907fc980b868725387e982cf6aa5441b72c06c4add4931f500cb6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d5e5d2605e21cc6a3de54a695ef1080", "guid": "bfdfe7dc352907fc980b868725387e9812331263c6ebe7e7a2d0a4bb1640936e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984da4f7f19699a976d3209112886b3dec", "guid": "bfdfe7dc352907fc980b868725387e987e3ee6d3264001cbeaaa16d14ebba2d2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985818a25b25c95419ab54f4d8d2787130", "guid": "bfdfe7dc352907fc980b868725387e986aa6f444cbe77f87fd077f4884830916", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fae5e080fd00be1c2d468a2107d51bb3", "guid": "bfdfe7dc352907fc980b868725387e98e9e1c7ed6cb51c8363a3a78a9b99feb1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f6d4daa2171681a82418e10a4b96870", "guid": "bfdfe7dc352907fc980b868725387e98d661b0aa9b7d790fd514d51ee58d4f23", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9820b1d2405bbac0baa45f98cee035682a", "guid": "bfdfe7dc352907fc980b868725387e98afcfd7868aa9f34a93996980c2b39877"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eef84f5c3e85a857ee9c659f3e78243f", "guid": "bfdfe7dc352907fc980b868725387e9801f6fe294f496a395de38f6fce330c83", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9869e824892d7203e9c8b85d6fc93095b2", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e7e613eb4bd13208e31c3f6ffc1b4485", "guid": "bfdfe7dc352907fc980b868725387e98decbbb0bb3692d7d4b94c96dac1ea78a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cdf1d7fc523c0517ac551250e7d195f2", "guid": "bfdfe7dc352907fc980b868725387e9872d3a2532a49ceb427f90fb984ba2ed2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9870c08d3e98aa1909a6ba80f107991830", "guid": "bfdfe7dc352907fc980b868725387e98260ceaebe2f29c1dbc65df45a27a0c97"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa4778c30473b42d53a6316e6163147d", "guid": "bfdfe7dc352907fc980b868725387e98322b74762e3dcf2d00ba8d6b49251e81"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98788c5bb33f45a2c7422082bce87e69cb", "guid": "bfdfe7dc352907fc980b868725387e9801f1819ab3c49837d99be58295558b13"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988776bc6d987244bc0dd856a1bfe5f0cc", "guid": "bfdfe7dc352907fc980b868725387e981033644b7f53e783d4f1e2cc8eb785f4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981211fe902b8d4318a56e7ffbc655acea", "guid": "bfdfe7dc352907fc980b868725387e982590231ed4e1b62f1b18175e4a117612"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bcc2b25d916f86d11cca7afde863e98a", "guid": "bfdfe7dc352907fc980b868725387e981ff9f52af48edc1c32611a234cb4bafe"}], "guid": "bfdfe7dc352907fc980b868725387e988c5251a59833025a67556188ff58bc75", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98374e3ec4dc398dcbdb656bf148272974", "guid": "bfdfe7dc352907fc980b868725387e9816b248f71d81c861e3089afeb6ae44db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98974e1232357fe39e8cbe1022d2c8bc9f", "guid": "bfdfe7dc352907fc980b868725387e988f349cb663078f2cda1a1b407ea6bb7c"}], "guid": "bfdfe7dc352907fc980b868725387e989f2063d6044870b99dfba4439bee7352", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98bbd352ee5eb9a4a47ac466e2d5642663", "targetReference": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1"}, {"guid": "bfdfe7dc352907fc980b868725387e98b565cd337e5b7108b4aa65724a05e520", "targetReference": "bfdfe7dc352907fc980b868725387e98d9a0c31ef553ffd1644ea2f5fc087d46"}], "guid": "bfdfe7dc352907fc980b868725387e98be7e79ef95283acd00b7ee586a97a8ec", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1", "name": "GTMSessionFetcher-GTMSessionFetcher_Core_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98d9a0c31ef553ffd1644ea2f5fc087d46", "name": "GTMSessionFetcher-GTMSessionFetcher_Full_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f65e88472d384b1ba0888326befb3a8e", "name": "GTMSessionFetcher.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}