{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9824a4eaceabe55d85ae9c580f3499e2dc", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/FirebaseABTesting", "DEVELOPMENT_TEAM": "3H8G5N4B57", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "FirebaseABTesting", "INFOPLIST_FILE": "Target Support Files/FirebaseABTesting/ResourceBundle-FirebaseABTesting_Privacy-FirebaseABTesting-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "FirebaseABTesting_Privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98aea2d4470c95df61308819990b9e7b6b", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98310a30cdd67eeffc2ffec9924270046b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/FirebaseABTesting", "DEVELOPMENT_TEAM": "3H8G5N4B57", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "FirebaseABTesting", "INFOPLIST_FILE": "Target Support Files/FirebaseABTesting/ResourceBundle-FirebaseABTesting_Privacy-FirebaseABTesting-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "PRODUCT_NAME": "FirebaseABTesting_Privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98a71b5bcaf770100b32e1eee590ff033d", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98310a30cdd67eeffc2ffec9924270046b", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/FirebaseABTesting", "DEVELOPMENT_TEAM": "3H8G5N4B57", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "FirebaseABTesting", "INFOPLIST_FILE": "Target Support Files/FirebaseABTesting/ResourceBundle-FirebaseABTesting_Privacy-FirebaseABTesting-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "PRODUCT_NAME": "FirebaseABTesting_Privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98b12e31e3f610d962a8299744307f5381", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e986b74b1a05e6a992b6d3bb507189ae720", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98c52a3998bf68382dc3ac200f94077b5d", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ff8bc0dc7476b3d87e736f70b4481862", "guid": "bfdfe7dc352907fc980b868725387e98fb1a41bdd2444eabe1bcc2fa8ad28410"}], "guid": "bfdfe7dc352907fc980b868725387e98ba237d97b71fb888bcde6ff03b2440d6", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e98b9cce05bc25808e2e3952904a4034443", "name": "FirebaseABTesting-FirebaseABTesting_Privacy", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98a718a291ceac43da58780ce82d37524d", "name": "FirebaseABTesting_Privacy.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}