{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9850ce314c97a44911286df439c764e671", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9842aa8898052f43043aea84cb7a638df9", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982e0bf567a34e458a843a70df44133783", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980cd4e99b9e0573da5eb8bcd9a91a8586", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982e0bf567a34e458a843a70df44133783", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f27ede33fd2680a2207bdc44bebbdcdd", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982c4805a09310fa897f8ad0bf4291b743", "guid": "bfdfe7dc352907fc980b868725387e98bce5f1e361acdd7a6898e62f711403ee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d6de42ef543fa1f87a5049cf1ef3a70", "guid": "bfdfe7dc352907fc980b868725387e986bcbcdc8a1e9573eca638cc7b563e619"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d943058275ff1311cf0a3416b74e31e", "guid": "bfdfe7dc352907fc980b868725387e984b0a24affd5c0af4399336ce28c72911"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf81533efb345fea914ed80fe6a714e7", "guid": "bfdfe7dc352907fc980b868725387e98e5517b93e57554a4b3d7773acff682a6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2ba1ca792b9824b1407e41c192ee5b0", "guid": "bfdfe7dc352907fc980b868725387e987009e3b2cf3d4a7f9e962aefe08f471c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca7455f7f445ace38d6be4202b15f671", "guid": "bfdfe7dc352907fc980b868725387e988b139ccbd0b6435564796fd825a0f16d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9863ab4b9d523df1ffb8b6320eb2c534a2", "guid": "bfdfe7dc352907fc980b868725387e98a29388adb4d4ffd03344f4ca701b1277", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855b82b125e4a4029509fa5b510e09970", "guid": "bfdfe7dc352907fc980b868725387e9890f92bfb852f3014f10bbfdfc97e60f9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9812cde4f6afbf418cc922c47eaa33c255", "guid": "bfdfe7dc352907fc980b868725387e98479b81790fa636a0a06d23ea9f90881f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98488c7e1f8030e0af31546eb31e5723c3", "guid": "bfdfe7dc352907fc980b868725387e9893f7776410c98a200795b523f7a4ea93"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7374cc4c999a7932fec4d1a2d02b6b5", "guid": "bfdfe7dc352907fc980b868725387e980ef308d65b7a377d3961b313966dff4a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c7be7720c6bcad34a0c0e886a9253794", "guid": "bfdfe7dc352907fc980b868725387e982cecd031d95140a44f817b8b06f3b709"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878d5b17da66360989c3147e9dc995b2c", "guid": "bfdfe7dc352907fc980b868725387e9864211ddda54908d1445df650a13dc187", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986349bce1a77f871a46bb2b4600dad3c2", "guid": "bfdfe7dc352907fc980b868725387e98cba9f138bba8f2553f4fbbba784d0205"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b35f93854cc0eaf819134c0dc20d2e5", "guid": "bfdfe7dc352907fc980b868725387e98551bcc4d9002089b64cb948c188b8ed8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea272f177a53448b5aa6d2cbbb4dd5e4", "guid": "bfdfe7dc352907fc980b868725387e98a7fd69403359e50b10b910efc78a7abb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981eeadd0d98755d53bf060ca4b87226b3", "guid": "bfdfe7dc352907fc980b868725387e9830177b3b1285b6da98535cbd208c420f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7401b9b297cefde1afc5553874b023e", "guid": "bfdfe7dc352907fc980b868725387e98b8652043be96b4242ac84f3a690eff2f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9838424716a84e410052ae3b83a7eb297d", "guid": "bfdfe7dc352907fc980b868725387e98fc7c5fad49dfc486b0237127914af960"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9806c2b6a47069d345245854e4b5ddbd25", "guid": "bfdfe7dc352907fc980b868725387e98a189ba80c30bca87453341666ee03e2e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba3929989ab6a2b95c84cfb3c648742e", "guid": "bfdfe7dc352907fc980b868725387e98a227157baef6a48e0e3a69d9664ffc1f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e03f15d74966d892484803c06ab1a13", "guid": "bfdfe7dc352907fc980b868725387e9896e606ac812676f174f3f2a77e99a43b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986bae4f407c5accfd691dfe8aca68734e", "guid": "bfdfe7dc352907fc980b868725387e9897bca449a45259b4a2ecb12485b3098a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b4e508cf09cff19c5aa3a36959643b38", "guid": "bfdfe7dc352907fc980b868725387e98fc8b856924a7f25846c1430bb725dfd2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989da8026404d500c445cf44671464a7af", "guid": "bfdfe7dc352907fc980b868725387e98c3cf52c36e1aec4017c3518a8e60e48a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c0c51ec3bbb704442e39a2ee2d8095c", "guid": "bfdfe7dc352907fc980b868725387e984c80b3d88f41c3a0588d505ed54b9942"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98345653847db7b0f1c3396c49044659fe", "guid": "bfdfe7dc352907fc980b868725387e98710dd1baf8e9d137870ec1bbb7d3ff88"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d46ef8107fd86346ed5b5f5043fd4d78", "guid": "bfdfe7dc352907fc980b868725387e982a6d457d004e14c4b3429449e220fb6b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ddee23ed273cde8a5ce5e0a5bc7a730a", "guid": "bfdfe7dc352907fc980b868725387e98ca03aafb4ff411c1e717ba635304efd1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d696b660224859b7bb57641acc55aea2", "guid": "bfdfe7dc352907fc980b868725387e98aec2576ccf581e585d37c51ff36d3625"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986172b5d6a12818512271b9f512ae5abe", "guid": "bfdfe7dc352907fc980b868725387e98f6f8e89dc0878f25fa1ec6b2b5f4cea4"}], "guid": "bfdfe7dc352907fc980b868725387e98a73fe7ba0d23729a02271d0ac4e25967", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e0b1e584a384b137b5b965bfb4ba0468", "guid": "bfdfe7dc352907fc980b868725387e98acaa56be78bd77f31ba9132273bc523c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834febaea6cda908a26c59561a63ff8c4", "guid": "bfdfe7dc352907fc980b868725387e981385e5812fcf4fba86376000b5506e78"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e22c910fa48ef091a79f491aee170dd2", "guid": "bfdfe7dc352907fc980b868725387e98dd5397119c39a4e73d2e5eef34a92bc0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f58e1b5dfb6a08c3d5c0117110c3e5b", "guid": "bfdfe7dc352907fc980b868725387e9865fb6cb12f512af794b8ce515836fbc3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cec6f65941da7fbd197223ae118a025e", "guid": "bfdfe7dc352907fc980b868725387e981a2db70c8b9fd9d17c5b796881a1bf1f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9868bd368f81af3da6115d77e2cf38e732", "guid": "bfdfe7dc352907fc980b868725387e983e8acacc7c6e5f373460c48314d45ac0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ae8549893134ac9c470730aa31692ab", "guid": "bfdfe7dc352907fc980b868725387e98abc18bd2bb2184205ca7a1bf317283e9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98feb2d622782c9bf95708d93a0978a754", "guid": "bfdfe7dc352907fc980b868725387e989305ecde5d60d9c44d6b875086804241"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e3925cacbf73f46574ec0c21083aa85", "guid": "bfdfe7dc352907fc980b868725387e985140d872d7fc4063b7e6c9666d78e8bc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f0783024c663f39b109570e2c1c3858", "guid": "bfdfe7dc352907fc980b868725387e98580d8b3eb6374c21f44b74c3df0fdc43"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d37dfd76ef0f8fc8617a1c4ac261ac1", "guid": "bfdfe7dc352907fc980b868725387e98d7cbb5461937f3ece64dcf2c470d6aca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e80b9f1e9b54ca019e84717d7d2e971c", "guid": "bfdfe7dc352907fc980b868725387e98fc6e4e645c5da2db573b8b4ea43c0812"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1f5ff832f74b1d784a3917d6f9634d0", "guid": "bfdfe7dc352907fc980b868725387e981064f499b3af7b33f2e7152aeb449eef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b8facb5df7cb941d1a773284a39dca4d", "guid": "bfdfe7dc352907fc980b868725387e98262100731223286d533df54620fea637"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f0fe4bc9cdab5e967557ef76fd966dcc", "guid": "bfdfe7dc352907fc980b868725387e989c489085eb697b031f53994fadd2584b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985be17be6ce192db9f246eda89003c223", "guid": "bfdfe7dc352907fc980b868725387e98dc84ce02ac4dc911ac14c93fed3ba642"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f57c2c8df9448fd21897a3f5e0200c40", "guid": "bfdfe7dc352907fc980b868725387e980c99a23a14203b325f94825fd4a753f6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9870d098dcb4d70e85ff564e0784d235f0", "guid": "bfdfe7dc352907fc980b868725387e98a3c5c8cd3622533cc4d2ee2226228117"}], "guid": "bfdfe7dc352907fc980b868725387e98e4a1c60a61e8531fa475bbc191aa188c", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98374e3ec4dc398dcbdb656bf148272974", "guid": "bfdfe7dc352907fc980b868725387e981a3357b4b22b5fd5bd5044af1962be2e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98974e1232357fe39e8cbe1022d2c8bc9f", "guid": "bfdfe7dc352907fc980b868725387e98c7aa1abb88dd581e26c36dbc90e94957"}], "guid": "bfdfe7dc352907fc980b868725387e989f3f3c8f612d11aff75f148452fc8b52", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e982852e0c3b74dbf979e117e46077a2781", "targetReference": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc"}], "guid": "bfdfe7dc352907fc980b868725387e9827d632c54f3825b15eefb143db980e06", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc", "name": "FirebaseInstallations-FirebaseInstallations_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9860819b8e327bf41b291e92315614a812", "name": "FirebaseInstallations.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}