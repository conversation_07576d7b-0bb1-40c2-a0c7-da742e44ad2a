{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9824a4eaceabe55d85ae9c580f3499e2dc", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/FirebaseABTesting/FirebaseABTesting-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseABTesting/FirebaseABTesting.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseABTesting", "PRODUCT_NAME": "FirebaseABTesting", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9815cc819bbcc3301d02c13e493bae7efa", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98310a30cdd67eeffc2ffec9924270046b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/FirebaseABTesting/FirebaseABTesting-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseABTesting/FirebaseABTesting.modulemap", "PRODUCT_MODULE_NAME": "FirebaseABTesting", "PRODUCT_NAME": "FirebaseABTesting", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98bad4ec6546dc51cbd997bc8cad1eaab5", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98310a30cdd67eeffc2ffec9924270046b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/FirebaseABTesting/FirebaseABTesting-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseABTesting/FirebaseABTesting.modulemap", "PRODUCT_MODULE_NAME": "FirebaseABTesting", "PRODUCT_NAME": "FirebaseABTesting", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9892344c3d839c7f8eb72d9c055f4e4f20", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98936fdca9c87a3b218e86e7a9871cc410", "guid": "bfdfe7dc352907fc980b868725387e9899b01f766be6674cb8835632bee72833"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98497a5f3e79d6c25afe05d10817d34721", "guid": "bfdfe7dc352907fc980b868725387e98f09e7dd79c6e20ecfc55f7e51b84dac9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826a89153c199ed0ed1f0374733f9d7ac", "guid": "bfdfe7dc352907fc980b868725387e989c5a6bf330ce0486b2a2602c74a9eb87"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f200867424e02aa193f25aa728086d26", "guid": "bfdfe7dc352907fc980b868725387e98ca26e7cc22b1487a1ad5dfa60d69c4ea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833bb62402870ca84b42c4d932dbb7250", "guid": "bfdfe7dc352907fc980b868725387e98aa5d9bc135792279a9259f0b53a1e961"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980295ec50b1f8170d58691e315209cd8f", "guid": "bfdfe7dc352907fc980b868725387e98768c5757d37a6d0df9142ca337ee86ae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aceee992675fd44d05d41db791a9fc2c", "guid": "bfdfe7dc352907fc980b868725387e982bfed491d4a97276cd008f5c4ad8075d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ac32d76dbaa3bcadd3ee382b1506e69", "guid": "bfdfe7dc352907fc980b868725387e98fd1bba8ed9e870281e3c61daef9cd5db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9b721c4c26016b2f69a0adee57bdb8d", "guid": "bfdfe7dc352907fc980b868725387e98f6f7daafe0d61de25b2df1f3471d6c1a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b6a0920b77f296c2aabf02a6332e82d", "guid": "bfdfe7dc352907fc980b868725387e98d05972825f39e8f7dae117ee8dae7f9d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982bfd1efe33e5447b45967f4b77804239", "guid": "bfdfe7dc352907fc980b868725387e981c0b2b6981995e6f5138dd30c86bac32", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d42329e910958bbd3ad31b06c003310", "guid": "bfdfe7dc352907fc980b868725387e98b3b832b1dca2a96df64874660971c96a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980334d0899d9b386fdec282988be33f91", "guid": "bfdfe7dc352907fc980b868725387e98990dc0924adb67aa5fe0f8183263eafb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98740001300ecee523503699dcb53e23c4", "guid": "bfdfe7dc352907fc980b868725387e98eeb6979ad821013640fa056326de709f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bad3bd331d69e3964fbd5e94553d94f9", "guid": "bfdfe7dc352907fc980b868725387e9876c4ee34766e27553e5faf2055df1a9b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876f715f811bc52559b5880919331c101", "guid": "bfdfe7dc352907fc980b868725387e98cac09d5d47f1e1f3df87e18ba3285715"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983285c21faa6e5df99583dc701ac73b7c", "guid": "bfdfe7dc352907fc980b868725387e98ae3fe000add255792ff2086864c00d42"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987fa7bd323c80c6f96defb0a135715d4b", "guid": "bfdfe7dc352907fc980b868725387e9815e7466341caa08aa2adc28a9426c537"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1d1dfc3a68cf2aee33a445c5969fbd1", "guid": "bfdfe7dc352907fc980b868725387e987b6a128dccc993968c39c525080d5708", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9898d270dbd9b745175c749e00e3758e0a", "guid": "bfdfe7dc352907fc980b868725387e9805a30a3cc7a1de6a59972cf1f55d0a29"}], "guid": "bfdfe7dc352907fc980b868725387e981e400ceea7bcbc7d269b2dccbd816b68", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985b677903fe871de879a4dabd10e224a7", "guid": "bfdfe7dc352907fc980b868725387e985dfff3023f601c90ae72b1faad21ebc7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dcc2f54b1e2d5c1f0cf910fc6c47f1fc", "guid": "bfdfe7dc352907fc980b868725387e984216bb4ff6cf0e8ce06f080400f3641a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e523247397044540c60efa1241d51da5", "guid": "bfdfe7dc352907fc980b868725387e9802032b9b8d5894e01cc4b476271723e2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a98debf585a48389349ad2f75d51a364", "guid": "bfdfe7dc352907fc980b868725387e980083e5444a4384723d28ddbb51931ee6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98255e7369e6d23f4330b3d9ec547d4d46", "guid": "bfdfe7dc352907fc980b868725387e98b9a7f1665d474bef28719d140eb11dab"}], "guid": "bfdfe7dc352907fc980b868725387e98fb1d9a5038e87d08f687f68a6bc1b866", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98374e3ec4dc398dcbdb656bf148272974", "guid": "bfdfe7dc352907fc980b868725387e98ba9a701918cdf6c549a999a2c43e1244"}], "guid": "bfdfe7dc352907fc980b868725387e98ca48bae1409f351c64b61edfc11e1821", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e982fd2a9134ddba2f8333f2eb16dc27aab", "targetReference": "bfdfe7dc352907fc980b868725387e98b9cce05bc25808e2e3952904a4034443"}], "guid": "bfdfe7dc352907fc980b868725387e98d568ec0a710c4c3ee2c08d336675a01c", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98b9cce05bc25808e2e3952904a4034443", "name": "FirebaseABTesting-FirebaseABTesting_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}], "guid": "bfdfe7dc352907fc980b868725387e984d1b80eb520d7ec9828b3cb4e14dcb65", "name": "FirebaseABTesting", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98388ecc0b6beee3823c42c78ba6025714", "name": "FirebaseABTesting.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}