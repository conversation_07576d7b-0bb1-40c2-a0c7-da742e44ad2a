{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98bd1e28b93b59a9758da02048322693a0", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/FirebaseRemoteConfig/FirebaseRemoteConfig-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseRemoteConfig/FirebaseRemoteConfig.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseRemoteConfig", "PRODUCT_NAME": "FirebaseRemoteConfig", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9809949230e8d1e6d1e7575a0fa1714da7", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980ff655952f831f05dc139f813418a970", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/FirebaseRemoteConfig/FirebaseRemoteConfig-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseRemoteConfig/FirebaseRemoteConfig.modulemap", "PRODUCT_MODULE_NAME": "FirebaseRemoteConfig", "PRODUCT_NAME": "FirebaseRemoteConfig", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c619b4c14133d03caf7d241601986e72", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980ff655952f831f05dc139f813418a970", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/FirebaseRemoteConfig/FirebaseRemoteConfig-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseRemoteConfig/FirebaseRemoteConfig.modulemap", "PRODUCT_MODULE_NAME": "FirebaseRemoteConfig", "PRODUCT_NAME": "FirebaseRemoteConfig", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985d7481a30734c85410e311d5747c79cd", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e5f4ca1819ff5436a86390a4825116e6", "guid": "bfdfe7dc352907fc980b868725387e981f99b0a57345539c5fa7c6df71bd298c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a5d6c320ab10e6aa49af2dc83a4e901", "guid": "bfdfe7dc352907fc980b868725387e98ce30e60fe6e151f97aa43bfa1166c4fe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef2549dab7871211111df098e84876ba", "guid": "bfdfe7dc352907fc980b868725387e98ba826108ce992db9c3cad3d554acf26b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889d2da8b2b8b16020f55f8c197933c50", "guid": "bfdfe7dc352907fc980b868725387e9851209a2d06b12f709ca4a23f51882643"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ada54e436e34d8fe88a49e55b2dc9e30", "guid": "bfdfe7dc352907fc980b868725387e9840e0466e3c74c112fb07323353b95bed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8d2710c5b4530edd95971d3227ed22d", "guid": "bfdfe7dc352907fc980b868725387e984f0a3e10086c718cdfb39ba3590de8d5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846afb5a6a820575fe92779cbf51dc8d2", "guid": "bfdfe7dc352907fc980b868725387e98bc95721aabeea0190eeee69d769d822e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d2064cb40c9692a47bb276770791807", "guid": "bfdfe7dc352907fc980b868725387e98e1cc5c807b5edd90496ec044daa7fdfa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9829e8d6b3ece130dd2e5e178e8c8b5a0f", "guid": "bfdfe7dc352907fc980b868725387e98833f8fa790b7b9db3d0e1a32626c2a22"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba3f5b3b050cada6e474a739306233ff", "guid": "bfdfe7dc352907fc980b868725387e98f5ce45092f4bfd80d321f4f767124c65"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f22c6b123f018ebba72d32a6379765c", "guid": "bfdfe7dc352907fc980b868725387e98b4a09231e705b2786e401ae7a8b20304", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98438493dae28a63b5471d946e2ec1e89c", "guid": "bfdfe7dc352907fc980b868725387e9877b0b2e90618f12c2c1b0d15a4339ab3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c7c4e4010d13f4f8885b18eb4cb9f24", "guid": "bfdfe7dc352907fc980b868725387e98cb370d3315841c49038543219d6a18aa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986871ae9cc6a2a79dc4e164641d65d7cd", "guid": "bfdfe7dc352907fc980b868725387e98dd93aaa104a0ac87b5b440a354d624ef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987115557ff2e3ccda88b0bbc785c10415", "guid": "bfdfe7dc352907fc980b868725387e98de65bea7aa9f3224a562df0195ee28f1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873b36ffbe553b130773a4c895944eca7", "guid": "bfdfe7dc352907fc980b868725387e98fd6b0b6f0ae0e067be51dffc9fba9898"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e916130ed43e9535a816ec96ee0a07a0", "guid": "bfdfe7dc352907fc980b868725387e98f439dc6f9e0b5b649b3f4340ef250c44"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d887062c002a74eb6da934e49443a289", "guid": "bfdfe7dc352907fc980b868725387e9857e171303852eb37a1f8871590fce087", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d610867352efbffd54a4a971a6ebe098", "guid": "bfdfe7dc352907fc980b868725387e984fe8e39f25d4c54eae735c506666fb04"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f718977b8bbb1857c86889e9f9efbc6", "guid": "bfdfe7dc352907fc980b868725387e98e91554641b73d960bb0b9e09630bc7be"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4fd5a4c7625fe3b1ee8fc4aae586ac7", "guid": "bfdfe7dc352907fc980b868725387e985e3b60b3ba69a2ec957859eaa3266447"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ae99b609b6231ef3aadb76015fde9bc", "guid": "bfdfe7dc352907fc980b868725387e98e4974b1bbc19aa1c86762f9f8ac825a3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980bf31735859fe5e71f82d0d0f8f704a9", "guid": "bfdfe7dc352907fc980b868725387e9835e67b9961d95ce1a350b429c2478d04"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832be3c19b8c61a14b965aadd146c1f7e", "guid": "bfdfe7dc352907fc980b868725387e9891325c22cc33b3809233865de090a22c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804cbb074dde4e7f97a770754552e13b1", "guid": "bfdfe7dc352907fc980b868725387e98537d470eafbe062b362f7330fd71913a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5b46c0536653a4783d914a089155012", "guid": "bfdfe7dc352907fc980b868725387e984c993e18b540acf84bf81ae63699ca37"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cca62e85b962d69887979a13eb29fe40", "guid": "bfdfe7dc352907fc980b868725387e988f6633e783808f51e8fe7f4bd1a994ab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9895654e766ff07753874fe8839e12cba2", "guid": "bfdfe7dc352907fc980b868725387e984223cf8363fa2a4dbe3801151684719e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9895d469f20d0280fe1a6563edcb8db51d", "guid": "bfdfe7dc352907fc980b868725387e980f01b178987a15cd2115e8a99ad1e022"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982fc40b3bc0a367f7ed6a5948a4fd467f", "guid": "bfdfe7dc352907fc980b868725387e9846a6870205a9a31e71baa1e89f0a2f4b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981edf32087b83a2850e09d8ddf8d045c8", "guid": "bfdfe7dc352907fc980b868725387e98fee64c819f9d7a1e03eac1e315fa092a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b19f83c92674e4288ea569a0ea79322e", "guid": "bfdfe7dc352907fc980b868725387e9826c4f0aa23695752ecd1e4080086da9a"}], "guid": "bfdfe7dc352907fc980b868725387e9842be3ddc90893dc5304cfefe6eb43d8e", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988807b8820135e77e7cfb81e74472e86c", "guid": "bfdfe7dc352907fc980b868725387e98c06768aba97462f7c3023676c99f95fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840bb7fd680b446f40d406a2f20350bda", "guid": "bfdfe7dc352907fc980b868725387e98603d0537156517234829fb24060a6a58"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98333dec3b2b7efd814de0d89ac10d17f5", "guid": "bfdfe7dc352907fc980b868725387e9830c9adec45c301e4e823b0bf9f902bba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e20d44ddd371a78c293efd7dadb3e8bf", "guid": "bfdfe7dc352907fc980b868725387e9891329e881d2d5d2fe7e9ec59f3f26abd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b466d26a3161c2e5615994747078f46", "guid": "bfdfe7dc352907fc980b868725387e984d0355a0e6b5d7604b02f2c025fbfefd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f8eb31c679f757d65bfae5b74bf4913b", "guid": "bfdfe7dc352907fc980b868725387e98fd00f3ae79678b02cc540ed560280d47"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f0d535d969527a3856b9857a15e15477", "guid": "bfdfe7dc352907fc980b868725387e9838d30040ff234635ef2bdf697d16e80f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ea642d902d28bc715b76088595fbaf0", "guid": "bfdfe7dc352907fc980b868725387e98ec98e643e36bd765dad919c28d10a7ae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98135096a8ca1f76decbd78721f46e55f3", "guid": "bfdfe7dc352907fc980b868725387e989197d83b2176886a1dd2c698df80e64b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984cf841c3969b53227972ac0aa3a65fa9", "guid": "bfdfe7dc352907fc980b868725387e987b0dd261fbd39e9977d90e2606f2b082"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed2af66c050df68eeef03eb3fc003c17", "guid": "bfdfe7dc352907fc980b868725387e9837e646020d4c9c31f3825424992388a1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858812f6bf27e47e43d310525495f13d8", "guid": "bfdfe7dc352907fc980b868725387e986c0a3e09fcf98f0621f4178d92d1e5c3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec4dc01e3278802fb42af3ad0481540a", "guid": "bfdfe7dc352907fc980b868725387e98039d927afdd43a76e0cb55c5738bbafe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981429a629589b79762986b6599b824cee", "guid": "bfdfe7dc352907fc980b868725387e9853852b180c76b4bdbbe542f381b5844b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d1db0bd56442e43580b02e5ce8de231", "guid": "bfdfe7dc352907fc980b868725387e980167230ac497f49414782e7a58728cfd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d79434f52415e80143ddb5380f6c7d9", "guid": "bfdfe7dc352907fc980b868725387e9804aaf37f6e60f57fd65da38cf37d2d1c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fff826eba846be12b4b77fbdf7fd5687", "guid": "bfdfe7dc352907fc980b868725387e98aea59a3067ce86875c6fc420c348d5d0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d6c43c2b144a93cc0c1e2f1153f596f", "guid": "bfdfe7dc352907fc980b868725387e987bd0ff79cd6199ad1ceeca46bae4d61d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aacd998fb554fbe06eace2e6a13b7781", "guid": "bfdfe7dc352907fc980b868725387e98f49f2391c256929dac41d3f7090975e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8ddf1e847405176c4b1ec871b2c7f40", "guid": "bfdfe7dc352907fc980b868725387e986e66dbf9c074705c5c703e335fd9b3c7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9800a856a2efa2de6dd27e58941ccfd446", "guid": "bfdfe7dc352907fc980b868725387e98b93d24bdd4b7103c16ec09ee06b9c8f2"}], "guid": "bfdfe7dc352907fc980b868725387e98df255ce09382285402ebaebcd6de21c9", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98374e3ec4dc398dcbdb656bf148272974", "guid": "bfdfe7dc352907fc980b868725387e98ef2002259365e01ec168a882367af130"}], "guid": "bfdfe7dc352907fc980b868725387e9848dc66f6cd7db5c770bbfd74e0d25962", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98d0117f9106723f0d0743bc544d2ac675", "targetReference": "bfdfe7dc352907fc980b868725387e98012330f90a37c9d15a390c6b73b6dbca"}], "guid": "bfdfe7dc352907fc980b868725387e9825b27c5d0755f5ff015ffcf650ee3ebf", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e984d1b80eb520d7ec9828b3cb4e14dcb65", "name": "FirebaseABTesting"}, {"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations"}, {"guid": "bfdfe7dc352907fc980b868725387e98012330f90a37c9d15a390c6b73b6dbca", "name": "FirebaseRemoteConfig-FirebaseRemoteConfig_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e984b1e8e5f67fa144e5e34058df6e2f50c", "name": "FirebaseRemoteConfigInterop"}, {"guid": "bfdfe7dc352907fc980b868725387e982a62e2c60acb8d344a6411a0606a13d4", "name": "FirebaseSharedSwift"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98928855ae8620d13300183deed96c33a1", "name": "FirebaseRemoteConfig", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e980b80126605cba44506bfa90fbbd69742", "name": "FirebaseRemoteConfig.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}