{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ccdc91adc89ac09dc7fbfc8a4eeb2510", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/GTMAppAuth/GTMAppAuth-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMAppAuth/GTMAppAuth.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GTMAppAuth", "PRODUCT_NAME": "GTMAppAuth", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "4.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9899a2a8820f9f4545faba130bba100190", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987b644a6d810bea8eb50b573191f7693c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/GTMAppAuth/GTMAppAuth-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMAppAuth/GTMAppAuth.modulemap", "PRODUCT_MODULE_NAME": "GTMAppAuth", "PRODUCT_NAME": "GTMAppAuth", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "4.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982f68b1c8e9713bd883fa7effbcd883c0", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987b644a6d810bea8eb50b573191f7693c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/GTMAppAuth/GTMAppAuth-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMAppAuth/GTMAppAuth.modulemap", "PRODUCT_MODULE_NAME": "GTMAppAuth", "PRODUCT_NAME": "GTMAppAuth", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "4.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98172caa0c7e86abd796c6109d91632681", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98bcaba451459398eb1fdb6199583aeee6", "guid": "bfdfe7dc352907fc980b868725387e985ee2a367bdb1c5b295db5e823bfbce34", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9892dbfbf2e4a3557ea3bb5d5b8037e796", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b4874d7df4db1a1cb588c41496e164ae", "guid": "bfdfe7dc352907fc980b868725387e98585eb2064e6e118d224f8ee485083789"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985804dfc1cf92afb3296b63bebd42af49", "guid": "bfdfe7dc352907fc980b868725387e981a10cc837d23e4084efc3d2a40f4312a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f34d412ca1c938d441741d0fbd737636", "guid": "bfdfe7dc352907fc980b868725387e980b55a2c09917549ea9e3a0f611666751"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983816a688581f87b0d24aaff391808729", "guid": "bfdfe7dc352907fc980b868725387e984b7198769465f600e29464c8ca978560"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db9e15fc244eaa1a2b98505cbc61ad56", "guid": "bfdfe7dc352907fc980b868725387e985619fc3153e2b508e1332607b5f4b889"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c2f928835404dd4d4ad5e119e5f177d6", "guid": "bfdfe7dc352907fc980b868725387e98f06e17de926c181258c28b6e76d3ad08"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2ce0cc8c1d92262402a432358864678", "guid": "bfdfe7dc352907fc980b868725387e98810813adf642fda0e5c73edbc8ede36e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b1851d4be0b26c543e6690e5416fd66", "guid": "bfdfe7dc352907fc980b868725387e98a6910260f09365fa21fa584da6b2f4f5"}], "guid": "bfdfe7dc352907fc980b868725387e988343f40fcd7f4d8e8798b73c10af6714", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98374e3ec4dc398dcbdb656bf148272974", "guid": "bfdfe7dc352907fc980b868725387e98465734d9dddd3b94519683fad57f2ad3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98974e1232357fe39e8cbe1022d2c8bc9f", "guid": "bfdfe7dc352907fc980b868725387e985b40cd1f827b7f9a157f054c72acf05c"}], "guid": "bfdfe7dc352907fc980b868725387e986075d94b693d9af108675649b328a684", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98548f3848f15e7ea52c6ed25ee60c8c8e", "targetReference": "bfdfe7dc352907fc980b868725387e9865af479ae97320e284a27cf831d212b3"}], "guid": "bfdfe7dc352907fc980b868725387e986eb35e750b6206866ba7365315695732", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98758cc842172da540ffb591e63e38dc1e", "name": "AppAuth"}, {"guid": "bfdfe7dc352907fc980b868725387e9865af479ae97320e284a27cf831d212b3", "name": "GTMAppAuth-GTMAppAuth_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher"}], "guid": "bfdfe7dc352907fc980b868725387e980be6c76e7b3dde057d7e3e6ad61f30d4", "name": "GTMAppAuth", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98855fb84830a2ff40ce73a17fc283f650", "name": "GTMAppAuth.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}