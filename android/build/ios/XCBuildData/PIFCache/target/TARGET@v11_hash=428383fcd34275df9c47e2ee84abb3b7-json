{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e2c058b28eafca87aa9947d60bc63508", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98577364055e6f0c2751e74f0694f105d7", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986c220435a378a672fe95bdbb20c859e5", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9842457b24cb70758e33ae90926df7e2be", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986c220435a378a672fe95bdbb20c859e5", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98fc610a7a8e6bcc93898ded2b910b710f", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9815850a1b63ba6e6f7a4fad0d0943d7ca", "guid": "bfdfe7dc352907fc980b868725387e988863331f81b523725d945cb594de604f", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98f0d3a553f54a3ec06727b47892b8863d", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98013f995ef63793692bf1c8aa193ff473", "guid": "bfdfe7dc352907fc980b868725387e982da257514e66284f37b07b60edb2f399"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f3b01b4e3d5aea0ac7143b651fa357a2", "guid": "bfdfe7dc352907fc980b868725387e98381b33c29ca78939b7fe39d6bc7921af"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98add97fda9f08c055b453fef2d0c83e44", "guid": "bfdfe7dc352907fc980b868725387e986fc5c86378fd496efe15f784dfcd0787"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987267bdbf886a6d7208223f78f2f83181", "guid": "bfdfe7dc352907fc980b868725387e98b93ccbd9fa191c015468c3b79605fa90"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98919417f21fd8ed9b5d094a31568e8a4e", "guid": "bfdfe7dc352907fc980b868725387e983aade2985b339347f0775ff52eaaaf44"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cbc39f1a4bb79d0457d6b3ef1e8c1892", "guid": "bfdfe7dc352907fc980b868725387e98c4ede3fcbc486a24e90d51b211869782"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1af9e7b34d8b92090021fca34b0b158", "guid": "bfdfe7dc352907fc980b868725387e983c8fc12544f499fa7d53650b76a515d1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9866443eb7dc3b27d61c0e93ba3aab057c", "guid": "bfdfe7dc352907fc980b868725387e98112c3de6864d6f4fd5731524ce0658ee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988687e41732c0cf26717f8c7c9cffbb46", "guid": "bfdfe7dc352907fc980b868725387e9878d98a4353b5df8881ec9309c356536e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cfd1dd03679adc037afb7c571a0198c1", "guid": "bfdfe7dc352907fc980b868725387e9862cc530c6518edab11e91c27a1f54d62"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc93d57d292fe4e4e4a7b8a658a21446", "guid": "bfdfe7dc352907fc980b868725387e98780cd1b3c6c82c1d52bdbe6843165b97"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9837ae2729963b735cf191039d4620dca3", "guid": "bfdfe7dc352907fc980b868725387e986d44dc16e2be8bf128987dff73bef270"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9890044c23af241e331f2bcdcab12dafe6", "guid": "bfdfe7dc352907fc980b868725387e98c329b55c064d2c38d20fb942ff0a5869"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9886600a081537ded399a6359cc06df6f7", "guid": "bfdfe7dc352907fc980b868725387e9835d42a1f365b192b1abffe6dd84423f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987011445fb221ab119eac8a31b231cb70", "guid": "bfdfe7dc352907fc980b868725387e98c37850b6bd437720a94c354215ef2faa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ef58b54eb1687d15ee0a8789726c818", "guid": "bfdfe7dc352907fc980b868725387e98008f0d45b77c870652b13395fa22ad4d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9853c638149b48eaf8e82a6d8d43f72462", "guid": "bfdfe7dc352907fc980b868725387e987d3d3895ec15c465e9b2b0d75c8b081b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eaf5512c95360400a8713044abbfde26", "guid": "bfdfe7dc352907fc980b868725387e986791c4b864e7f740f49e5c1279b1fab2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852feb2297285cc2cf5b7b8a0e52da604", "guid": "bfdfe7dc352907fc980b868725387e9872fe24f7f6b149c9cc620db6b894af87"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98919bef7d258ec6ff3331bd4a33efeeb9", "guid": "bfdfe7dc352907fc980b868725387e98d05ac6eee2e819d2dcd272e37f3bd9b4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7df7acea400cc4577eb483e4db953d8", "guid": "bfdfe7dc352907fc980b868725387e982963c3116390b32cbd4369f9d746ab84"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a844b56ee1ac88591bb26b5a4972c27", "guid": "bfdfe7dc352907fc980b868725387e9899b9b487cb1422273afdf0181490f3f0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893a9406164fab5feb9c9f0f08c304909", "guid": "bfdfe7dc352907fc980b868725387e98bcb35a8cca1a314397d271ccc17b6813"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e5837286034dc507a411d273b715bb4", "guid": "bfdfe7dc352907fc980b868725387e98201748c318a57698fe415addbb9bc368"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a6d6a8c047686fb266bb7406a5a41565", "guid": "bfdfe7dc352907fc980b868725387e98d168af0ad8c7a46d390a28f345f6a260"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987369dc236eec761465d2f1c1d8a88333", "guid": "bfdfe7dc352907fc980b868725387e982389c7f52da36e781291cd1085329d33"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e608418abbf8abd7f91029ffbe248eae", "guid": "bfdfe7dc352907fc980b868725387e98225625c8535c48709d4de5f1aa295294"}], "guid": "bfdfe7dc352907fc980b868725387e98a8266317077d017a17c15be2b6b3c270", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ff11a75580b21e28fced6cdcfefbcd9a", "guid": "bfdfe7dc352907fc980b868725387e9812838916befbc73c3e6fb0daf3361ec3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c013ab9c2ba15ba9c8ee07489e57504", "guid": "bfdfe7dc352907fc980b868725387e989de0cc4087fde1ed0d90eb0eb85dbd45"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98374e3ec4dc398dcbdb656bf148272974", "guid": "bfdfe7dc352907fc980b868725387e984a573ceed16fe39f3dba23499321e40d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ecfcd2f8da23e3b68f8b9272a708c37", "guid": "bfdfe7dc352907fc980b868725387e98bac5e4383cd3fee68b3ef6bafc47ab7b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d63ad33f357e9346c3b5060dfe50403", "guid": "bfdfe7dc352907fc980b868725387e98135a8a0f0943712a362d00038c07e7ae"}], "guid": "bfdfe7dc352907fc980b868725387e98f4d08f177513a781572a1e7cdcc9b827", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e982e3926400c060179ddeba270a6bb8565", "targetReference": "bfdfe7dc352907fc980b868725387e98d3f65728b12dd217475d1283ee417937"}], "guid": "bfdfe7dc352907fc980b868725387e98f6fc18725257f0224c7bf144377aea9d", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98d3f65728b12dd217475d1283ee417937", "name": "DKPhotoGallery-DKPhotoGallery"}, {"guid": "bfdfe7dc352907fc980b868725387e98c46180aea4e87057640961e6db37df0d", "name": "SDWebImage"}, {"guid": "bfdfe7dc352907fc980b868725387e9872eabefc63c14dfe52fb0c95ad90294e", "name": "SwiftyGif"}], "guid": "bfdfe7dc352907fc980b868725387e989d0a1858a86fd6e6731ed20f88a1e515", "name": "DKPhotoGallery", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986e90c628ccd44af657bee5ff4af2f692", "name": "DKPhotoGallery.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}