{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98bc89d68d7fee520264d63fb767e4e15f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ba07b86cc750992c6630e8133bda679b", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9858909759b67b78f713a7749442450443", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984d13ab57ca5b2bf9054450c7e19f6660", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9858909759b67b78f713a7749442450443", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f762e3e3ecb128d8393f628d7520868c", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9855d024548b864e5c6502733898e8b9dd", "guid": "bfdfe7dc352907fc980b868725387e98d8f59e3274a192d75ab8068a6d3edbe4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d136b1efb8d92e906e75964f9147225", "guid": "bfdfe7dc352907fc980b868725387e98d538bebd43c30d41934f7f4e79ae1265", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a97fc0023fbe08531d0ee624064aedf5", "guid": "bfdfe7dc352907fc980b868725387e9860631e480431ecc02c9ea0b949d0ff13"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a202f2089b72c45074a15586d12e44d8", "guid": "bfdfe7dc352907fc980b868725387e98b4b75e2f2579e03bc648152d99d12470"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb99c986c24342bd100bd4d07e4ed558", "guid": "bfdfe7dc352907fc980b868725387e98d8757245171c680f7f140e67ab50686f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98625747f83b78763c4dd22b30e0ba20b4", "guid": "bfdfe7dc352907fc980b868725387e980354cafe1e963ecc48c5b51b4f9e1afb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98074a2ffd5ce188247c2bd3bfa778f576", "guid": "bfdfe7dc352907fc980b868725387e98f755b9cf12ca339e69bb693f423e6b39"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1eafe6e22f08f3cc6c623e28d44fa3d", "guid": "bfdfe7dc352907fc980b868725387e9894c3c3da519fece95e315117d7f5ba0d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828c8807b26b2ab400714ac425356770c", "guid": "bfdfe7dc352907fc980b868725387e980cbb8a624867032a86ccaa308015e124", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989df07e9ee0ed60a733f241edb975b4ca", "guid": "bfdfe7dc352907fc980b868725387e9871af008875f048075395c94a3de17d9e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f9798f2b96a761785b5710dd5d811fd9", "guid": "bfdfe7dc352907fc980b868725387e98ed88b4b014f36cf89ae98383c1a5737c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889c9ec96c439ca9356e0a15c650d116e", "guid": "bfdfe7dc352907fc980b868725387e981b7827f0db8942a8aa8d4cf8942d39f7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988466c3ec6b91b05a7102d4b0eae64506", "guid": "bfdfe7dc352907fc980b868725387e987787c4183705f95241436400f76bd337"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984c7fbc2a181314b7914229d0bb2f5802", "guid": "bfdfe7dc352907fc980b868725387e98425a1b4ad7cb06f5a550a4dca7a99688"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe65863afde183ae0f7e8417b5fe4b60", "guid": "bfdfe7dc352907fc980b868725387e9860c2952ed81f7b050864e5966836ec9d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e38a97a76b1a948a23728761dfe2dc36", "guid": "bfdfe7dc352907fc980b868725387e98b264c0d03eb2c7c4940d6d2b2e961e9d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9853d620f4682f9ebec2667d1fc3d104fb", "guid": "bfdfe7dc352907fc980b868725387e98e2c62779a1443e092175492d7081db04"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987663dc0371c035f19342cab32b12013f", "guid": "bfdfe7dc352907fc980b868725387e98f7b9c55e38769f71a76c09b349fab021", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982681a2dc97ccf3cee3669ec9f3d321ea", "guid": "bfdfe7dc352907fc980b868725387e9808b43adec298897d51f9b092343261d7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9800b20891da2546e1de1f7a558b9b7a7a", "guid": "bfdfe7dc352907fc980b868725387e98d038a5fe4871c878a86663b1bb24ec82"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987cfb0582ee3670e3d02c0d935f920f21", "guid": "bfdfe7dc352907fc980b868725387e9841b184cebff5d1c70aabd6ddfe325b9e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984be51ca913ee37ac9578b5adfb1371c0", "guid": "bfdfe7dc352907fc980b868725387e986896c07a7ac20c2a3176a29dffac7b94"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9847f8aace3c7f436cdfa61127cb9747ee", "guid": "bfdfe7dc352907fc980b868725387e98f76fadb0327f3a9808eafbe74934a69b", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9884bcf7a5db50d940ea1d25714dc4ce7b", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98473dd4ffda4364f69a3bf1325331e2be", "guid": "bfdfe7dc352907fc980b868725387e98088b890e46c482410bba1070c4a6def5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9887e7930f06b97f8f3c4ea5522ca61333", "guid": "bfdfe7dc352907fc980b868725387e981cb0d6a93a978237bf6eaa63cac1b85d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb8b407b6ba63aa0f85d63cd882c9396", "guid": "bfdfe7dc352907fc980b868725387e98018412b9c24fe2ae002c6b70cdcdb1c2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9890f708046856f41bd48f25909e498d9e", "guid": "bfdfe7dc352907fc980b868725387e9870e9c71128eac4f09cbbaac6e5e57a12"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b809bb581fb889861f6e8cc82d5ed0ae", "guid": "bfdfe7dc352907fc980b868725387e981d13b1602be9ed02bfc30c73ba046274"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825d3cf43d6f6bf4e207010f84ff086d1", "guid": "bfdfe7dc352907fc980b868725387e98f36540747f89b01d9b71ddd6b9db9927"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec34442f1df8c12325513b6c7fc329f0", "guid": "bfdfe7dc352907fc980b868725387e9825cb5243f0687cb07e08c9340727c698"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9802556cf954520546d49b6b23f83c6c05", "guid": "bfdfe7dc352907fc980b868725387e989a174735013674785ce0f7cc2c7c3aa3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986cd3494e3ccbfbb29f0f1ef15712b9d7", "guid": "bfdfe7dc352907fc980b868725387e9840871df0720360749b81a976fb0509c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da7f2f32431f20217b9cebcde4422b6f", "guid": "bfdfe7dc352907fc980b868725387e9865e73f0ac4aa94dd751be7bcc3fe91e5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ec2cfb0278de1a882cc05bd4eaceb06", "guid": "bfdfe7dc352907fc980b868725387e9868d79f88d7ae39a700c0493dfe180ee8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981dd5273a980f0c1dc8d4901d2c80664b", "guid": "bfdfe7dc352907fc980b868725387e9895fb30e5771544c952292463b56cb06f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851be7579e2d5ff8d0eb90cb6884ffa22", "guid": "bfdfe7dc352907fc980b868725387e989d8dc8028e3f3a66564374fb5053ebb0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e4bd0917aac061a5d4e886b091354921", "guid": "bfdfe7dc352907fc980b868725387e9832f9343a315f577d7995dbd272e548f4"}], "guid": "bfdfe7dc352907fc980b868725387e982bf55d71d514e63e34cb3b6f070735c7", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98374e3ec4dc398dcbdb656bf148272974", "guid": "bfdfe7dc352907fc980b868725387e984a6c3d8396b0a5b9aec56be80132f3f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d63ad33f357e9346c3b5060dfe50403", "guid": "bfdfe7dc352907fc980b868725387e9876701fb977ec89d5aae82f6916eefc80"}], "guid": "bfdfe7dc352907fc980b868725387e9849d4d914703ad372341fe9f007936ee3", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9825916b279ab298a1cac03c70827f7fe5", "targetReference": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14"}], "guid": "bfdfe7dc352907fc980b868725387e98cb8769a65c911724f8e7a9e6ce28d1bb", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}